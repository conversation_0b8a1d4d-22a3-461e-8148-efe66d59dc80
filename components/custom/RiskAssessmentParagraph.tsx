import { FC } from 'react';

interface RiskAssessmentParagraphProps {
  title?: string;
  justification: string;
  riskLevel: 'severe' | 'high' | 'medium' | 'low';
}

const riskColors = {
  severe: {
    bg: 'bg-red-700/10',
    title: 'text-red-900',
    text: 'text-red-800',
  },
  high: {
    bg: 'bg-red-50',
    title: 'text-red-800',
    text: 'text-red-700',
  },
  medium: {
    bg: 'bg-orange-50',
    title: 'text-orange-800',
    text: 'text-orange-700',
  },
  low: {
    bg: 'bg-green-50',
    title: 'text-green-800',
    text: 'text-green-700',
  },
};

export const RiskAssessmentParagraph: FC<RiskAssessmentParagraphProps> = ({
  title = 'Risk Assessment',
  justification,
  riskLevel
}) => {
  const colors = riskColors[riskLevel] || riskColors.low;
  return (
    <div className={`mt-4 p-4 rounded-lg ${colors.bg}`}>
      <h4 className={`font-medium mb-2 ${colors.title}`}>{title}</h4>
      <p className={`${colors.text}`}>{justification}</p>
    </div>
  );
};
