"use client"

import * as React from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { SidebarFooter } from "@/components/ui/sidebar"
import { LogOut, Settings, User } from "lucide-react"
import { useAuthStore } from '@/app/store/authentication/authStore';
import { useRouter } from 'next/navigation';

interface UserProfileFooterProps {
  user: {
    name: string
    email: string
    avatarUrl?: string
  }
}

export function UserProfileFooter({ user }: UserProfileFooterProps) {
  const { logout, user: authUser } = useAuthStore();
  const router = useRouter();

  const handleLogout = () => {
    logout();
    router.push('/auth');
  };

  const initials = user.name
    .split(" ")
    .map(n => n[0])
    .join("")
    .toUpperCase()

  const ProfileButton = React.forwardRef<HTMLDivElement, React.ComponentProps<"div">>(
    (props, ref) => (
      <div
        ref={ref}
        className="flex items-center gap-2 p-2 cursor-pointer hover:bg-accent rounded-md transition-colors"
        {...props}
      >
        <Avatar className="h-8 w-8">
          <AvatarImage src={user.avatarUrl} alt={authUser?.username} />
          <AvatarFallback>{initials}</AvatarFallback>
        </Avatar>
        <div className="flex flex-col flex-1 min-w-0 group-data-[collapsible=icon]:hidden">
          <span className="text-sm font-medium leading-none truncate">
            {authUser?.username}
          </span>
          <span className="text-xs text-muted-foreground truncate">
            {authUser?.email}
          </span>
        </div>
      </div>
    )
  )

  const MenuHeader = () => (
    <div className="flex items-center gap-2 px-2 py-1">
      <Avatar className="w-6 h-6">
        <AvatarImage src={user?.avatarUrl} alt={authUser?.username} />
        <AvatarFallback>{initials}</AvatarFallback>
      </Avatar>
      <div className="flex flex-col flex-1 min-w-0">
        <span className="text-sm font-medium leading-none truncate">
          {authUser?.username}
        </span>
        <span className="text-xs text-muted-foreground truncate">
          {authUser?.email}
        </span>
      </div>
    </div>
  )

  return (
    <SidebarFooter className="border-t">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <ProfileButton />
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-64">
          <MenuHeader />
          <DropdownMenuSeparator />
          <DropdownMenuItem>
            <User className="w-4 h-4 mr-2" />
            View Profile
          </DropdownMenuItem>
          <DropdownMenuItem>
            <Settings className="w-4 h-4 mr-2" />
            Settings
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem 
            className="text-red-600 focus:text-red-600"
            onClick={handleLogout}
          >
            <LogOut className="w-4 h-4 mr-2" />
            Sign Out
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarFooter>
  )
}