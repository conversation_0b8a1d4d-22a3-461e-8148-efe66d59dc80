import { useEffect, useRef, useState } from 'react';
import CodeMirror from '@uiw/react-codemirror';
import { sql } from '@codemirror/lang-sql';
import { vscodeLight } from '@uiw/codemirror-theme-vscode';
import { format } from 'sql-formatter';

interface CodeViewProps {
  code: string;
  isEditable?: boolean;
  onChange?: (value: string) => void;
}

export function CodeView({ code: initialCode, isEditable = false, onChange }: CodeViewProps) {
  const [code, setCode] = useState(initialCode);
  const editorRef = useRef<any>(null);

  useEffect(() => {
    setCode(initialCode);
  }, [initialCode]);

  const formatSQL = () => {
    try {
      const formatted = format(code, { 
        language: 'postgresql',
        keywordCase: 'upper',
        indentStyle: 'standard',
        linesBetweenQueries: 2
      });
      setCode(formatted);
      if (onChange) onChange(formatted);
    } catch (error) {
      console.error('SQL formatting failed:', error);
    }
  };

  return (
    <div className="overflow-auto max-h-[300px] rounded-lg shadow-lg border border-gray-700 bg-[#1e1e1e]">
      {isEditable && (
        <div className="px-3 py-2 border-b border-gray-700 flex justify-end">
          <button 
            onClick={formatSQL}
            className="px-2 py-1 text-xs bg-blue-600 hover:bg-blue-700 rounded text-white"
          >
            Format SQL
          </button>
        </div>
      )}
      <CodeMirror
        value={code}
        height="300px"
        editable={isEditable}
        extensions={[sql()]}
        theme={vscodeLight}
        onChange={(value) => {
          setCode(value);
          if (onChange) onChange(value);
        }}
        basicSetup={{
          lineNumbers: true,
          highlightActiveLineGutter: true,
          foldGutter: true,
          syntaxHighlighting: true,
          bracketMatching: true,
          autocompletion: true,
        }}
        className="text-white"
        style={{ 
          fontSize: '12px',
          fontFamily: 'JetBrains Mono, monospace'
        }}
      />
    </div>
  );
} 