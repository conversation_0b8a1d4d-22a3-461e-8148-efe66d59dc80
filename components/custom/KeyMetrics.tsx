import { FC } from 'react';
import * as LucideIcons from 'lucide-react';
import { LucideIcon } from 'lucide-react';
import { motion } from 'framer-motion';
import { TabSectionHeading } from '@/components/custom/TabSectionHeading';
import { CollapseButton } from '@/components/custom/CollapseButton';
import { StatCard } from '@/app/pages/Merchant/MerchantInvestigation/Overview/components/StatCard';
import { getStatCards } from '@/app/pages/Merchant/MerchantInvestigation/Overview/utils/statCardUtils';
import { KeyMetric } from '@/app/types';
import { EmptyState } from '@/app/pages/Merchant/MerchantInvestigation/Overview/components/EmptyState';
import { Card } from '@/components/ui/card';

interface SimpleMetric {
  label: string;
  value: string | number;
  icon: string;
}

interface KeyMetricsProps {
  keyMetricList?: {
    key_metrics: Array<SimpleMetric | KeyMetric>;
  };
  hardcodedMetrics?: Array<SimpleMetric | KeyMetric>;
  isMetricsExpanded: boolean;
  setIsMetricsExpanded: (value: boolean) => void;
  title?: string;
  icon?: LucideIcons.LucideIcon;
  iconColorClass?: string;
  showHeader?: boolean;
}

export const KeyMetrics: FC<KeyMetricsProps> = ({ 
  keyMetricList, 
  hardcodedMetrics,
  isMetricsExpanded, 
  setIsMetricsExpanded,
  title = "Key Metrics",
  icon = LucideIcons.Activity,
  iconColorClass = "text-blue-600",
  showHeader = true
}) => {
  // Use either backend data or hardcoded data
  const metrics = keyMetricList?.key_metrics || hardcodedMetrics || [];
  const hasData = metrics.length > 0;

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  const renderMetrics = () => {
    if (metrics.length === 0) return null;

    // If first metric is KeyMetric type, use getStatCards
    if ('total_amount' in metrics[0]) {
      return getStatCards(metrics[0] as KeyMetric)
        .slice(0, isMetricsExpanded ? undefined : 4)
        .map((stat, index) => (
          <StatCard key={index} {...stat} />
        ));
    }

    // Otherwise render SimpleMetrics
    return metrics
      .slice(0, isMetricsExpanded ? undefined : 4)
      .map((metric, index) => {
        const simpleMetric = metric as SimpleMetric;
        const iconName = simpleMetric?.icon?.match(/<(\w+)/)?.[1];
        const IconComponent = iconName ? (LucideIcons[iconName as keyof typeof LucideIcons] as LucideIcon) : LucideIcons.Activity;
        const colorClass = simpleMetric?.icon?.match(/text-(\w+)-500/)?.[1] || 'blue';
        
        return (
          <StatCard 
            key={index} 
            title={simpleMetric?.label || 'N/A'}
            value={simpleMetric?.value?.toString() || '0'}
            icon={<IconComponent className={`h-5 w-5 text-${colorClass}-500`} />}
          />
        );
      });
  };

  const shouldShowCollapse = () => {
    if (metrics.length === 0) return false;
    if ('total_amount' in metrics[0]) {
      return getStatCards(metrics[0] as KeyMetric).length > 4;
    }
    return metrics.length > 4;
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-2"
    >
      {showHeader && (
        <TabSectionHeading icon={icon} iconColorClass={iconColorClass}>
          {title}
        </TabSectionHeading>
      )}
      <motion.div variants={containerVariants}>
        {hasData ? (
          <>
            <motion.div
              initial="hidden"
              animate="visible"
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
            >
              {renderMetrics()}
            </motion.div>
            {shouldShowCollapse() && (
              <motion.div 
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
                className="flex justify-center mt-4"
              >
                <CollapseButton
                  isExpanded={isMetricsExpanded}
                  onClick={() => setIsMetricsExpanded(!isMetricsExpanded)}
                />
              </motion.div>
            )}
          </>
        ) : (
          <Card className="p-4">
            <EmptyState message="No Key Metrics available" />
          </Card>
        )}
      </motion.div>
    </motion.div>
  );
};
