import { FC, ReactNode, useState } from 'react';
import { CollapseButton } from './CollapseButton';

interface Column {
  key: string;
  header: string;
}

interface CustomTableViewProps {
  title?: string;
  titleNumber?: number;
  columns: Column[];
  data: Record<string, any>[];
  className?: string;
  initialRowLimit?: number;
  isExpanded?: boolean;
  setIsExpanded?: (value: boolean) => void;
}

export const CustomTableView: FC<CustomTableViewProps> = ({
  title,
  titleNumber,
  columns,
  data,
  className = '',
  initialRowLimit = 3,
  isExpanded = false,
  setIsExpanded,
}) => {
  // If no external control is provided, manage state internally
  const [isExpandedInternal, setIsExpandedInternal] = useState(false);
  
  // Use either external or internal state
  const expanded = setIsExpanded ? isExpanded : isExpandedInternal;
  const toggleExpanded = () => {
    if (setIsExpanded) {
      setIsExpanded(!isExpanded);
    } else {
      setIsExpandedInternal(!isExpandedInternal);
    }
  };

  // Show all rows if expanded, otherwise limit to initialRowLimit
  const visibleData = expanded ? data : data.slice(0, initialRowLimit);
  const hasMoreRows = data.length > initialRowLimit;

  return (
    <div className={`${className} ${title ? 'pt-2' : ''}`}>
      {title && (
        <h3 className="text-md font-semibold flex items-center mb-2">
          {title}
        </h3>
      )}
      <div className="border rounded-md overflow-hidden shadow-sm">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {columns.map((column) => (
                  <th 
                    key={column.key} 
                    className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider"
                  >
                    {column.header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {visibleData.length > 0 ? (
                visibleData.map((row, rowIndex) => (
                  <tr 
                    key={rowIndex} 
                    className="hover:bg-gray-50 transition-colors duration-150 ease-in-out"
                  >
                    {columns.map((column) => (
                      <td 
                        key={`${rowIndex}-${column.key}`} 
                        className="px-4 py-3 text-sm text-gray-700 whitespace-nowrap"
                      >
                        {row[column.key]}
                      </td>
                    ))}
                  </tr>
                ))
              ) : (
                <tr>
                  <td 
                    colSpan={columns.length} 
                    className="px-4 py-4 text-sm text-center text-gray-500"
                  >
                    No data available
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
      
      {hasMoreRows && (
        <div className="flex justify-center mt-4">
          <CollapseButton 
            isExpanded={expanded} 
            onClick={toggleExpanded} 
          />
        </div>
      )}
    </div>
  );
};
