import { ChatHistoryItem, ChatDetailedMessage } from '../types';
import { API } from './axios';
import { useMerchantIdStore } from '../store/merchant/merchantIdStore';
import { v4 as uuidv4 } from 'uuid';

export const chatService = {
    getActiveChatIds: async (): Promise<{chat_id: string, has_visualization: boolean, created_at: string, chat_title: string, merchant_id: string, user_id: string, visualization_id: string | null}[]> => {
        // Get merchant ID from the store
        const merchantState = useMerchantIdStore.getState();
        const merchantId = merchantState.selectedMerchantId;
        
        if (!merchantId) {
            console.error('No merchant ID available for fetching active chats');
            return [];
        }
        
        // Use the new API endpoint
        try {
            const response = await API.get(`/api/v1/chat2/${merchantId}/active-chats`);
            return response.data.data;
        } catch (error) {
            console.error('Error fetching active chats:', error);
            return [];
        }
    }, 

    getNewChatId: async (): Promise<string> => {
        const response = await API.get(`/api/v1/chat/new-chat`);
        return response.data.chat_id;
    },

    createNewChat: async (userId: string, merchantId: string, hasVisualization: boolean = false, visualizationId: string | null = null): Promise<string> => {
        console.log('Creating new chat with:', { userId, merchantId, hasVisualization, visualizationId });
        const requestBody = {
            user_id: userId,
            merchant_id: merchantId,
            has_visualization: hasVisualization,
            visualization_id: visualizationId
        };
        
        try {
            const response = await API.post(`/api/v1/chat2/new-chat`, requestBody);
            console.log('New chat created with API response:', response);
            
            // Extract the chat ID from the response
            if (response.data && response.data.data && response.data.data.chat_id) {
                // Format: { success: true, message: "New chat created", data: { chat_id: "uuid" } }
                console.log('Extracted chat ID from data.data.chat_id:', response.data.data.chat_id);
                return response.data.data.chat_id;
            } else if (response.data && typeof response.data === 'string') {
                // If the response is directly the chat ID as a string
                console.log('Extracted chat ID from string response:', response.data);
                return response.data;
            } else if (response.data && response.data.chat_id) {
                // Format: { chat_id: "uuid" }
                console.log('Extracted chat ID from data.chat_id:', response.data.chat_id);
                return response.data.chat_id;
            } else {
                console.error('Unexpected response format:', response.data);
                throw new Error('Unexpected response format from API');
            }
        } catch (error) {
            console.error('Error creating new chat:', error);
            throw error;
        }
    },

    sendChatMessage: async (chatId: string, userId: string, merchantId: string, message: string, onMessage: (data: any) => void, onError: (error: any) => void) => {
        try {
            if (!chatId) {
                throw new Error('Chat ID is required');
            }
            
            console.log('Sending message to chat with ID:', chatId);
            console.log('Message details:', { userId, merchantId, message });
            
            // Generate UUIDs for message_id and reply_message_id
            const messageId = uuidv4();
            const replyMessageId = uuidv4();
            
            const response = await fetch(
                `${process.env.NEXT_PUBLIC_API_URL}api/v1/chat2/initiate-chat`,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('auth') ? JSON.parse(localStorage.getItem('auth') || '{}').accessToken : ''}`
                    },
                    body: JSON.stringify({ 
                        chat_id: chatId,
                        message: message,
                        message_id: messageId,
                        reply_message_id: replyMessageId
                    })
                }
            );

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`HTTP error! status: ${response.status}, body:`, errorText);
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            // Start polling for updates using the reply_message_id
            console.log('Chat initiated, polling for updates using reply_message_id:', replyMessageId);
            return chatService.pollMessageUpdates(replyMessageId, onMessage, onError);
        } catch (error) {
            console.error('Error sending chat message:', error);
            onError(error);
            return () => {};
        }
    },
    
    pollMessageUpdates: (messageId: string, onMessage: (data: any) => void, onError: (error: any) => void) => {
        console.log('Starting to poll for message updates with ID:', messageId);
        
        let intervalId: NodeJS.Timeout;
        let isCompleted = false;
        let allSteps: any[] = []; // Store all steps we've seen
        let failedAttempts = 0; // Track failed API requests
        
        const fetchUpdates = async () => {
            try {
                const response = await fetch(
                    `${process.env.NEXT_PUBLIC_API_URL}api/v1/chat2/${messageId}/updates`,
                    {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                            'Authorization': `Bearer ${localStorage.getItem('auth') ? JSON.parse(localStorage.getItem('auth') || '{}').accessToken : ''}`
                        }
                    }
                );

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error(`HTTP error! status: ${response.status}, body:`, errorText);
                    
                    // Increment failed attempts
                    failedAttempts++;
                    
                    // If we've failed more than 3 times, add an error step
                    if (failedAttempts >= 3) {
                        const errorStep = {
                            stage: 'Error',
                            step: `Server returned an error: ${response.status}. Please try again.`,
                            timestamp: Date.now()
                        };
                        
                        // Only add if we haven't already added a similar error
                        if (!allSteps.some(s => s.stage === 'Error')) {
                            allSteps.push(errorStep);
                            
                            // Send the steps to the UI
                            onMessage({
                                type: 'thinking',
                                steps: allSteps
                            });
                            
                            // Stop polling after showing error
                            isCompleted = true;
                            clearInterval(intervalId);
                        }
                    }
                    
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                // Reset failed attempts counter on successful response
                failedAttempts = 0;

                const data = await response.json();
                console.log('Message update response:', data);
                
                if (data.success && data.data) {
                    // Update thinking status
                    if (data.data.steps && Array.isArray(data.data.steps)) {
                        // Log each step for debugging
                        data.data.steps.forEach((step: any, index: number) => {
                            console.log(`Step ${index + 1}:`, step);
                        });
                        
                        // Format stage names for better UI display
                        const formattedSteps = data.data.steps.map((step: any) => {
                            // Create a formatted copy of the step
                            const formattedStep = { ...step };
                            
                            // Format stage names for better display
                            if (formattedStep.stage) {
                                // Convert snake_case to Title Case and remove underscores
                                formattedStep.stage = formattedStep.stage
                                    .split('_')
                                    .map((word: string) => 
                                        word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
                                    )
                                    .join(' ');
                            }
                            
                            return formattedStep;
                        });
                        
                        // Merge with existing steps, keeping all steps we've seen
                        for (const step of formattedSteps) {
                            // Check if this is a new step we haven't seen
                            const existingStepIndex = allSteps.findIndex(s => 
                                s.stage === step.stage && s.step === step.step
                            );
                            
                            if (existingStepIndex === -1) {
                                // New step, add it to our collection
                                allSteps.push(step);
                                console.log(`New step added: ${step.stage} - ${step.step}`);
                            }
                        }
                        
                        // Update state with all thinking steps for UI display
                        onMessage({
                            type: 'thinking',
                            steps: allSteps
                        });
                    }
                    
                    // If processing is complete, return the final message
                    if (data.data.current_status === 'completed' && data.data.message) {
                        console.log('Processing completed with message:', data.data.message);
                        
                        // Send final message
                        onMessage({
                            type: 'assistant',
                            is_final: true,
                            content: data.data.message.message
                        });
                        
                        // Stop polling
                        isCompleted = true;
                        clearInterval(intervalId);
                    }
                } else if (data.error) {
                    // Handle API error response
                    console.error('API returned error:', data.error);
                    
                    // Add an error step
                    const errorStep = {
                        stage: 'Error',
                        step: data.error || 'An error occurred while processing your request',
                        timestamp: Date.now()
                    };
                    
                    if (!allSteps.some(s => s.stage === 'Error')) {
                        allSteps.push(errorStep);
                        
                        // Send the steps to the UI
                        onMessage({
                            type: 'thinking',
                            steps: allSteps
                        });
                    }
                }
            } catch (error) {
                console.error('Error polling for message updates:', error);
                
                // Don't trigger error callback on every failed request, only if we're stopping
                if (failedAttempts >= 3) {
                    onError(error);
                    clearInterval(intervalId);
                }
            }
        };
        
        // Start time for timeout tracking
        const startTime = Date.now();
        
        // Poll every 1 second
        intervalId = setInterval(fetchUpdates, 1000);
        
        // Call once immediately
        fetchUpdates();
        
        // Return a cleanup function
        return () => {
            if (intervalId) {
                clearInterval(intervalId);
            }
        };
    },

    getNewVisualizationChatId: async (): Promise<string> => {
        const response = await API.get(`/api/v1/chat/new-visualization-chat`);
        return response.data.chat_id;
    },

    getActiveChatHistory: async (chatId: string): Promise<ChatHistoryItem[]> => {
        try {
            console.log(`Fetching chat history for chat ID: ${chatId}`);
            const response = await API.get(`/api/v1/chat2/${chatId}/chat-history`);
            
            if (response.data && response.data.success && Array.isArray(response.data.data)) {
                // Transform the new API format to match the old one's expected structure
                return response.data.data.map((msg: any) => ({
                    created_at: msg.created_at,
                    message: msg.message?.message || msg.message,
                    writer: msg.sender === 'user' ? 'user' : 'assistant'
                }));
            }
            return [];
        } catch (error) {
            console.error("Error fetching chat history:", error);
            return [];
        }
    },

    getChatVisualizationStatus: async (chatId: string): Promise<boolean> => {
        const response = await API.get(`/api/v1/chat/chat/${chatId}/has-visualization`);
        return response.data.has_visualization;
    },

    getChatDetailedHistory: async (chatId: string): Promise<ChatDetailedMessage[]> => {
        try {
            console.log(`Fetching chat history for chat ID: ${chatId}`);
            const response = await API.get(`/api/v1/chat2/${chatId}/chat-history`);
            console.log('Chat history API response:', response.data);
            
            if (response.data && response.data.success && Array.isArray(response.data.data)) {
                // Log each message as we process it
                response.data.data.forEach((msg: any, index: number) => {
                    console.log(`Message ${index}:`, msg);
                    console.log(`Message ${index} content:`, msg.message);
                });
                
                // Simply return the data - it's already in the right format
                return response.data.data;
            } else if (response.data && response.data.data) {
                // If data exists but is not an array, wrap it
                console.warn('Chat history response data is not an array:', response.data.data);
                return Array.isArray(response.data.data) ? response.data.data : [response.data.data];
            }
            console.error('Unexpected response format:', response.data);
            return [];
        } catch (error) {
            console.error('Error fetching detailed chat history:', error);
            throw error;
        }
    },
};