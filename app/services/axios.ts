import axios from 'axios';

export const API = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// Add a request interceptor
API.interceptors.request.use((config) => {
  // Get token from localStorage
  const auth = localStorage.getItem('auth');
  if (auth) {
    const { accessToken } = JSON.parse(auth);
    if (accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }
  }
  return config;
});

// Add a response interceptor
API.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Clear auth data and redirect to login
      localStorage.removeItem('auth');
      window.location.href = '/auth';
    }
    return Promise.reject(error);
  }
);