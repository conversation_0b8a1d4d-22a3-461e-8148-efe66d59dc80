import { API } from './axios';

interface CustomerResponse {
  id: string;
  name: string;
}

export const customerService = {
  getCustomerList: async () => {
    const response = await API.get<CustomerResponse[]>('/api/v1/customers/customerIDs');
    return response.data.map(customer => ({
      value: customer.id,
      label: customer.name
    }));
  },
  
  getCustomerRedFlags: async (customerId: string) => {
    const response = await API.get(`/api/v1/customer-red-flags/customer/${customerId}`);
    return response.data;
  }
}; 