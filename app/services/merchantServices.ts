import { API } from './axios';
import { MerchantItemType, MerchantProfileType } from '@/app/types';

export const merchantService = {
  // Get merchant list for dropdown
  getMerchantList: async (): Promise<MerchantItemType[]> => {
    const response = await API.get('/api/v1/merchants/merchantIDs');
    return response.data;
  },

  // Get detailed merchant data
  getMerchantDetails: async (merchantId: string): Promise<MerchantProfileType> => {
    const response = await API.get(`/api/v1/merchants/${merchantId}`);
    return response.data;
  },

  // from here MerchantInvestigation services
  // Get merchant investigations
  // getMerchantInvestigations: async (merchantId: string) => {
  //   const response = await API.get(`/api/v1/merchants/${merchantId}/investigations`);
  //   return response.data;
  // },

  // Add more endpoints as needed
  getMerchantCompliance: async (merchantId: string) => {
    const response = await API.get(`/api/v1/merchants/${merchantId}/compliance`);
    return response.data;
  },

  getMerchantFinancials: async (merchantId: string) => {
    const response = await API.get(`/api/v1/merchants/${merchantId}/financials`);
    return response.data;
  },

  getMerchantKeyMetrics: async (merchantId: string) => {
    const response = await API.get(`/api/v1/merchants/${merchantId}/key-metrics`);
    return response.data;
  },

  getMerchantTransactionMetrics: async (merchantId: string) => {
    const response = await API.get(`/api/v1/merchants/${merchantId}/transaction-metrics`);
    return response.data;
  },

  // get risk assessment
  getMerchantRiskAssessment: async (merchantId: string) => {
    try {
      const response = await API.get(`/api/v1/merchants/${merchantId}/risk-assessment`);
      return response.data;
    } catch (error: any) {
      console.error('Risk assessment fetch error:', error);
      // Create a more informative error object that includes any details from the server
      const errorMessage = error.response?.data?.detail || error.message || 'Error fetching risk assessment';
      
      // Rethrow a more informative error that can be handled by consuming components
      const enhancedError = new Error(errorMessage);
      // Add the original error information
      (enhancedError as any).originalError = error;
      (enhancedError as any).status = error.response?.status;
      
      throw enhancedError;
    }
  },

  getMerchantSummary: async (merchantId: string) => {
    const response = await API.get(`/api/v1/merchants/${merchantId}/summary`);
    return response.data;
  },

  // get merchant red flags
  getMerchantRedFlags: async (merchantId: string) => {
    const response = await API.get(`/api/v1/merchant-red-flags/merchant/${merchantId}`);
    return response.data;
  },

  getMerchantNetwork: async (merchantId: string) => {
    const response = await API.get(`/api/v1/merchants/${merchantId}/linkages`);
    return response.data;
  },

  getMerchantDigitalInformation: async (merchantId: string) => {
    const response = await API.get(`/api/v1/merchants/${merchantId}/digital-information`);
    return response.data;
  },

  // from here Activity services
  getMerchantEventTimeline: async (merchantId: string) => {
    const response = await API.get(`/api/v1/merchants/${merchantId}/timeline`);
    return response.data;
  },

  getDocumentsUploaded: async (merchantId: string) => {
    const response = await API.get(`/api/v1/merchants/${merchantId}/documents-uploaded`);
    return response.data;
  },

  getMerchantPaymentChannels: async (merchantId: string) => {
    const response = await API.get(`/api/v1/merchants/${merchantId}/payment-channels`);
    return response.data;
  },

  getMerchantCommunications: async (merchantId: string) => {
    const response = await API.get(`/api/v1/merchants/${merchantId}/communications`);
    return response.data;
  },

  getMerchantTransactions: async (merchantId: string) => {
    const response = await API.get(`/api/v1/merchants/${merchantId}/transactions`);
    return response.data;
  },

  getMerchantPayouts: async (merchantId: string) => {
    const response = await API.get(`/api/v1/merchants/${merchantId}/payouts`);
    return response.data;
  },

  // External insights data for insolvency analysis
  getMerchantExternalData: async (merchantId: string) => {
    const response = await API.get(`/api/v1/credit-dashboard/${merchantId}/getExternalData`);
    return response.data;
  },
  
  // Get audit report insights data for insolvency analysis
  getMerchantAuditReportInsights: async (merchantId: string) => {
    const response = await API.get(`/api/v1/credit-dashboard/${merchantId}/getFlagsFromAuditorDisclosures`);
    return response.data;
  },

  // Get annual report insights data for insolvency analysis
  getMerchantAnnualReportInsights: async (merchantId: string) => {
    const response = await API.get(`/api/v1/credit-dashboard/${merchantId}/getAnnualReportInsights`);
    return response.data;
  },
  
  // Get financial table data for insolvency analysis
  getMerchantFinancialTable: async (merchantId: string) => {
    const response = await API.get(`/api/v1/credit-dashboard/${merchantId}/financialsTable`);
    return response.data;
  },
};
