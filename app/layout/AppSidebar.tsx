"use client"

import * as React from "react"
import Image from "next/image"
import {
  Sidebar,
  SidebarContent as <PERSON>barContentRoot,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenuButton,
  SidebarProvider,
  SidebarTrigger,
  SidebarHeader,
  SidebarSeparator,
  useSidebar,
} from "@/components/ui/sidebar"
import {
  LineChart,
  Sparkles,
  Footprints,
  Microscope,
  ChevronsLeft,
  ChevronsRight,
  Store,
  BookOpen,
  Brain,
  Bell,
  ListTodo,
  Folder,
  UserCheck,
  MessageCircle,
  Clock,
  FileText,
  AlertTriangle
} from "lucide-react"
import dynamic from 'next/dynamic'
import { cn } from "@/lib/utils"
import MerchantProfile from "../pages/Merchant/MerchantProfile/MerchantProfile"
import MerchantActivity from "../pages/Merchant/MerchantActivity/MerchantActivity"
import MerchantInsolvency from "../pages/Merchant/MerchantInsolvency/MerchantInsolvency"
import RulesPage from "../pages/Strategy/Red Flags/RedFlagsPage"
import SandboxPage from "../pages/Strategy/Sandbox/SandboxPage"
import { Montserrat } from 'next/font/google'
import CxInvestigationInsights from "../pages/Customer/CustomerInvestigation"
import { UserProfileFooter } from "@/components/custom/UserProfileFooter"
import { useWorkspaceStore } from '@/app/store/workspace/workspaceStore';
import InvestigationInsights from "../pages/Merchant/MerchantInvestigation/InvestigationInsights"
import InvestigationHub from "../pages/CaseManagement/InvestigationHub/InvestigationHub"
import QueueManager from "@/app/pages/CaseManagement/QueueManager/QueueManager"
import ChatHistory from "@/app/pages/Modus Agent/ChatHistory/ChatHistory"
import Dashboards from "@/app/pages/Modus Agent/Dashboards/Dashboards"
import { useActiveContextStore } from '@/app/store/activeContextStore';
import { BlankMerchantState } from "../pages/Merchant/BlankMerchantState"
import { BlankCaseState } from "../pages/CaseManagement/BlankCaseState"
import CustomerInvestigation from "../pages/Customer/CustomerInvestigation"

const montserrat = Montserrat({
  subsets: ['latin'],
  weight: ['800'], // 800 is ExtraBold
})



export const sidebarGroups = [
  {
    label: "Merchant",
    items: [
      {
        label: "Investigation",
        component: InvestigationInsights,
        icon: Microscope,
      },
      {
        label: "Insolvency",
        component: MerchantInsolvency,
        icon: AlertTriangle,
      },
      {
        label: "Activity",
        component: MerchantActivity,
        icon: Footprints,
      },
      {
        label: "Profile",
        component: MerchantProfile,
        icon: Store,
      }
    ],
  }, 
  {
    label: "Case Management",
    items: [
      {
        label: "Queue Manager",
        component: QueueManager,
        icon: ListTodo,
      },
      {
        label: "Investigation Hub",
        component: InvestigationHub,
        icon: Folder,
      }
    ],
  },
  {
    label: "Modus Agent",
    items: [
      {
        label: "Chat History",
        component: ChatHistory,
        icon: MessageCircle,
      },
      {
        label: "Dashboards",
        component: Dashboards,
        icon: LineChart,
      },
    ],
  },
  {
    label: "Strategy",
    items: [
      {
        label: "Red Flags",
        component: RulesPage,
        icon: BookOpen,
      },
      {
        label: "Sandbox",
        component: SandboxPage,
        icon: Brain,
      },
    ],
  },
  {
    label: "Customer", 
    items: [
      {
        label: "Investigation",
        component: CustomerInvestigation,
        icon: Microscope,
      }
    ],
  },
  {
    label: "Report Gen",
    items: [
      {
        label: "selecteditems",
        component: dynamic(() => import('@/app/pages/ReportGeneration/ReportGeneration'), { ssr: false }),
        icon: FileText,
      }
    ]
  },
  {
    label: "Admin",
    items: [
      {
        label: "Job Schedule",
        component: dynamic(() => import('@/app/pages/Admin/Admin'), { ssr: false }),
        icon: Clock,
      }
    ]
  }
]

const SidebarContents = () => {
  const { setActiveComponent, setActiveNavigation, activeNavigation } = useWorkspaceStore()
  const { state } = useSidebar()
  const { activeContexts } = useActiveContextStore()

  React.useEffect(() => {
    // Only proceed if there's an active merchant
    if (activeContexts.merchant) {
      // Check if we're already on a merchant page
      const isOnMerchantPage = activeNavigation?.group === "Merchant";
      
      if (!isOnMerchantPage) {
        // If not on a merchant page, default to Investigation
        const defaultGroup = sidebarGroups.find(g => g.label === "Merchant")
        const defaultItem = defaultGroup?.items.find(i => i.label === "Investigation")
        
        if (defaultGroup && defaultItem) {
          setActiveComponent(defaultItem.component);
          setActiveNavigation({ group: defaultGroup.label, item: defaultItem.label });
        }
      } else {
        // We're on a merchant page, find the current page's component
        const currentGroup = sidebarGroups.find(g => g.label === activeNavigation.group);
        const currentItem = currentGroup?.items.find(i => i.label === activeNavigation.item);
        
        if (currentGroup && currentItem && 'component' in currentItem) {
          // Keep the same component but update the merchant context
          setActiveComponent(currentItem.component);
          setActiveNavigation({ group: currentGroup.label, item: currentItem.label });
        }
      }
    } else if (activeNavigation?.group === "Merchant") {
      // If we're on a merchant page and merchant is cleared, show blank state for current page
      const currentGroup = sidebarGroups.find(g => g.label === activeNavigation.group);
      const currentItem = currentGroup?.items.find(i => i.label === activeNavigation.item);
      
      if (currentGroup && currentItem && 'component' in currentItem) {
        setActiveComponent(BlankMerchantState);
        setActiveNavigation({ group: currentGroup.label, item: currentItem.label });
      }
    }
  }, [activeContexts.merchant]);

  const handleItemClick = React.useCallback((group: string, item: any) => {
    if ('component' in item) {
      const Component = item.component;
      // Only update if the component or navigation actually changed
      const store = useWorkspaceStore.getState();
      const currentNav = store.activeNavigation;
      
      if (
        currentNav?.group !== group || 
        currentNav?.item !== item.label
      ) {
        // Reset any active tab when changing navigation
        store.setActiveTab(null, null);
        
        // Determine which component to render based on group and context
        let ComponentToRender;
        
        if (group === "Merchant" && !activeContexts.merchant) {
          ComponentToRender = BlankMerchantState;
        } else if (item.label === "Investigation Hub" && !activeContexts.case) {
          ComponentToRender = BlankCaseState;
        } else {
          ComponentToRender = Component;
        }
        
        setActiveComponent(ComponentToRender);
        setActiveNavigation({ group, item: item.label });
      }
    }
  }, [activeContexts]);

  return (
    <Sidebar variant="sidebar" collapsible="icon">
      <SidebarHeader className="h-14 relative">
        <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
          <div className={cn(
            "transition-all duration-200",
            "group-data-[collapsible=icon]:opacity-0"
          )}>
            <span className={cn("text-lg text-[#00285B]", montserrat.className)}>modus ai</span>
          </div>
        </div>
        <div className="absolute right-2 top-1/2 -translate-y-1/2">
          <SidebarTrigger>
            {state === "collapsed" ? (
              <ChevronsRight className="h-4 w-4" />
            ) : (
              <ChevronsLeft className="h-4 w-4" />
            )}
          </SidebarTrigger>
        </div>
      </SidebarHeader>
      <SidebarSeparator />
      <SidebarContentRoot>
        {sidebarGroups.map((group) => (
          <SidebarGroup key={group.label}>
            <SidebarGroupLabel>{group.label}</SidebarGroupLabel>
            {group.items.map((item) => {
              const isActive = activeNavigation?.group === group.label && activeNavigation?.item === item.label
              return (
                <SidebarMenuButton
                  key={item.label}
                  icon={item.icon}
                  tooltip={item.label}
                  onClick={() => handleItemClick(group.label, item)}
                  className={cn(
                    isActive && "bg-blue-100/80 text-blue-600 font-medium hover:bg-blue-200/80",
                    "transition-colors"
                  )}
                >
                  {item.label}
                </SidebarMenuButton>
              )
            })}
          </SidebarGroup>
        ))}
      </SidebarContentRoot>
      <UserProfileFooter 
        user={{
          name: "John Doe",
          email: "<EMAIL>",
          // avatarUrl: "/path/to/avatar.jpg" // Optional
        }}
      />
    </Sidebar>
  )
}

export function AppSidebar() {
  return (
    <SidebarProvider defaultOpen={true}>
      <SidebarContents />
    </SidebarProvider>
  )
}