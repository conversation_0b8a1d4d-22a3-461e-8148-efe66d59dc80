"use client"

import { useWorkspace } from "@/app/layout/Workspace/WorkspaceContext"
import { ChevronRight, RefreshCw } from "lucide-react"
import { ActiveContext } from "./ActiveContext/ActiveContext"
import { useEffect } from "react"

export function AppTopbar() {
  const { activeNavigation, activeTab, activeTabLabel, refreshActiveTab, setActiveTab } = useWorkspace()

  // Reset tab label when navigation changes
  useEffect(() => {
    // When navigation changes, we should reset any active tab information
    // to avoid showing stale data from previous sections
    if (activeNavigation) {
      setActiveTab(null, null);
    }
  }, [activeNavigation?.group, activeNavigation?.item, setActiveTab]);

  const handleRefresh = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (activeTab) {
      refreshActiveTab()
    }
  }

  return (
    <header className="border-b bg-white h-14 flex items-center px-6 justify-between">
      <div className="flex items-center gap-4">
        <h1 className="flex items-center text-sm font-medium">
          <span className="text-muted-foreground hover:text-foreground transition-colors">
            {activeNavigation?.group}
          </span>
          <ChevronRight className="mx-1 h-4 w-4 text-muted-foreground" />
          <span className="text-muted-foreground">
            {activeNavigation?.item}
          </span>
          {activeTabLabel && (
            <>
              <ChevronRight className="mx-1 h-4 w-4 text-muted-foreground" />
              <span className="text-blue-600">
                {activeTabLabel}
              </span>
              <button
                onClick={handleRefresh}
                className="ml-1.5 p-1 hover:bg-gray-100 rounded-full transition-colors"
              >
                <RefreshCw className="h-3.5 w-3.5 text-muted-foreground hover:text-blue-600" />
              </button>
            </>
          )}
        </h1>
      </div>

      <ActiveContext />
    </header>
  )
}