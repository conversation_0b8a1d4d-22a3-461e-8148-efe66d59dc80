"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { InvestigationGPTInput } from "./InvestigationGPTInput"
import { InvestigationGPTSuggestions } from "./InvestigationGPTSuggestions"
import { InvestigationGPTChat } from "./InvestigationGPTChat"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Send, Plus } from "lucide-react"
import { useState, useEffect } from 'react'
import { useArtifactStore } from "@/app/store/artifact/artifactStore"
import { useChatStore } from "@/app/store/chat/chatStore"
import { MessageMode } from "@/app/types"
import { CreateChatDialog } from "./CreateChatDialog"
import { usePromptsStore } from "@/app/store/prompts/promptsStore"
import { useMerchantIdStore } from "@/app/store/merchant/merchantIdStore"

export function InvestigationGPT() {
  const { addTab, setActiveTabId, setCollapsed } = useArtifactStore()
  const { 
    messages, 
    isLoading, 
    currentMessage,
    // currentResponse,
    activeChatId,
    addMessage,
    setLoading,
    setCurrentMessage,
    getNewChatId,
    sendMessage,
    sendWebSearchMessage,
  } = useChatStore()  
  
  const { fetchFraudInvestigationSuggestions } = usePromptsStore()
  const { selectedMerchantId } = useMerchantIdStore()

  const [mode, setMode] = useState<MessageMode>('chat')
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [initialChatMessage, setInitialChatMessage] = useState('')

  useEffect(() => {
    if (!activeChatId) return
    console.log("activeChatId in investigationgpt", activeChatId)

    // Get the first user message if it exists
    const firstUserMessage = messages.find(message => message.isUser)?.text || '';
    
    // Truncate to first 20 characters or use "blank chat" if no message
    const messageSummary = firstUserMessage 
      ? firstUserMessage.substring(0, 20) + (firstUserMessage.length > 20 ? '...' : '') 
      : 'Blank Chat';
    
    // Create unique tab ID with prefix
    const tabId = `chat-artifact-${activeChatId || 'new-chat'}`
    
    addTab({
      id: tabId,
      title: `${messageSummary} - N ${activeChatId}`,
      renderArtifact: () => (
        <InvestigationGPTChat chatId={activeChatId || 'new-chat'} />
      )
    })
    setActiveTabId(tabId)
    setCollapsed(false)
  }, [activeChatId, messages])

  const handleNewChat = async () => {
    console.log("Plus button clicked - creating new chat");
    
    // Fetch initial fraud investigation suggestions for the dialog
    // Using empty query to get the default 5 suggestions
    await fetchFraudInvestigationSuggestions();
    
    // Clear any previously entered message
    setInitialChatMessage('');
    
    // Open the dialog
    setIsDialogOpen(true);
  }

  const handleCreateNewChat = async () => {
    console.log("Creating new chat from dialog with initial message:", initialChatMessage);
    
    const mId = selectedMerchantId || 'default-merchant';
    console.log("Using merchant ID:", mId);
    
    try {
      // Create new chat with has_visualization=false
      const newChatId = await getNewChatId(false);
      console.log("New chat created with ID:", newChatId);
      
      if (newChatId && initialChatMessage) {
        // Explicitly add the user message to the store first
        addMessage({
          text: initialChatMessage,
          isUser: true
        });
        
        // Wait a moment for the UI to update and the chat to be ready
        setTimeout(async () => {
          try {
            // Send the initial message to the new API
            console.log("Sending initial message to chat:", initialChatMessage);
            await sendMessage(initialChatMessage, false);
            console.log("Initial message sent successfully");
          } catch (error) {
            console.error("Error sending initial message:", error);
            // Don't throw here to prevent blocking the dialog from closing
          }
        }, 200); // Increased timeout to ensure chat is ready
      } else if (!newChatId) {
        console.error("Failed to create new chat - no chat ID returned");
      }
      
      // Close the dialog and reset the initial message
      setIsDialogOpen(false);
      setInitialChatMessage('');
    } catch (error) {
      console.error("Error in handleCreateNewChat:", error);
      // Dialog will remain open so user can try again
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentMessage.trim()) return;

    console.log("Submitting message:", currentMessage);
    
    const mId = selectedMerchantId || 'default-merchant';
    console.log("Using merchant ID:", mId);

    setLoading(true);
    const messageToSend = currentMessage;
    setCurrentMessage("");

    try {
      // Check if the artifact panel is collapsed
      const isArtifactCollapsed = useArtifactStore.getState().isCollapsed;
      
      // If the artifact is collapsed or there's no active chat, start a new chat first
      if (isArtifactCollapsed || !activeChatId) {
        console.log("Creating new chat before sending message");
        // Create a new chat with has_visualization=false
        const newChatId = await getNewChatId(false);
        console.log("New chat created with ID:", newChatId);
        
        if (newChatId) {
          // Explicitly add the user message to the store first
          addMessage({
            text: messageToSend,
            isUser: true
          });
          
          // Wait a moment for the UI to update and the chat to be ready
          setTimeout(async () => {
            try {
              // Now send the message to the new chat
              console.log("Sending message to new chat:", messageToSend);
              switch (mode) {
                case 'chat':
                  await sendMessage(messageToSend, false);
                  console.log("Message sent successfully to new chat");
                  break;
                case 'websearch':
                  await sendWebSearchMessage(messageToSend, false);
                  console.log("Web search message sent successfully to new chat");
                  break;
              }
            } catch (error) {
              console.error("Error sending message to new chat:", error);
              setLoading(false);
            }
          }, 200); // Increased timeout to ensure chat is ready
        } else {
          console.error("Failed to create new chat");
          setLoading(false);
        }
      } else {
        // Artifact is already open and there's an active chat, proceed as normal
        console.log("Sending message to existing chat:", activeChatId);
        
        // Explicitly add the user message to the store first
        addMessage({
          text: messageToSend,
          isUser: true
        });
        
        try {
          switch (mode) {
            case 'chat':
              await sendMessage(messageToSend, false);
              console.log("Message sent successfully to existing chat");
              break;
            case 'websearch':
              await sendWebSearchMessage(messageToSend, false);
              console.log("Web search message sent successfully to existing chat");
              break;
          }
        } catch (error) {
          console.error("Error sending message to existing chat:", error);
          setLoading(false);
        }
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setLoading(false);
    }
  }

  useEffect(() => {
    console.log("messages in investigationgpt", messages);
    console.log("activeChatId in investigationgpt", activeChatId);
  }, [messages, activeChatId]);

  return (
    <div className="flex-shrink-0 bg-white rounded-lg p-2 h-12">
      <form onSubmit={handleSubmit} className="flex gap-2">
        <div className="flex-1 flex items-center gap-2">
          <Button
            onClick={handleNewChat}
            type="button"
            size="icon"
            variant="outline"
            className="h-8 w-8 shrink-0"
            disabled={isLoading}
          >
            <Plus className="h-3.5 w-3.5" />
            <span className="sr-only">New chat</span>
          </Button>
          <div className="flex-1">
            <InvestigationGPTSuggestions 
              onPromptClick={(prompt) => setCurrentMessage(prompt)}
              disabled={isLoading}
            />
          </div>
          <div className="flex items-center gap-2">
            <InvestigationGPTInput 
              message={currentMessage}
              setMessage={setCurrentMessage}
              mode={mode}
              setMode={setMode}
              disabled={isLoading}
            />
            <Button 
              onClick={handleSubmit}
              type="button"
              size="icon"
              className="h-8 w-8 shrink-0"
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="h-3.5 w-3.5 rounded-full border-2 border-t-transparent border-blue-500 animate-spin" />
              ) : (
                <Send className="h-3.5 w-3.5" />
              )}
              <span className="sr-only">Send message</span>
            </Button>
          </div>
        </div>
      </form>

      <CreateChatDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        onCreateChat={handleCreateNewChat}
        initialMessage={initialChatMessage}
        onInitialMessageChange={(value) => setInitialChatMessage(value)}
        disabled={isLoading}
      />
    </div>
  )
}
