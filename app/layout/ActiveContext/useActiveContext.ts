import { useActiveContextStore } from '@/app/store/activeContextStore';
import { useMerchantIdStore } from '@/app/store/merchant/merchantIdStore';
import { useInvestigationOverviewStore } from '@/app/store/merchant/investicationOverviewStore';
import { useInvestigationRedFlagsStore } from '@/app/store/merchant/InvestigationRedFlagsStore';
import { useInvestigationLinkagesStore } from '@/app/store/merchant/investigationLinkagesStore';
import { useInvestigationDigitalFootprintStore } from '@/app/store/merchant/investigationDigitalFootprintStore';
import { useActivityEventTimelineStore } from '@/app/store/merchant/activityEventTimelineStore';
import { useActivityTransactionsStore } from '@/app/store/merchant/activityTransactionsStore';
import { useCaseManagementStore } from '@/app/store/caseManagement/QueueManagerStore';
import { useInvestigationIDStore } from '@/app/store/caseManagement/InvestigationIDStore';
import { useWorkspace } from '../Workspace/WorkspaceContext';
import React from 'react';
import { cn } from "@/lib/utils"
import { contextGroupMapping } from './constants';
import { sidebarGroups } from '../AppSidebar';

const contextNavigationMapping = {
  merchant: { group: "Merchant", item: "Investigation" },
  customer: { group: "Customer", item: "Investigation" },
  rule: { group: "Strategy", item: "Rules" },
  case: { group: "Case Management", item: "Investigation Hub" }
} as const;

export function useActiveContext() {
  const { activeContexts, setContext } = useActiveContextStore();
  const { setSelectedMerchantId,fetchMerchantDetails } = useMerchantIdStore();
  const { fetchRiskAssessment, fetchKeyMetricList } = useInvestigationOverviewStore();
  const { fetchCaseInvestigations } = useCaseManagementStore();
  const { setInvestigationId, fetchInvestigationDetails } = useInvestigationIDStore();
  const { setActiveComponent, setActiveNavigation, activeNavigation } = useWorkspace();
  const [inputValue, setInputValue] = React.useState("");
  const [isOpen, setIsOpen] = React.useState(false);
  const commandRef = React.useRef<HTMLDivElement>(null);

  const handleSelect = React.useCallback((type: keyof typeof contextNavigationMapping, value: string | null, label: string | null) => {
    setContext(type, value);
    console.log("label : ", label);
    if (value !== null && type === "merchant") {
      setSelectedMerchantId(value);
      fetchMerchantDetails(value);
      fetchRiskAssessment(value);
      fetchKeyMetricList(value);
      fetchCaseInvestigations(value);
    }
    if (type === "case" && label !== null) {
      setInvestigationId(label);
      fetchInvestigationDetails(label);
      
      // Always navigate to the Investigation Hub when clicking on a case,
      // regardless of the current view
      const navigation = contextNavigationMapping[type];
      const group = sidebarGroups.find(g => g.label === navigation.group);
      const item = group?.items.find(i => i.label === navigation.item);
      if (group && item && 'component' in item) {
        const Component = item.component;
        setActiveComponent(() => React.createElement(Component));
        setActiveNavigation({ group: group.label, item: item.label });
      }
      return; // Stop here after handling case navigation
    }

    // Handle other context types (for non-case items)
    if (value !== null && !activeNavigation?.group?.includes("Merchant")) {
      const navigation = contextNavigationMapping[type];
      const group = sidebarGroups.find(g => g.label === navigation.group);
      const item = group?.items.find(i => i.label === navigation.item);
      if (group && item && 'component' in item) {
        const Component = item.component;
        setActiveComponent(() => React.createElement(Component));
        setActiveNavigation({ group: group.label, item: item.label });
      }
    }
  }, [activeNavigation]);

  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (commandRef.current && !commandRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }
    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const getItemClassName = (isActive: boolean) => {
    return cn(
      "transition-colors",
      "hover:bg-accent hover:text-accent-foreground",
      isActive && "bg-blue-50 hover:bg-blue-100 font-medium"
    )
  }

  const getDisplayText = () => {
    const groupMapping = Object.entries(contextGroupMapping).find(
      ([_, value]) => value.group === activeNavigation?.group
    );

    if (groupMapping) {
      const [contextType, mapping] = groupMapping;
      return {
        prefix: mapping.defaultPreText,
        value: activeContexts[contextType as keyof typeof activeContexts] || mapping.defaultText
      };
    }

    return {
      prefix: "",
      value: "Search for anything"
    };
  };

  return {
    activeContexts,
    inputValue,
    isOpen,
    commandRef,
    setInputValue,
    setIsOpen,
    handleSelect,
    getItemClassName,
    getDisplayText
  };
}