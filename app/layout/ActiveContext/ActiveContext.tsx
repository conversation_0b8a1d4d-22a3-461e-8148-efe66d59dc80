import * as React from "react"
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandList,
} from "@/components/ui/command"
import { useActiveContext } from "./useActiveContext"
import { ruleIds } from "./constants"
import { ActiveContextCommandGroup } from "./ActiveContextCommandGroup"
import { useMerchantIdStore } from "@/app/store/merchant/merchantIdStore"
import { useCustomerIdStore } from "@/app/store/customer/customerIdStore"
import { useInvestigationHubStore } from "@/app/store/caseManagement/InvestigationHubStore"

export function ActiveContext() {
  // Create a ref for the input element
  const inputRef = React.useRef<HTMLInputElement>(null)

  const { merchantIdList } = useMerchantIdStore()
  const { customerIdList, fetchCustomerIdList } = useCustomerIdStore()
  const { investigationReferences } = useInvestigationHubStore()

  const {
    activeContexts,
    inputValue,
    isOpen,
    commandRef,
    setInputValue,
    setIsOpen,
    handleSelect,
    getItemClassName,
    getDisplayText
  } = useActiveContext()

  React.useEffect(() => {
    fetchCustomerIdList()
  }, [fetchCustomerIdList])

  // Focus the input when isOpen changes to true
  React.useEffect(() => {
    if (isOpen && inputRef.current) {
      // Use a small timeout to ensure the input is rendered before focusing
      setTimeout(() => {
        inputRef.current?.focus()
      }, 0)
    }
  }, [isOpen])

  const displayText = getDisplayText()

  const commandGroups = [
    {
      heading: "Merchant IDs",
      items: merchantIdList,
      type: "merchant" as const,
      activeValue: activeContexts.merchant
    },
    {
      heading: "Customer IDs",
      items: customerIdList,
      type: "customer" as const,
      activeValue: activeContexts.customer
    },
    {
      heading: "Case IDs",
      items: investigationReferences.map(ref => ({
        value: ref.case_number,
        label: ref.investigation_id
      })),
      type: "case" as const,
      activeValue: activeContexts.case
    },
    {
      heading: "Rule IDs",
      items: ruleIds,
      type: "rule" as const,
      activeValue: activeContexts.rule
    }
  ]

  return (
    <div className="relative ml-auto mr-4" ref={commandRef}>
      <div 
        onClick={() => setIsOpen(true)}
        className="h-9 w-[200px] px-3 flex items-center gap-2 rounded-md border shadow-sm cursor-text bg-white"
      >
        <span className="text-sm truncate">
          <span className="text-muted-foreground">{displayText.prefix}</span>
          <span className={displayText.value.startsWith("Select") || displayText.value.startsWith("Search") 
            ? "text-muted-foreground" 
            : "text-blue-600 font-medium"}>
            {displayText.value}
          </span>
        </span>
      </div>

      {isOpen && (
        <div className="absolute right-0 top-[calc(100%+4px)] w-[300px] z-50">
          <Command className="rounded-lg border shadow-md">
            <CommandInput 
              ref={inputRef}
              placeholder="Search across all IDs..."
              value={inputValue}
              onValueChange={setInputValue}
            />
            <CommandList>
              <CommandEmpty>No results found.</CommandEmpty>
              
              {commandGroups.map(group => (
                <ActiveContextCommandGroup
                  key={group.heading}
                  heading={group.heading}
                  items={group.items}
                  activeValue={group.activeValue}
                  inputValue={inputValue}
                  onSelect={(value: string, label: string) => handleSelect(group.type, value, label)}
                  onClear={() => handleSelect(group.type, null, null)}
                  getItemClassName={getItemClassName}
                />
              ))}
            </CommandList>
          </Command>
        </div>
      )}
    </div>
  )
}