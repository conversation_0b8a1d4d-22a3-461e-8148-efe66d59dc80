import { ActiveContextType } from "./types";
// import { someRules } from "@/app/pages/Strategy/Red Flags/Rules/DeterministicRulesTab";
import { Rule } from "@/app/pages/Strategy/Red Flags/RedFlagsPage";
export const merchantIds = [
    { value: "M123", label: "TechServe Solutions" },
    { value: "M124", label: "Digital Payments Ltd" },
    { value: "M125", label: "Global Trade Solutions" },
    { value: "M126", label: "TechServe Solutions" },
    { value: "M127", label: "Digital Payments Ltd" },
    { value: "M128", label: "Global Trade Solutions" },
  ]
  
  export const customerIds = [
    { value: "C789", label: "<PERSON>" },
    { value: "C790", label: "<PERSON> Johnson" },
    { value: "C791", label: "<PERSON> Wilson" },
  ]
  
  // some hardcoded rules
  export const ruleIds = [
    { value: "R123", label: "Rule 1" },
    { value: "R124", label: "Rule 2" },
    { value: "R125", label: "Rule 3" },
    { value: "R126", label: "Rule 4" },
    { value: "R127", label: "Rule 5" }
  ]

  export const caseIds = [
    { value: "124", label: "High Customer Complaint Rate" },
    { value: "125", label: "Suspicious Transaction Pattern" },
    { value: "123", label: "Regulatory Compliance Review" },
    { value: "122", label: "AML Alert Investigation" },
    { value: "121", label: "Document Verification Issue" }
  ];

  export type ContextGroupMapping = {
    [K in keyof ActiveContextType]: {
      group: string;
      defaultText: string;
      defaultPreText: string;
    }
  }
  
  export const contextGroupMapping: ContextGroupMapping = {
    merchant: {
      group: "Merchant",
      defaultText: "None",
      defaultPreText: "Active Merchant: "
    },
    customer: {
      group: "Customer",
      defaultText: "None",
      defaultPreText: "Active Customer: "
    },
    case: {
      group: "Case Management",
      defaultText: "None",
      defaultPreText: "Active Case: "
    },
    rule: {
      group: "Strategy",
      defaultText: "None",
      defaultPreText: "Searched Rule: "
    }
  } 