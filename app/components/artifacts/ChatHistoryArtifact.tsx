import { FC, useEffect, useState } from 'react';
import { chatService } from '@/app/services/chat';
import { ChatDetailedMessage } from '@/app/types';

interface ChatHistoryArtifactProps {
  chatId: string;
}

export const ChatHistoryArtifact: FC<ChatHistoryArtifactProps> = ({ chatId }) => {
  const [messages, setMessages] = useState<ChatDetailedMessage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchChatHistory = async () => {
      setIsLoading(true);
      try {
        const data = await chatService.getChatDetailedHistory(chatId);
        console.log('Chat history data received:', data);
        
        if (data && Array.isArray(data)) {
          setMessages(data);
        } else {
          throw new Error('Failed to load chat history');
        }
      } catch (err) {
        console.error('Error fetching chat history:', err);
        setError(err instanceof Error ? err.message : 'Failed to load chat history');
      } finally {
        setIsLoading(false);
      }
    };

    fetchChatHistory();
  }, [chatId]);

  // Format date to be more readable
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch (error) {
      return dateString;
    }
  };

  if (isLoading) {
    return <div className="p-4">Loading chat history...</div>;
  }

  if (error) {
    return <div className="p-4 text-red-500">Error: {error}</div>;
  }

  return (
    <div>
      <div className="border-b p-4">
        <h2 className="text-lg font-semibold">Chat History</h2>
        <p className="text-sm text-gray-500">{messages.length} messages</p>
      </div>
      
      <div className="flex-1 overflow-y-auto p-4">
        {messages.map((msg, index) => (
          <div 
            key={msg.id || index}
            className={`mb-4 flex ${
              msg.sender === 'user' 
                ? 'justify-end' 
                : 'justify-start'
            }`}
          >
            <div className={`p-3 rounded-lg w-3/5 ${
              msg.sender === 'user' 
                ? 'bg-blue-100' 
                : 'bg-gray-100'
            }`}>
              <div className="text-xs text-gray-500 mb-1">
                {msg.sender === 'user' ? 'You' : 'Assistant'} • {formatDate(msg.created_at)}
              </div>
              
              <div>
                {msg.message && typeof msg.message === 'object' && 'message' in msg.message
                  ? msg.message.message
                  : typeof msg.message === 'string'
                    ? msg.message
                    : JSON.stringify(msg.message)}
              </div>
              
              {msg.message && 
               typeof msg.message === 'object' && 
               'sources' in msg.message && 
               msg.message.sources && 
               msg.message.sources.length > 0 && (
                <div className="mt-2 text-xs">
                  <p className="font-medium text-gray-500">Sources:</p>
                  <ul className="list-disc pl-4 text-blue-500">
                    {msg.message.sources.map((source, i) => (
                      <li key={i}>
                        <a href={source} target="_blank" rel="noopener noreferrer" className="hover:underline">
                          {source}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}; 