import { create } from 'zustand';
import { API } from '@/app/services/axios';

interface RiskMetrics {
  risk_segmentation: string;
  loss_given_default: number;
  exposure_at_default: number;
  expected_loss_at_default: number;
  expected_loss: number;
  average_days_deferred: number;
  pd_score: number;
}

interface RiskMetricsStore {
  metrics: RiskMetrics | null;
  loading: boolean;
  error: string | null;
  fetchRiskMetrics: (merchantId: string) => Promise<void>;
}

export const useRiskMetricsStore = create<RiskMetricsStore>((set) => ({
  metrics: null,
  loading: false,
  error: null,
  
  fetchRiskMetrics: async (merchantId: string) => {
    if (!merchantId) return;
    try {
      set({ loading: true, error: null });
      const response = await API.get(`/api/v1/credit-dashboard/${merchantId}/getRiskMetrics`);
      
      // Check if we have valid data
      if (!response.data || !response.data.data) {
        set({ 
          metrics: null, 
          loading: false, 
          error: 'No risk metrics data available for this merchant' 
        });
        return;
      }
      
      set({ metrics: response.data.data, loading: false });
    } catch (error: any) {
      console.error('Risk metrics fetch error:', error);
      let errorMessage = 'Failed to fetch risk metrics';
      
      // Extract more detailed error information if available
      if (error.response?.data?.detail) {
        errorMessage = `Error: ${error.response.data.detail}`;
      } else if (error.message) {
        errorMessage = `Error: ${error.message}`;
      }
      
      set({ error: errorMessage, loading: false, metrics: null });
    }
  },
})); 