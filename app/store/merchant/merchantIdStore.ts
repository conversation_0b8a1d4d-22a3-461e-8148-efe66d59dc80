import { create } from "zustand";
import { merchantService } from "@/app/services/merchantServices";
import { MerchantItemType, MerchantProfileType } from '@/app/types';


interface MerchantIdStore {
  merchantIdList: MerchantItemType[];
  selectedMerchantId: string | null;
  selectedMerchant: MerchantProfileType | null;
  loading: boolean;
  error: string | null;
  fetchMerchantIdList: () => Promise<void>;
  fetchMerchantDetails: (merchantId: string) => Promise<void>;
  setSelectedMerchantId: (merchantId: string | null) => void;
  clearSelectedMerchant: () => void;
}

export const useMerchantIdStore = create<MerchantIdStore>((set) => ({
  merchantIdList: [],
  selectedMerchantId: null,
  selectedMerchant: null,
  loading: false,
  error: null,
  
  setSelectedMerchantId: (merchantId: string | null) => 
    set({ selectedMerchantId: merchantId }),
    
  clearSelectedMerchant: () => 
    set({ selectedMerchant: null, selectedMerchantId: null }),
    
  fetchMerchantIdList: async () => {
    try {
      set({ loading: true, error: null });
      const merchants = await merchantService.getMerchantList();
      set({ merchantIdList: merchants, loading: false });
    } catch (error) {
      set({ error: 'Failed to fetch merchant list', loading: false });
    }
  },

  fetchMerchantDetails: async (merchantId: string ) => {
    if (!merchantId) return;
    try {
      set({ loading: true, error: null });
      const merchantData = await merchantService.getMerchantDetails(merchantId);
      set({ 
        selectedMerchant: merchantData, 
        selectedMerchantId: merchantId,
        loading: false 
      });
    } catch (error) {
      set({ error: 'Failed to fetch merchant details', loading: false });
    }
  },
}));
