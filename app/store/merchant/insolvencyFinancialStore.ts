import { create } from 'zustand';
import { merchantService } from '@/app/services/merchantServices';

interface FinancialStatementData {
  success: boolean;
  message: string;
  data: any[]; // Financial data array from the API
}

interface InsolvencyFinancialStore {
  financialsData: FinancialStatementData | null;
  loading: boolean;
  error: string | null;
  fetchFinancialsData: (merchantId: string) => Promise<void>;
}

export const useInsolvencyFinancialStore = create<InsolvencyFinancialStore>((set) => ({
  financialsData: null,
  loading: false,
  error: null,
  fetchFinancialsData: async (merchantId: string) => {
    try {
      set({ loading: true, error: null });
      const response = await merchantService.getMerchantFinancialTable(merchantId);
      set({ financialsData: response, loading: false });
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to fetch financial statement data', 
        loading: false 
      });
    }
  }
}));