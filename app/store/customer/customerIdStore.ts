import { create } from "zustand";
import { customerService } from "@/app/services/customerServices";

interface CustomerIdStore {
  customerIdList: { value: string; label: string }[];
  loading: boolean;
  error: string | null;
  fetchCustomerIdList: () => Promise<void>;
}

export const useCustomerIdStore = create<CustomerIdStore>((set) => ({
  customerIdList: [],
  loading: false,
  error: null,

  fetchCustomerIdList: async () => {
    try {
      set({ loading: true, error: null });
      const customers = await customerService.getCustomerList();
      set({ customerIdList: customers, loading: false });
    } catch (error) {
      set({ error: 'Failed to fetch customer list', loading: false });
    }
  }
}));