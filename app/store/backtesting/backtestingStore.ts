import { create } from 'zustand';
import { TableRow } from './types';
import { z } from 'zod';
import { API } from '@/app/services/axios';
import { ColumnConfig } from '@/app/pages/Strategy/Sandbox/SampleSandboxData';
import { BacktestingAnalysisSettings } from '@/app/pages/Strategy/Sandbox/Backtesting/types';
import { BucketConfig } from '@/app/types';

export interface TableConfig {
  id: string;
  name: string;
  description: string;
  schema: z.ZodObject<any>;
  default_field_mapping: Record<string, { label: string; matching_fields: string[] }>;
}

type FieldValuesResponse =
  | { field: string; min_value: string; max_value: string }
  | { field: string; distinct_values: string[] };

interface BucketCountRequest {
  field: string;
  buckets: BucketConfig[];
}

interface BucketCountResponse {
  field: string;
  buckets: BucketConfig[]; // Buckets with updated counts
  total_count: number;
}

interface BacktestingStore {
  data: TableRow[];
  tables: TableConfig[];
  isLoading: boolean;
  error: string | null;
  fetchData: () => Promise<void>;
  fetchTables: () => Promise<void>;
  getColumnConfig: (tableId: string) => ColumnConfig;
  fetchFieldValues: (tableId: string, field: string, responseType: 'min_max' | 'all') => Promise<FieldValuesResponse>;
  calculateBucketCounts: (tableId: string, field: string, buckets: BucketConfig[]) => Promise<BucketCountResponse>;
  runRuleAnalysis: (settings: BacktestingAnalysisSettings) => Promise<any>;
}

export const useBacktestingStore = create<BacktestingStore>((set, get) => ({
  data: [],
  tables: [],
  isLoading: false,
  error: null,
  fetchData: async () => {
    set({ isLoading: true, error: null });
    try {
      const { data } = await API.get('/api/v1/strategy/backtesting/data');
      set({ data, isLoading: false });
    } catch (error) {
      set({ error: 'Failed to fetch data', isLoading: false });
    }
  },
  fetchTables: async () => {
    set({ isLoading: true, error: null });
    try {
      const { data: { data_tables } } = await API.get('/api/v1/strategy/backtesting/config');
      console.log('API Response:', JSON.stringify(data_tables, null, 2));
      const tables = data_tables.map(({ table_details, default_field_mapping }: any) => {
        console.log('Table Details:', JSON.stringify(table_details, null, 2));
        return {
          id: table_details.id,
          name: table_details.name,
          description: table_details.description,
          schema: z.object(Object.fromEntries(
            Object.entries(table_details.schema).map(([key, type]) => [
              key,
              type === 'BOOLEAN' ? z.boolean() :
              type === 'DECIMAL(15, 2)' ? z.number() :
              type === 'DATETIME' ? z.date() :
              z.string()
            ])
          )),
          default_field_mapping: default_field_mapping || {}
        };
      });
      console.log('Processed Tables:', JSON.stringify(tables, null, 2));
      set({ tables, isLoading: false });
    } catch (error) {
      console.error('Error fetching tables:', error);
      set({ error: 'Failed to fetch tables', isLoading: false });
    }
  },
  getColumnConfig: (tableId: string) => {
    const table = get().tables.find(t => t.id === tableId);
    console.log('Getting column config for table:', tableId);
    console.log('Found table:', table);
    if (!table) {
      return {
        rowUniqueId: '',
        datetime: '',
        fraudLabel: '',
        amount: ''
      };
    }

    const defaultFieldMapping = table.default_field_mapping || {};
    console.log('Default field mapping:', defaultFieldMapping);
    const config = {
      rowUniqueId: defaultFieldMapping.txn_id?.matching_fields[0] || '',
      datetime: defaultFieldMapping.txn_datetime?.matching_fields[0] || '',
      fraudLabel: defaultFieldMapping.fraud_label?.matching_fields[0] || '',
      amount: defaultFieldMapping.txn_amt?.matching_fields[0] || '',
      applicationId: defaultFieldMapping.application_id?.matching_fields[0] || '',
      accountId: defaultFieldMapping.account_id?.matching_fields[0] || '',
      transactionId: defaultFieldMapping.txn_id?.matching_fields[0] || ''
    };
    console.log('Generated config:', config);
    return config;
  },
  fetchFieldValues: async (tableId: string, field: string, responseType: 'min_max' | 'all') => {
    try {
      const { data } = await API.get(`/api/v1/strategy/backtesting/values/${tableId}/${field}?response_type=${responseType}`);
      return data;
    } catch (error) {
      console.error('Error fetching field values:', error);
      throw new Error('Failed to fetch field values');
    }
  },
  calculateBucketCounts: async (tableId: string, field: string, buckets: BucketConfig[]) => {
    try {
      const requestPayload: BucketCountRequest = {
        field,
        buckets
      };

      const { data } = await API.post(`/api/v1/strategy/backtesting/bucket-counts/${tableId}`, requestPayload);
      return data;
    } catch (error) {
      console.error('Error calculating bucket counts:', error);
      throw new Error('Failed to calculate bucket counts');
    }
  },
  runRuleAnalysis: async (settings: BacktestingAnalysisSettings) => {
    try {
      const response = await API.post('/api/v1/strategy/backtesting/rule-analysis', settings);
      return response.data;
    } catch (error) {
      console.error('Error running rule analysis:', error);
      throw error;
    }
  }
}));