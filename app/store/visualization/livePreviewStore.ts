import { create } from 'zustand';

interface LivePreviewConfig {
  type: 'combo' | 'stats';
  data: any[];
  title?: string;
  description?: string;
  xAxisKey: string | string[];
  yAxisKeys: any[];
  leftYAxisLabel?: string;
  rightYAxisLabel?: string;
  groupBy?: string;
  errorColumn?: string;
  stacking?: string;
  normalization?: boolean;
  missingNullHandling?: string;
}

interface LivePreviewStore {
  // Map of visualization ID to live preview config
  livePreviewConfigs: Record<string, LivePreviewConfig>;

  // Actions
  setLivePreviewConfig: (visualizationId: string, config: LivePreviewConfig) => void;
  clearLivePreviewConfig: (visualizationId: string) => void;
  clearAllLivePreviewConfigs: () => void;
  getLivePreviewConfig: (visualizationId: string) => LivePreviewConfig | undefined;
}

export const useLivePreviewStore = create<LivePreviewStore>((set, get) => ({
  livePreviewConfigs: {},

  setLivePreviewConfig: (visualizationId: string, config: LivePreviewConfig) => {
    // Check if the config has actually changed before updating state
    const currentConfig = get().livePreviewConfigs[visualizationId];

    // Skip update if configs are equal (shallow comparison for performance)
    if (currentConfig === config) return;

    // More comprehensive comparison to catch all property changes
    if (currentConfig && config) {
      const sameType = currentConfig.type === config.type;
      const sameTitle = currentConfig.title === config.title;
      const sameDescription = currentConfig.description === config.description;
      const sameDataLength = currentConfig.data?.length === config.data?.length;
      const sameXAxis = JSON.stringify(currentConfig.xAxisKey) === JSON.stringify(config.xAxisKey);
      const sameYAxis = JSON.stringify(currentConfig.yAxisKeys) === JSON.stringify(config.yAxisKeys);
      const sameStacking = currentConfig.stacking === config.stacking;
      const sameLeftYAxisLabel = currentConfig.leftYAxisLabel === config.leftYAxisLabel;
      const sameRightYAxisLabel = currentConfig.rightYAxisLabel === config.rightYAxisLabel;
      const sameGroupBy = currentConfig.groupBy === config.groupBy;
      const sameErrorColumn = currentConfig.errorColumn === config.errorColumn;
      const sameNormalization = currentConfig.normalization === config.normalization;
      const sameMissingNullHandling = currentConfig.missingNullHandling === config.missingNullHandling;

      // Only skip update if ALL properties are the same
      if (sameType && sameTitle && sameDescription && sameDataLength && sameXAxis && sameYAxis &&
          sameStacking && sameLeftYAxisLabel && sameRightYAxisLabel && sameGroupBy &&
          sameErrorColumn && sameNormalization && sameMissingNullHandling) {
        return;
      }
    }

    // Only update if the config is different
    set((state) => ({
      livePreviewConfigs: {
        ...state.livePreviewConfigs,
        [visualizationId]: config
      }
    }));
  },

  clearLivePreviewConfig: (visualizationId: string) => {
    set((state) => {
      const { [visualizationId]: removed, ...rest } = state.livePreviewConfigs;
      return {
        livePreviewConfigs: rest
      };
    });
  },

  clearAllLivePreviewConfigs: () => {
    set({ livePreviewConfigs: {} });
  },

  getLivePreviewConfig: (visualizationId: string) => {
    return get().livePreviewConfigs[visualizationId];
  }
}));
