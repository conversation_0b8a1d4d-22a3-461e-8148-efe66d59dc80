import { create } from 'zustand';
import { ActiveContextType } from '../layout/ActiveContext/types';
import { useMerchantIdStore } from './merchant/merchantIdStore';

interface ActiveContextStore {
  activeContexts: ActiveContextType;
  setContext: (type: keyof ActiveContextType, value: string | null) => void;
  clearContexts: () => void;
}

export const useActiveContextStore = create<ActiveContextStore>((set) => ({
  activeContexts: {
    merchant: null,
    customer: null,
    rule: null,
    case: null
  },
  setContext: (type, value) => {
    set(state => {
      const newContexts = { 
        ...state.activeContexts, 
        [type]: value 
      };
      
      // Sync with merchantIdStore
      if (type === 'merchant') {
        useMerchantIdStore.getState().setSelectedMerchantId(value);
      }
      
      return { activeContexts: newContexts };
    });
  },
  clearContexts: () => set({ 
    activeContexts: {
      merchant: null,
      customer: null,
      rule: null,
      case: null
    }
  })
}));