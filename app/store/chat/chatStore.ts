import { create } from 'zustand';
import { chatService } from '@/app/services/chat';
import { detectLanguage } from '@/app/utils/codeUtils';
import { ChatHistoryItem } from '@/app/types';
import { useAuthStore } from '../authentication/authStore';
import { useMerchantIdStore } from '../merchant/merchantIdStore';
import { v4 as uuidv4 } from 'uuid';

interface Message {
  text: string;
  isUser: boolean;
  code?: {
    language: string;
    content: string;
  } | null;
  graph?: {
    type: string;
    data: any;
    metadata?: any;
    xAxisKey?: string;
    yAxisKeys?: any[];
    groupBy?: string;
    errorColumn?: string;
    stacking?: "none" | "normal" | "percent";
    normalization?: boolean;
    missingNullHandling?: string;
  } | null;
  results?: any[];
}

// Thinking step interface
interface ThinkingStep {
  stage: string;
  step: string;
  timestamp?: number;
  type?: string; // Add type property for tracking loading/success/error
}

interface ChatInfo {
  chat_id: string;
  has_visualization: boolean;
  created_at: string;
  chat_title: string | null;
  user_id: string;
  merchant_id: string;
  visualization_id: string | null;
}

interface ChatStore {
  messages: Message[];
  isLoading: boolean;
  currentMessage: string;
  streamingMessage: string;
  activeChatId: string | null;
  allChatIds: ChatInfo[];
  processingStep: string;
  processingProgress: string;
  isHistoryLoading: boolean;
  initializationStep: string;
  relevanceCheckStep: string;
  requirementAnalysisStep: string;
  codeGenerationStep: string;
  codeValidationStep: string;
  codeExecutionStep: string;
  dbStatus: string;
  thinkingSteps: ThinkingStep[];
  isThinkingComplete: boolean;
  
  // Actions
  addMessage: (message: Message) => void;
  setLoading: (loading: boolean) => void;
  setCurrentMessage: (message: string) => void;
  setStreamingMessage: (message: string) => void;
  clearMessages: () => void;
  setActiveChatId: (id: string) => void;
  addThinkingStep: (step: ThinkingStep) => void;
  clearThinkingSteps: () => void;
  setThinkingComplete: (isComplete: boolean) => void;
  
  // Chat Service Actions
  getNewChatId: (isVisualization?: boolean) => Promise<string | null>;
  sendMessage: (message: string, addUserMessage?: boolean) => Promise<(() => void) | undefined>;
  sendWebSearchMessage: (message: string, addUserMessage?: boolean) => Promise<(() => void) | undefined>;
  sendGraphVisualizationMessage: (message: string, addUserMessage?: boolean) => Promise<(() => void) | undefined>;
  fetchChatHistory: (chatId: string) => Promise<void>;
  fetchAllChatIds: () => Promise<void>;
  handleStreamData: (data: any) => void;
}

export const useChatStore = create<ChatStore>((set, get) => ({
  messages: [],
  isLoading: false,
  currentMessage: "",
  streamingMessage: "",
  activeChatId: null,
  allChatIds: [],
  processingStep: '',
  processingProgress: '',
  isHistoryLoading: false,
  initializationStep: '',
  relevanceCheckStep: '',
  requirementAnalysisStep: '',
  codeGenerationStep: '',
  codeValidationStep: '',
  codeExecutionStep: '',
  dbStatus: '',
  thinkingSteps: [],
  isThinkingComplete: false,

  // Basic actions
  addMessage: (message) => set((state) => ({ 
    messages: [...state.messages, message] 
  })),
  setLoading: (loading) => set({ isLoading: loading }),
  setCurrentMessage: (message) => set({ currentMessage: message }),
  setStreamingMessage: (message) => set({ streamingMessage: message }),
  clearMessages: () => set({ messages: [], streamingMessage: "" }),
  setActiveChatId: (id) => set({ activeChatId: id }),
  addThinkingStep: (step) => set((state) => {
    // Determine step type if not provided
    let stepType = step.type;
    if (!stepType) {
      const lowerStep = step.step.toLowerCase();
      if (lowerStep.includes('error') || lowerStep.includes('failed') || lowerStep.includes('timeout')) {
        stepType = 'error';
      } else if (
        lowerStep.includes('success') || 
        lowerStep.includes('complete') || 
        lowerStep.includes('finished') ||
        lowerStep.includes('is relevant')
      ) {
        stepType = 'success';
      } else {
        // Default to loading for most processing steps
        stepType = 'loading';
      }
    }
    
    return { 
      thinkingSteps: [...state.thinkingSteps, { 
        ...step, 
        type: stepType, 
        timestamp: Date.now() 
      }] 
    };
  }),
  clearThinkingSteps: () => set({ thinkingSteps: [], isThinkingComplete: false }),
  setThinkingComplete: (isComplete) => set({ isThinkingComplete: isComplete }),

  // Chat service actions
  getNewChatId: async (isVisualization: boolean = false, visualizationId: string | null = null) => {
    try {
      // Get merchant info
      const merchantState = useMerchantIdStore.getState();
      
      // Use selected merchant ID or a default value
      const merchantId = merchantState.selectedMerchantId || 'default-merchant';
      
      // For now, use merchantId as userId as requested
      const userId = merchantId;
      
      console.log('Creating new chat with user ID (using merchant ID):', userId, 'merchant:', merchantId);
      
      // Call the new API endpoint
      const chatId = await chatService.createNewChat(
        userId,
        merchantId,
        isVisualization,
        visualizationId
      );
      
      if (!chatId) {
        console.error('No chat ID returned from API');
        throw new Error('No chat ID returned from API');
      }
      
      console.log('New chat created with ID:', chatId);
      set({ activeChatId: chatId, messages: [] });
      return chatId;
    } catch (error) {
      console.error('Error getting new chat ID:', error);
      set({ isLoading: false });
      
      // Only show alert if there was an actual error
      if (error) {
        alert('Failed to create a new chat. Please try again.');
      }
      
      return null;
    }
  },

  sendMessage: async (message: string, addUserMessage: boolean = true) => {
    const state = get();
    if (!state.activeChatId) return Promise.resolve(undefined);

    // Get merchant info
    const merchantState = useMerchantIdStore.getState();
    
    // Use selected merchant ID or a default value
    const merchantId = merchantState.selectedMerchantId || 'default-merchant';
    
    // For now, use merchantId as userId as requested
    const userId = merchantId;

    // Only add user message if requested (to avoid duplicates)
    if (addUserMessage) {
      const userMessage = { text: message, isUser: true };
      // Check for duplicates (look at the last message)
      const lastMessage = state.messages.length > 0 ? state.messages[state.messages.length - 1] : null;
      const isDuplicate = lastMessage && lastMessage.isUser && lastMessage.text === message;
      
      // Only add if not a duplicate
      if (!isDuplicate) {
        set(state => ({ 
          messages: [...state.messages, userMessage]
        }));
      }
    }
    
    // Set loading state and reset steps
    set(state => ({ 
      isLoading: true,
      streamingMessage: "",
      // Reset all status messages
      initializationStep: "",
      relevanceCheckStep: "",
      requirementAnalysisStep: "",
      processingStep: "",
      processingProgress: "",
      codeGenerationStep: "",
      codeValidationStep: "",
      codeExecutionStep: "",
      dbStatus: "",
      // Reset thinking steps
      thinkingSteps: [],
      isThinkingComplete: false
    }));

    try {
      // Generate UUIDs for message_id and reply_message_id
      const messageId = uuidv4();
      const replyMessageId = uuidv4();
      
      // Add a default relevance checking step
      get().addThinkingStep({
        stage: "Relevance",
        step: "Checking relevance of the message",
        type: "loading"
      });
      
      // Update relevanceCheckStep for backward compatibility
      set({ relevanceCheckStep: "Checking relevance of the message" });
      
      await chatService.sendChatMessage(
        state.activeChatId,
        userId,
        merchantId,
        message,
        (data) => {
          console.log("Response data:", data);
          
          if (data.type === 'thinking' && data.steps) {
            // Log the full steps array for debugging
            console.log("All thinking steps received:", data.steps);
            
            // Set thinking complete to false since we're still receiving steps
            set({ isThinkingComplete: false });
            
            // Process thinking steps
            for (const step of data.steps) {
              console.log("Processing step:", step);
              
              // If step is undefined or doesn't have necessary properties, skip it
              if (!step || !step.stage || !step.step) {
                console.warn("Invalid step format:", step);
                continue;
              }
              
              // Add to our tracking array for the new component
              get().addThinkingStep(step);
              
              // Update the UI based on step stage (legacy support)
              switch (step.stage) {
                case 'relevance':
                  set({ relevanceCheckStep: step.step });
                  break;
                case 'processing':
                  set({ processingStep: step.step });
                  break;
                case 'initialization':
                  set({ initializationStep: step.step });
                  break;
                case 'requirement_analysis':
                  set({ requirementAnalysisStep: step.step });
                  break;
                case 'code_generation':
                  set({ codeGenerationStep: step.step });
                  break;
                case 'code_validation':
                  set({ codeValidationStep: step.step });
                  break;
                case 'code_execution':
                  set({ codeExecutionStep: step.step });
                  break;
                case 'db_status':
                  set({ dbStatus: step.step });
                  break;
                default:
                  // For any other step types, just use processing
                  console.log(`Unknown step stage '${step.stage}', using processingStep`);
                  set({ processingStep: step.step });
                  break;
              }
            }
          } else if (data.type === 'assistant' && data.is_final) {
            // Set thinking as complete when we receive the final message
            set({ isThinkingComplete: true });
            
            // Final message received
            const assistantMessage = {
              text: data.content,
              isUser: false,
              code: null,
              graph: null
            };
            
            set(state => ({
              messages: [...state.messages, assistantMessage],
              isLoading: false,
              // Reset all status messages
              initializationStep: "",
              relevanceCheckStep: "",
              requirementAnalysisStep: "",
              processingStep: "",
              processingProgress: "",
              codeGenerationStep: "",
              codeValidationStep: "",
              codeExecutionStep: "",
              dbStatus: ""
            }));
          }
        },
        (error) => {
          console.error('Error in chat stream:', error);
          const errorMessage = {
            text: "Sorry, there was an error processing your request. Please try again.",
            isUser: false,
            code: null,
            graph: null
          };
          
          set(state => ({
            messages: [...state.messages, errorMessage],
            isLoading: false
          }));
        }
      );
      return () => {}; // Return empty cleanup function
    } catch (error) {
      console.error('Error sending message:', error);
      
      // Add error message to chat
      const errorMessage = {
        text: "Sorry, there was an error sending your message. Please try again.",
        isUser: false,
        code: null,
        graph: null
      };
      
      set(state => ({
        messages: [...state.messages, errorMessage],
        isLoading: false
      }));
      
      return undefined;
    }
  },

  sendWebSearchMessage: async (message: string, addUserMessage: boolean = true) => {
    const state = get();
    if (!state.activeChatId) return Promise.resolve(undefined);

    // Get merchant info
    const merchantState = useMerchantIdStore.getState();
    
    // Use selected merchant ID or a default value
    const merchantId = merchantState.selectedMerchantId || 'default-merchant';
    
    // For now, use merchantId as userId as requested
    const userId = merchantId;

    console.log('Sending web search message to chat with IDs:', { 
      chatId: state.activeChatId,
      userId,
      merchantId,
      message
    });

    // Only add user message if requested (to avoid duplicates)
    if (addUserMessage) {
      const userMessage = { text: message, isUser: true };
      // Check for duplicates (look at the last message)
      const lastMessage = state.messages.length > 0 ? state.messages[state.messages.length - 1] : null;
      const isDuplicate = lastMessage && lastMessage.isUser && lastMessage.text === message;
      
      // Only add if not a duplicate
      if (!isDuplicate) {
        set(state => ({ 
          messages: [...state.messages, userMessage]
        }));
      }
    }
    
    // Set loading state and reset steps
    set((state) => ({ 
      isLoading: true,
      streamingMessage: "",
      // Reset all status messages
      initializationStep: "",
      relevanceCheckStep: "",
      requirementAnalysisStep: "",
      processingStep: "",
      processingProgress: "",
      codeGenerationStep: "",
      codeValidationStep: "",
      codeExecutionStep: "",
      dbStatus: "",
      // Reset thinking steps
      thinkingSteps: [],
      isThinkingComplete: false
    }));

    try {
      // Generate UUIDs for message_id and reply_message_id
      const messageId = uuidv4();
      const replyMessageId = uuidv4();
      
      // Add a default relevance checking step
      get().addThinkingStep({
        stage: "Relevance",
        step: "Checking relevance of the message",
        type: "loading"
      });
      
      // Update relevanceCheckStep for backward compatibility
      set({ relevanceCheckStep: "Checking relevance of the message" });
      
      const cleanup = await chatService.sendChatMessage(
        state.activeChatId,
        userId,
        merchantId,
        message,
        (data: any) => {
          console.log("Response data from sendWebSearchMessage:", data);
          
          if (data.type === 'thinking' && data.steps) {
            console.log("Thinking steps:", data.steps);
            
            // Set thinking complete to false since we're still receiving steps
            set({ isThinkingComplete: false });
            
            // Process thinking steps
            for (const step of data.steps) {
              // If step is undefined or doesn't have necessary properties, skip it
              if (!step || !step.stage || !step.step) {
                console.warn("Invalid step format:", step);
                continue;
              }
              
              // Add to our tracking array for the new component
              get().addThinkingStep(step);
              
              // Also update individual step states for backward compatibility
              switch (step.stage.toLowerCase()) {
                case 'relevance':
                  set({ relevanceCheckStep: step.step });
                  break;
                case 'processing':
                  set({ processingStep: step.step });
                  break;
                case 'initialization':
                  set({ initializationStep: step.step });
                  break;
                case 'requirement_analysis':
                  set({ requirementAnalysisStep: step.step });
                  break;
                case 'code_generation':
                  set({ codeGenerationStep: step.step });
                  break;
                case 'code_validation':
                  set({ codeValidationStep: step.step });
                  break;
                case 'code_execution':
                  set({ codeExecutionStep: step.step });
                  break;
                case 'db_status':
                  set({ dbStatus: step.step });
                  break;
              }
            }
          } else if (data.type === 'assistant' && data.is_final) {
            // Set thinking as complete when we receive the final message
            set({ isThinkingComplete: true });
            
            // Final message received
            const assistantMessage = {
              text: data.content,
              isUser: false,
              code: null,
              graph: null
            };
            
            set(state => ({
              messages: [...state.messages, assistantMessage],
              isLoading: false,
              // Reset all status messages
              initializationStep: "",
              relevanceCheckStep: "",
              requirementAnalysisStep: "",
              processingStep: "",
              processingProgress: "",
              codeGenerationStep: "",
              codeValidationStep: "",
              codeExecutionStep: "",
              dbStatus: ""
            }));
          }
        },
        (error: any) => {
          console.error('Error in web search:', error);
          
          // Add error message to chat
          const errorMessage: Message = {
            text: "Sorry, there was an error with the web search. Please try again.",
            isUser: false,
            code: null,
            graph: null
          };
          
          set((state) => ({
            messages: [...state.messages, errorMessage],
            isLoading: false
          }));
        }
      );

      return cleanup;
    } catch (error) {
      console.error('Error sending web search message:', error);
      
      // Add error message to chat
      const errorMessage: Message = {
        text: "Sorry, there was an error sending your web search. Please try again.",
        isUser: false,
        code: null,
        graph: null
      };
      
      set((state) => ({
        messages: [...state.messages, errorMessage],
        isLoading: false
      }));
      
      return undefined;
    }
  },

  sendGraphVisualizationMessage: async (message: string, addUserMessage: boolean = true) => {
    const state = get();
    if (!state.activeChatId) return Promise.resolve(undefined);

    // Get merchant info
    const merchantState = useMerchantIdStore.getState();
    
    // Use selected merchant ID or a default value
    const merchantId = merchantState.selectedMerchantId || 'default-merchant';
    
    // For now, use merchantId as userId as requested
    const userId = merchantId;

    // Only add user message if requested (to avoid duplicates)
    if (addUserMessage) {
      const userMessage = { text: message, isUser: true };
      // Check for duplicates (look at the last message)
      const lastMessage = state.messages.length > 0 ? state.messages[state.messages.length - 1] : null;
      const isDuplicate = lastMessage && lastMessage.isUser && lastMessage.text === message;
      
      // Only add if not a duplicate
      if (!isDuplicate) {
        set(state => ({ 
          messages: [...state.messages, userMessage]
        }));
      }
    }
    
    // Set loading state and reset steps
    set((state) => ({ 
      isLoading: true,
      streamingMessage: "",
      // Reset all status messages
      initializationStep: "",
      relevanceCheckStep: "",
      requirementAnalysisStep: "",
      processingStep: "",
      processingProgress: "",
      codeGenerationStep: "",
      codeValidationStep: "",
      codeExecutionStep: "",
      dbStatus: "",
      // Reset thinking steps
      thinkingSteps: [],
      isThinkingComplete: false
    }));

    try {
      // Generate UUIDs for message_id and reply_message_id
      const messageId = uuidv4();
      const replyMessageId = uuidv4();
      
      // Add a default relevance checking step
      get().addThinkingStep({
        stage: "Relevance",
        step: "Checking relevance of the message",
        type: "loading"
      });
      
      // Update relevanceCheckStep for backward compatibility
      set({ relevanceCheckStep: "Checking relevance of the message" });

      const cleanup = await chatService.sendChatMessage(
        state.activeChatId,
        userId,
        merchantId,
        message,
        (data: any) => {
          console.log("backend data in sendGraphVisualizationMessage", data);
          
          if (data.type === 'thinking' && data.steps) {
            console.log("Thinking steps:", data.steps);
            
            // Set thinking complete to false since we're still receiving steps
            set({ isThinkingComplete: false });
            
            // Process thinking steps
            for (const step of data.steps) {
              // If step is undefined or doesn't have necessary properties, skip it
              if (!step || !step.stage || !step.step) {
                console.warn("Invalid step format:", step);
                continue;
              }
              
              // Add to our tracking array for the new component
              get().addThinkingStep(step);
              
              // Also update individual step states for backward compatibility
              switch (step.stage.toLowerCase()) {
                case 'relevance':
                  set({ relevanceCheckStep: step.step });
                  break;
                case 'processing':
                  set({ processingStep: step.step });
                  break;
                case 'initialization':
                  set({ initializationStep: step.step });
                  break;
                case 'requirement_analysis':
                  set({ requirementAnalysisStep: step.step });
                  break;
                case 'code_generation':
                  set({ codeGenerationStep: step.step });
                  break;
                case 'code_validation':
                  set({ codeValidationStep: step.step });
                  break;
                case 'code_execution':
                  set({ codeExecutionStep: step.step });
                  break;
                case 'db_status':
                  set({ dbStatus: step.step });
                  break;
              }
            }
          } else if (data.type === 'assistant' && data.is_final) {
            // Set thinking as complete when we receive the final message
            set({ isThinkingComplete: true });
            
            // Final message received
            const currentMessage: Message = {
              text: data.content,
              isUser: false,
              code: null,
              graph: null
            };
            
            // Handle code and graph content if present
            if (data.code) {
              currentMessage.code = {
                language: detectLanguage(data.code),
                content: data.code
              };
            }
            
            if (data.graph_type && data.content) {
              currentMessage.graph = {
                type: data.graph_type,
                data: data.content
              };
            }
            
            set(state => ({
              messages: [...state.messages, currentMessage],
              isLoading: false,
              // Reset all status messages
              initializationStep: "",
              relevanceCheckStep: "",
              requirementAnalysisStep: "",
              processingStep: "",
              processingProgress: "",
              codeGenerationStep: "",
              codeValidationStep: "",
              codeExecutionStep: "",
              dbStatus: ""
            }));
          }
        },
        (error: any) => {
          console.error('Error in graph visualization chat:', error);
          
          // Add error message to chat
          const errorMessage = {
            text: "Sorry, there was an error generating the visualization. Please try again.",
            isUser: false,
            code: null,
            graph: null
          };
          
          set(state => ({
            messages: [...state.messages, errorMessage],
            isLoading: false
          }));
        }
      );

      return cleanup;
    } catch (error) {
      console.error('Error sending graph visualization message:', error);
      
      // Add error message to chat
      const errorMessage = {
        text: "Sorry, there was an error sending your visualization request. Please try again.",
        isUser: false,
        code: null,
        graph: null
      };
      
      set(state => ({
        messages: [...state.messages, errorMessage],
        isLoading: false
      }));
      
      return undefined;
    }
  },

  fetchChatHistory: async (chatId: string) => {
    try {
      set({ isHistoryLoading: true });
      
      console.log(`Fetching chat history for chat ID: ${chatId}`);
      
      if (!chatId) {
        console.error("Cannot fetch history: No chat ID provided");
        set({ isHistoryLoading: false });
        return;
      }
      
      // For now, we'll just set an empty history for new API chats
      if (chatId.includes("new-chat") || chatId === "undefined") {
        console.log("New chat detected, setting empty history");
        set({ messages: [], isHistoryLoading: false });
        return;
      }
      
      // Get history from API endpoint
      const history: ChatHistoryItem[] = await chatService.getActiveChatHistory(chatId);
      console.log("Fetched history:", history);
      
      // Sort history to ensure oldest messages appear first
      const sortedHistory = [...history].sort((a, b) => 
        new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      );
      
      const messages: Message[] = sortedHistory.map((item: ChatHistoryItem) => {
        if (item.writer === 'user') {
          return {
            text: item.message,
            isUser: true,
            code: null,
            graph: null
          };
        } else {
          return parseAssistantMessage(item.message);
        }
      });

      set({ messages });
    } catch (error) {
      console.error('Error in fetchChatHistory:', error);
    } finally {
      set({ isHistoryLoading: false });
    }
  },

  fetchAllChatIds: async () => {
    try {
      const chatIds = await chatService.getActiveChatIds();
      set({ allChatIds: chatIds });
    } catch (error) {
      console.error('Error fetching chat IDs:', error);
    }
  },

  // Handle stream data events
  handleStreamData: (data) => {
    if (!data.type) return;
    
    switch (data.type) {
      case 'init':
        set({ initializationStep: data.content });
        break;
      case 'status':
        switch (data.step) {
          case 'relevance_check':
            set({ relevanceCheckStep: data.content });
            break;
          case 'requirement_analysis':
            set({ requirementAnalysisStep: data.content });
            break;
          case 'processing':
            set({ processingStep: data.content });
            break;
        }
        break;
      case 'progress':
        set({ processingProgress: data.content });
        break;
      case 'code_gen':
        set({ codeGenerationStep: data.content });
        break;
      case 'validation':
        set({ codeValidationStep: data.content });
        break;
      case 'execution_status':
        set({ codeExecutionStep: data.content });
        break;
      case 'execution_result':
        // Handle code execution results
        if (data.success && data.data && data.data.reply_message) {
          // Check if the message starts with "Here are the results" which indicates results data
          const isResultsMessage = typeof data.data.reply_message.message === 'string' && 
            data.data.reply_message.message.startsWith('Here are the results');
          
          // Create a message with the results for table rendering
          const resultMessage: Message = {
            // If there's code or results message, only show the success message, otherwise show the full message
            text: data.data.reply_message.code || isResultsMessage 
              ? "Code execution completed successfully" 
              : data.data.reply_message.message,
            isUser: false,
            code: data.data.reply_message.code ? {
              language: detectLanguage(data.data.reply_message.code),
              content: data.data.reply_message.code
            } : null,
            // Include results if they exist
            results: data.data.reply_message.results || undefined
          };
          
          set((state) => ({
            messages: [...state.messages, resultMessage],
            isLoading: false
          }));
        }
        break;
      case 'db_status':
        set({ dbStatus: data.content });
        break;
      // ... other cases remain the same
    }
  }
}));

const parseAssistantMessage = (messageStr: string): Message => {
  try {
    // Special case for messages that start with "Here are the results"
    if (messageStr.startsWith('Here are the results')) {
      try {
        // Try to extract JSON from the message
        const jsonStart = messageStr.indexOf('[');
        const jsonEnd = messageStr.lastIndexOf(']') + 1;
        
        if (jsonStart > 0 && jsonEnd > jsonStart) {
          const jsonStr = messageStr.substring(jsonStart, jsonEnd);
          const results = JSON.parse(jsonStr);
          
          if (Array.isArray(results)) {
            return {
              text: "Code execution completed successfully",
              isUser: false,
              code: null,
              graph: null,
              results: results
            };
          }
        }
      } catch (e) {
        console.error('Error parsing results from message:', e);
      }
    }
    
    const events = JSON.parse(messageStr);
    const message: Message = {
      text: '',
      isUser: false,
      code: null,
      graph: null,
      results: undefined
    };

    for (const event of events) {
      switch (event.type) {
        case 'assistant':
          if (event.is_final) {
            message.text = event.content;
          }
          break;
        case 'code': 
          message.code = {
            language: detectLanguage(event.content),
            content: event.content
          };
          break;
        case 'graph_code':
          message.code = {
            language: detectLanguage(event.content),
            content: event.content
          };
          break;
        case 'graph_data':
          message.graph = {
            type: 'visualization',
            data: event.content
          };
          break;
        case 'graph_metadata':
          if (message.graph) {
            message.graph = {
              ...message.graph,
              metadata: event.content
            };
          }
          break;
        case 'results':
          // Include results if they exist
          if (event.content && Array.isArray(event.content)) {
            message.results = event.content;
          }
          break;
      }
    }

    return message;
  } catch (error) {
    console.error('Error parsing assistant message:', error);
    return {
      text: messageStr,
      isUser: false,
      code: null,
      graph: null
    };
  }
};