'use client';

import { FC, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { LoginForm } from './components/LoginForm';
import { RegisterForm } from './components/RegisterForm';
import { Button } from '@/components/ui/button';
import { SuccessDialog } from './components/SuccessDialog';

const AuthPage: FC = () => {
  const [isLogin, setIsLogin] = useState(true);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);

  const handleRegistrationSuccess = () => {
    setShowSuccessDialog(true);
  };

  const handleSuccessDialogClose = () => {
    setShowSuccessDialog(false);
    setIsLogin(true);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="space-y-8"
      >
        <motion.div 
          className="text-center"
          initial={{ scale: 0.95 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <h1 className="text-3xl font-bold">Welcome to Aphrodite</h1>
          <p className="text-gray-600 mt-2">Please sign in to continue</p>
        </motion.div>
        
        <AnimatePresence mode="wait">
          <motion.div
            key={isLogin ? 'login' : 'register'}
            initial={{ opacity: 0, x: isLogin ? -20 : 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: isLogin ? 20 : -20 }}
            transition={{ duration: 0.3 }}
          >
            {isLogin ? (
              <LoginForm />
            ) : (
              <RegisterForm onRegistrationSuccess={handleRegistrationSuccess} />
            )}
          </motion.div>
        </AnimatePresence>
        
        <div className="text-center">
          <Button
            variant="link"
            onClick={() => setIsLogin(!isLogin)}
          >
            {isLogin ? "Don't have an account? Register" : "Already have an account? Login"}
          </Button>
        </div>
      </motion.div>

      <SuccessDialog 
        isOpen={showSuccessDialog} 
        onClose={handleSuccessDialogClose}
      />
    </div>
  );
};

export default AuthPage;