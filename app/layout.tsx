'use client'

import { AppSidebar } from "@/app/layout/AppSidebar"
import { AppTopbar } from "@/app/layout/AppTopbar"
import { AppContent } from "@/app/layout/AppContent"
import type { Metadata } from "next"
import localFont from "next/font/local"
import "./globals.css"
import { Providers } from "@/components/Providers"
// import { WorkspaceProvider } from "./layout/Workspace/WorkspaceContext"
import { useMerchantIdStore } from './store/merchant/merchantIdStore';
import { useInvestigationOverviewStore } from './store/merchant/investicationOverviewStore';
import { useInvestigationRedFlagsStore } from './store/merchant/InvestigationRedFlagsStore';
import { useInvestigationLinkagesStore } from './store/merchant/investigationLinkagesStore';
import { useInvestigationDigitalFootprintStore } from './store/merchant/investigationDigitalFootprintStore';
import { useActivityEventTimelineStore } from './store/merchant/activityEventTimelineStore';
import { useActivityTransactionsStore } from './store/merchant/activityTransactionsStore';
import { useCaseManagementStore } from './store/caseManagement/QueueManagerStore';
import { useInvestigationHubStore } from './store/caseManagement/InvestigationHubStore';
import { useActiveContextStore } from '@/app/store/activeContextStore';
import { useEffect, useState } from 'react';
import { useInvestigationIDStore } from "./store/caseManagement/InvestigationIDStore"
import { useAuthStore } from './store/authentication/authStore';
import { useRouter, usePathname } from 'next/navigation';

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
})

// export const metadata: Metadata = {
//   title: "Dashboard",
//   description: "Your application description",
// }

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { isAuthenticated } = useAuthStore();
  const router = useRouter();
  const pathname = usePathname();
  const { fetchMerchantIdList } = useMerchantIdStore();
  const { fetchInvestigationReferences } = useInvestigationHubStore();
  
  const [isClient, setIsClient] = useState(false);
  
  useEffect(() => {
    setIsClient(true);
    const savedAuth = localStorage.getItem('auth');
    if (savedAuth) {
      const authData = JSON.parse(savedAuth);
      if (authData.isAuthenticated && authData.accessToken) {
        useAuthStore.setState({
          isAuthenticated: true,
          accessToken: authData.accessToken,
          user: authData.user
        });
      }
    }
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      if (isAuthenticated) {
        try {
          await Promise.all([
            fetchMerchantIdList(),
            fetchInvestigationReferences()
          ]);
        } catch (error) {
          console.error('Failed to fetch initial data:', error);
        }
      }
    };

    if (isClient) {
      if (!isAuthenticated && pathname !== '/auth') {
        router.push('/auth');
      } else if (isAuthenticated && pathname === '/auth') {
        router.push('/');
      } else if (isAuthenticated) {
        fetchData();
      }
    }
  }, [isAuthenticated, pathname, router, fetchMerchantIdList, fetchInvestigationReferences, isClient]);

  if (!isClient) {
    return null;
  }

  return (
    <html lang="en" suppressHydrationWarning>
      <body className={geistSans.variable} suppressHydrationWarning>
        <Providers>
          {pathname === '/auth' ? (
            <div className="auth-container">{children}</div>
          ) : (
            isAuthenticated ? (
              // <WorkspaceProvider>
                <div className="flex h-screen">
                  <AppSidebar />
                  <div className="flex flex-col flex-1">
                    <AppTopbar />
                    <AppContent>
                      {children}
                    </AppContent>
                  </div>
                </div>
              // </WorkspaceProvider>
            ) : null
          )}
        </Providers>
      </body>
    </html>
  );
}
