import { CaseEvent, CaseEventType, CaseChannel, CaseStatus, CasePriority, Investigation } from '@/app/pages/CaseManagement/InvestigationHub/types';

export const investigationCases: Investigation[] = [
  {
    id: 'INV-001',
    caseNumber: '124',
    title: 'High Customer Complaint Rate',
    description: 'Multiple complaints about non-existent hotel bookings and unresponsive customer service.',
    status: 'In Progress',
    priority: 'High',
    assignee: '<PERSON><PERSON>',
    merchantId: 'M123',
    merchantName: 'Tech Solutions Ltd',
    // queueType: 'Complaint',
    channel: 'Partner Bank',
    // type: 'Complaint',
    createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
    lastUpdated: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
    slaDeadline: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
    events: [
      {
        id: 'evt-1',
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        type: 'trigger',
        description: 'Case opened due to multiple customer complaints',
        user: 'System',
        metadata: {
          channel: 'Partner Bank'
        }
      },
      {
        id: 'evt-2',
        timestamp: new Date(Date.now() - 23 * 60 * 60 * 1000).toISOString(),
        type: 'status_change',
        description: 'Case status changed from Open to In Progress',
        user: 'Rahul Kumar',
        metadata: {
          oldStatus: 'Open',
          newStatus: 'In Progress'
        }
      },
      {
        id: 'evt-3',
        timestamp: new Date(Date.now() - 22 * 60 * 60 * 1000).toISOString(),
        type: 'note',
        description: 'Investigation Note Added',
        content: 'Initial review of customer complaints shows pattern of booking confirmation delays',
        user: 'Rahul Kumar',
        metadata: {}
      }
    ]
  },
  {
    id: 'INV-002',
    caseNumber: '125',
    title: 'Suspicious Transaction Pattern',
    description: 'Unusual spike in high-value transactions from multiple locations within short timeframe.',
    status: 'In Progress',
    priority: 'High',
    assignee: 'Priya Singh',
    merchantId: 'M123',
    merchantName: 'Tech Solutions Ltd',
    // queueType: 'Fraud',
    channel: 'MHA Portal',
    // type: 'Fraud',
    createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
    lastUpdated: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
    slaDeadline: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
    events: [
      {
        id: 'evt-1',
        timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
        type: 'trigger',
        description: 'Suspicious transaction pattern detected',
        user: 'System',
        metadata: {
          channel: 'MHA Portal'
        }
      }
    ],
  },
  {
    id: 'INV-003',
    caseNumber: '123',
    title: 'Regulatory Compliance Review',
    description: 'Scheduled quarterly review of merchant compliance and documentation.',
    status: 'Closed',
    priority: 'Medium',
    assignee: 'Amit Patel',
    merchantId: 'M123',
    merchantName: 'Tech Solutions Ltd',
    // queueType: 'Compliance',
    channel: 'LEA',
    // type: 'Compliance',
    createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
    lastUpdated: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
    slaDeadline: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
    events: [
      {
        id: 'evt-1',
        timestamp: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
        type: 'trigger',
        description: 'Quarterly compliance review initiated',
        user: 'System',
        metadata: {
          channel: 'Direct'
        }
      },
      {
        id: 'evt-2',
        timestamp: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
        type: 'document',
        description: 'Required compliance documents received',
        user: 'Amit Patel',
        metadata: {
          documentType: 'Compliance Report'
        }
      }
    ],
  },
  {
    id: 'INV-004',
    caseNumber: '122',
    title: 'AML Alert Investigation',
    description: 'Investigation of potential money laundering patterns in transaction flow.',
    status: 'Closed',
    priority: 'High',
    assignee: 'Sarah Johnson',
    merchantId: 'M123',
    merchantName: 'Tech Solutions Ltd',
    // queueType: 'AML',
    channel: 'LEA',
    // type: 'AML',
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    lastUpdated: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    slaDeadline: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString(),
    events: [
      {
        id: 'evt-1',
        timestamp: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        type: 'trigger',
        description: 'AML alert received from monitoring system',
        user: 'System',
        metadata: {
          channel: 'LEA'
        }
      },
      {
        id: 'evt-2',
        timestamp: new Date(Date.now() - 29 * 24 * 60 * 60 * 1000).toISOString(),
        type: 'communication',
        description: 'Communication received from Law Enforcement',
        user: 'Sarah Johnson',
        metadata: {
          communicationType: 'Official Request'
        }
      }
    ],
  }
];

export type InvestigationCase = typeof investigationCases[0]; 