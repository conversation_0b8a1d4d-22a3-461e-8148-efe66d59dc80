import { Alert<PERSON>riangle, Check<PERSON>ircle2, XCircle } from "lucide-react";

import { AlertCircle } from "lucide-react";

export const DocumentStatus = ({ status }: { status: string }) => {
    const normalizedStatus = status.toLowerCase();
    const config = {
      verified: { icon: CheckCircle2, color: 'text-green-500', text: 'Verified' },
      pending: { icon: AlertCircle, color: 'text-yellow-500', text: 'Pending' },
      rejected: { icon: XCircle, color: 'text-red-500', text: 'Rejected' }
    }[normalizedStatus] || { icon: AlertTriangle, color: 'text-gray-500', text: status };   
  
    const Icon = config.icon;
    
    return (
      <div className={`flex items-center gap-1 ${config.color}`}>
        <Icon className="w-4 h-4" />
        <span className="text-sm">{config.text}</span>
      </div>
    );
  };
  