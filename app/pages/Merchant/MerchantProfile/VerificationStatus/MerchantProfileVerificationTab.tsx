import { FC } from 'react';
import { Calendar, Globe, Building2, FileText, ShieldCheck, UserCheck, Building, Loader2 } from 'lucide-react';
import { DocumentStatus, InfoItem } from './components';
import { TabSectionHeading } from '@/components/custom/TabSectionHeading';
import { useMerchantIdStore } from '@/app/store/merchant/merchantIdStore';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { EmptyState } from '../../MerchantInvestigation/Overview/components/EmptyState';

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 }
};

export const VerificationTab: FC = () => {
  const { selectedMerchant, loading, error } = useMerchantIdStore();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  if (error || !selectedMerchant) {
    return (
      <Card className="p-4">
        <EmptyState message={error || 'No verification data available'} />
      </Card>
    );
  }

  return (
    <motion.div 
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Onboarding Status */}
      <motion.div variants={itemVariants} className="space-y-4">
        <TabSectionHeading icon={Calendar} iconColorClass="text-blue-600">
          Onboarding Information
        </TabSectionHeading>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <InfoItem 
            icon={Calendar} 
            label="Onboarding Date" 
            value={new Date(selectedMerchant?.basicInfo.onboardingDate || '').toLocaleDateString()} 
          />
          <InfoItem 
            icon={Globe} 
            label="Onboarding Platform" 
            value={selectedMerchant?.basicInfo.onboardingPlatform || ''} 
          />
        </div>
      </motion.div>

      {/* KYC Status */}
      <motion.div variants={itemVariants} className="space-y-4">
        <TabSectionHeading icon={UserCheck} iconColorClass="text-green-600">
          KYC Status
        </TabSectionHeading>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 rounded-lg border border-gray-100">
            <p className="text-sm text-gray-500 mb-2">Overall KYC Status</p>
            <DocumentStatus status={selectedMerchant?.basicInfo.kycVerificationStatus || ''} />
            <p className="text-xs text-gray-500 mt-1">
              Verified on: {new Date(selectedMerchant?.basicInfo.kycVerificationDate || '').toLocaleDateString()}
            </p>
          </div>
          {/* {Object.entries(merchantData.compliance.kycStatus).map(([key, status]) => (
            <div key={key} className="p-4 rounded-lg border border-gray-100">
              <p className="text-sm text-gray-500 mb-2">
                {key.split(/(?=[A-Z])/).join(' ')}
              </p>
              <DocumentStatus status={status} />
            </div>
          ))} */}
        </div>
      </motion.div>

      {/* Bank Account Verification */}
      <motion.div variants={itemVariants} className="space-y-4">
        <TabSectionHeading icon={Building} iconColorClass="text-purple-600">
          Bank Account Details
        </TabSectionHeading>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <InfoItem 
            icon={Building2} 
            label="Bank Name" 
            value={selectedMerchant?.financial[0].bankName || ''} 
          />
          <InfoItem 
            icon={FileText} 
            label="Account Number" 
            value={selectedMerchant?.financial[0].accountNumber || ''} 
          />
          <InfoItem 
            icon={FileText} 
            label="IFSC Code" 
            value={selectedMerchant?.financial[0].ifsc || ''} 
          />
          <div className="p-4 rounded-lg border border-gray-100">
            <p className="text-sm text-gray-500 mb-2">Verification Status</p>
            <DocumentStatus status={selectedMerchant?.financial[0].verificationStatus || ''} />
            <p className="text-xs text-gray-500 mt-1">
              {/* Last verified: {new Date(merchantData.bankDetails.lastVerifiedDate).toLocaleDateString()} */}
            </p>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
}; 