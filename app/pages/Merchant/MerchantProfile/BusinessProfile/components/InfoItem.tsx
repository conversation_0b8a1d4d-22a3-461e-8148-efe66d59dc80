import { LucideIcon } from "lucide-react";

export const InfoItem = ({ 
    icon: Icon, 
    label, 
    value,
    colorClass = 'text-gray-500'
  }: { 
    icon: LucideIcon; 
    label: string; 
    value: string;
    colorClass?: string;
  }) => (
    <div className="flex items-start gap-3 p-3 rounded-lg bg-white border border-gray-100 hover:border-gray-200 hover:shadow-sm transition-all">
      <div className="shrink-0 mt-0.5">
        <Icon className={`w-4 h-4 ${colorClass}`} />
      </div>
      <div className="min-w-0">
        <p className="text-sm font-medium text-gray-900 truncate">{value}</p>
        <p className="text-xs text-gray-500">{label}</p>
      </div>
    </div>
  );