import { FC } from 'react';
import { Building2, Store, Briefcase, Calendar, Globe, MapPin, Mail, Phone, Loader2 } from 'lucide-react';
import { InfoItem } from './components/InfoItem';
import { TabSectionHeading } from '@/components/custom/TabSectionHeading';
import { useMerchantIdStore } from '@/app/store/merchant/merchantIdStore';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { EmptyState } from '../../MerchantInvestigation/Overview/components/EmptyState';

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 }
};


export const BusinessProfileTab: FC = () => {
  const { selectedMerchant, loading, error } = useMerchantIdStore();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  if (error || !selectedMerchant) {
    return (
      <Card className="p-4">
        <EmptyState message={error || 'No merchant data available'} />
      </Card>
    );
  }

  return (
    <motion.div 
      className="space-y-8 w-full"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div variants={itemVariants} className="space-y-4">
        <TabSectionHeading icon={Building2} iconColorClass="text-blue-600">
          Business Information
        </TabSectionHeading>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          <motion.div variants={itemVariants}>
            <InfoItem 
              icon={Building2} 
              label="Legal Business Name" 
              value={selectedMerchant.basicInfo.legalName}
              colorClass="text-blue-600" 
            />
          </motion.div>
          <motion.div variants={itemVariants}>
            <InfoItem 
              icon={Store} 
              label="Trade Name" 
              value={selectedMerchant.basicInfo.tradeName}
              colorClass="text-purple-600" 
            />
          </motion.div>
          <motion.div variants={itemVariants}>
            <InfoItem 
              icon={Briefcase} 
              label="Business Type" 
              value={selectedMerchant.basicInfo.businessType}
              colorClass="text-indigo-600" 
            />
          </motion.div>
          <motion.div variants={itemVariants}>
            <InfoItem 
              icon={Store} 
              label="Business Category" 
              value={`${selectedMerchant.basicInfo.businessCategory} / ${selectedMerchant.basicInfo.businessSubcategory}`}
              colorClass="text-pink-600" 
            />
          </motion.div>
          <motion.div variants={itemVariants}>
            <InfoItem 
              icon={Briefcase} 
              label="Business Model" 
              value={selectedMerchant.basicInfo.businessModel}
              colorClass="text-orange-600" 
            />
          </motion.div>
          <motion.div variants={itemVariants}>
            <InfoItem 
              icon={Calendar} 
              label="Date of Incorporation" 
              value={new Date(selectedMerchant.basicInfo.incorporationDate).toLocaleDateString()}
              colorClass="text-green-600" 
            />
          </motion.div>
        </div>
      </motion.div>

      <motion.div variants={itemVariants} className="space-y-4">
        <TabSectionHeading icon={Mail} iconColorClass="text-teal-600">
          Contact Information
        </TabSectionHeading>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          <motion.div variants={itemVariants}>
            <InfoItem 
              icon={MapPin} 
              label="Registered Address" 
              value={selectedMerchant.contacts[0].registeredAddress}
              colorClass="text-red-600" 
            />
          </motion.div>
          <motion.div variants={itemVariants}>
            <InfoItem 
              icon={MapPin} 
              label="Operating Address" 
              value={selectedMerchant.contacts[0].operatingAddress}
              colorClass="text-red-600" 
            />
          </motion.div>
          <motion.div variants={itemVariants}>
            <InfoItem 
              icon={Mail} 
              label="Email Address" 
              value={selectedMerchant.contacts[0].email}
              colorClass="text-teal-600" 
            />
          </motion.div>
          <motion.div variants={itemVariants}>
            <InfoItem 
              icon={Phone} 
              label="Phone Number" 
              value={selectedMerchant.contacts[0].phone}
              colorClass="text-emerald-600" 
            />
          </motion.div>
          <motion.div variants={itemVariants}>
            <InfoItem 
              icon={Phone} 
              label="Mobile Number" 
              value={selectedMerchant.contacts[0].mobile}
              colorClass="text-emerald-600" 
            />
          </motion.div>
          <motion.div variants={itemVariants}>
            <InfoItem 
              icon={Phone} 
              label="Alternate Phone" 
              value={selectedMerchant.contacts[0].altPhone}
              colorClass="text-emerald-600" 
            />
          </motion.div>
        </div>
      </motion.div>
    </motion.div>
  );
}; 