import { FC } from 'react';
import { Workspace } from '@/app/layout/Workspace/Workspace';
// import { CommunicationsList } from '../../Lists/CommunicationsList';
import { OverallTransactionsList } from './TransactionAndPayouts/OverallTransactionsList';
import { UnifiedTimelineList } from './EventTimeline/UnifiedTimelineList';     


const MerchantActivity: FC = () => {

  const tabs = [
    {
      id: 'timeline',
      label: 'Event Timeline',
      content: <UnifiedTimelineList />
    },
    {
      id: 'transactions & payouts',
      label: 'transactions & payouts',
      content: <OverallTransactionsList />
    },
  ];

  console.log("pankaj merchant activity")
  return <Workspace tabs={tabs} />;
};

export default MerchantActivity;
