import { useState, useEffect, useMemo } from 'react';
// import { useWorkspace } from '@/app/layout/Workspace/WorkspaceContext';
import { useActivityTransactionsStore } from '@/app/store/merchant/activityTransactionsStore';
import { useMerchantIdStore } from '@/app/store/merchant/merchantIdStore';
import { useArtifactStore } from '@/app/store/artifact/artifactStore';
import { TransactionsStatsCard } from '@/components/custom/TransactionsStatsCard';
import { ListChecks } from 'lucide-react';
import { motion } from 'framer-motion';

import { TransactionsBalanceCard } from '@/components/custom/TransactionsBalanceCard';
import { VirtualList, VirtualListItemProps } from '@/components/custom/VirtualList';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  PayoutArtifact,
  PayoutItem,
  TransactionItem,
  TransactionArtifact
} from './components';
import { ReportableSection } from '@/app/pages/ReportGeneration/utils/ReportSectionHelpers';

const filterByTimeRange = (timestamp: string, timeRange: string) => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

  switch (timeRange) {
    case 'last 7 days': return diffDays <= 7;
    case 'last 30 days': return diffDays <= 30;
    case 'last 90 days': return diffDays <= 90;
    default: return true;
  }
};

const filterByStatus = (status: string, itemStatus: string) =>
  status === 'all' ? true : itemStatus.toLowerCase() === status.toLowerCase();

const filterByChannel = (channel: string, paymentChannel: string) =>
  channel === 'all' ? true : paymentChannel.toLowerCase() === channel.toLowerCase();

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: {
      duration: 0.3
    }
  }
};

export const OverallTransactionsList = () => {
  // const { setArtifact } = useWorkspace();
  const { addTab, setActiveTabId, setCollapsed } = useArtifactStore();
  const { transactions, payouts, fetchTransactions, fetchPayouts } = useActivityTransactionsStore();
  const { selectedMerchantId } = useMerchantIdStore();

  useEffect(() => {
    console.log("OverallTransactionsList mounted");
    if (selectedMerchantId) {
      fetchTransactions(selectedMerchantId);
      fetchPayouts(selectedMerchantId);
    }
  }, [fetchTransactions, fetchPayouts, selectedMerchantId]);

  const [type, setType] = useState('transactions');
  const [status, setStatus] = useState('completed');
  const [channel, setChannel] = useState('pos');
  const [timeRange, setTimeRange] = useState('all');
  const [transactionStats, setTransactionStats] = useState({ totalAmount: 0, totalCount: 0, avgAmount: 0, avgCount: 0 });
  const [payoutStats, setPayoutStats] = useState({ totalAmount: 0, totalCount: 0, avgAmount: 0, avgCount: 0 });

  // Memoize filtered items
  const filteredItems = useMemo(() => {
    const filteredTransactions = type !== 'payouts' ? transactions.transactions.filter(t =>
      filterByTimeRange(t.timestamp, timeRange) &&
      filterByStatus(status, t.status) &&
      filterByChannel(channel, t.payment_channel)
    ) : [];

    const filteredPayouts = type !== 'transactions' ? payouts.payouts.filter(p =>
      filterByTimeRange(p.timestamp, timeRange) &&
      filterByStatus(status, p.status)
    ) : [];

    // Convert to VirtualListItemProps format
    const transactionItems: VirtualListItemProps[] = filteredTransactions.map(t => ({
      id: t.id,
      title: `Transaction: ${t.transaction_id}`,
      content: (
        <ReportableSection type="transaction" data={t}>
          <TransactionItem {...t} />
        </ReportableSection>
      ),
      metadata: {
        type: 'transaction',
        data: t,
        renderArtifact: () => <TransactionArtifact transaction={t} />
      }
    }));

    const payoutItems: VirtualListItemProps[] = filteredPayouts.map(p => ({
      id: p.id,
      title: `Payout: ₹${p.amount}`,
      content: (
        <ReportableSection type="payout" data={p}>
          <PayoutItem payout={p} />
        </ReportableSection>
      ),
      metadata: {
        type: 'payout',
        data: p,
        renderArtifact: () => <PayoutArtifact payout={p} />
      }
    }));

    // Combine and sort by timestamp
    return [...transactionItems, ...payoutItems].sort((a, b) => {
      const timestampA = a.metadata.type === 'transaction'
        ? a.metadata.data.timestamp
        : a.metadata.data.timestamp;
      const timestampB = b.metadata.type === 'transaction'
        ? b.metadata.data.timestamp
        : b.metadata.data.timestamp;
      return new Date(timestampB).getTime() - new Date(timestampA).getTime();
    });
  }, [transactions, payouts, type, status, channel, timeRange]);

  // Calculate stats
  useEffect(() => {
    const transactionItems = filteredItems.filter(item => item.metadata.type === 'transaction');
    const payoutItems = filteredItems.filter(item => item.metadata.type === 'payout');

    const transactionTotal = transactionItems.reduce((sum, item) =>
      sum + Number(item.metadata.data.amount), 0);
    const payoutTotal = payoutItems.reduce((sum, item) =>
      sum + Number(item.metadata.data.amount), 0);

    const daysInRange = timeRange === 'last 7 days' ? 7 :
      timeRange === 'last 30 days' ? 30 :
        timeRange === 'last 90 days' ? 90 : 1;

    setTransactionStats({
      totalAmount: transactionTotal,
      totalCount: transactionItems.length,
      avgAmount: transactionItems.length > 0 ? transactionTotal / transactionItems.length : 0,
      avgCount: transactionItems.length / daysInRange
    });

    setPayoutStats({
      totalAmount: payoutTotal,
      totalCount: payoutItems.length,
      avgAmount: payoutItems.length > 0 ? payoutTotal / payoutItems.length : 0,
      avgCount: payoutItems.length / daysInRange
    });
  }, [filteredItems, timeRange]);

  const handleItemClick = (item: VirtualListItemProps) => {
    addTab({
      id: item.id,
      title: item.title,
      renderArtifact: item.metadata.renderArtifact
    });
    setActiveTabId(item.id);
    setCollapsed(false);
  };

  return (
    <motion.div 
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div variants={itemVariants} className="grid grid-cols-2 gap-6">
        {type === 'all' ? (
          <TransactionsBalanceCard
            title="Balance Overview"
            icon={ListChecks}
            totalTransactionsAmount={transactionStats.totalAmount}
            totalPayoutAmount={payoutStats.totalAmount}
          />
        ) : type === 'transactions' ? (
          <>
            <TransactionsStatsCard
              title="Total Transactions"
              icon={ListChecks}
              amount={transactionStats.totalAmount}
              count={transactionStats.totalCount}
            />
            <TransactionsStatsCard
              title="Average Per Day"
              icon={ListChecks}
              amount={transactionStats.avgAmount}
              count={transactionStats.avgCount}
            />
          </>
        ) : (
          <>
            <TransactionsStatsCard
              title="Total Payouts"
              icon={ListChecks}
              amount={payoutStats.totalAmount}
              count={payoutStats.totalCount}
            />
            <TransactionsStatsCard
              title="Average Per Day"
              icon={ListChecks}
              amount={payoutStats.avgAmount}
              count={payoutStats.avgCount}
            />
          </>
        )}
      </motion.div>

      <motion.div variants={itemVariants} className="flex flex-wrap gap-4">
        <Select value={type} onValueChange={setType}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select type" />
          </SelectTrigger>
          <SelectContent>
            {['all', 'transactions', 'payouts'].map((option) => (
              <SelectItem key={option} value={option}>
                {option.charAt(0).toUpperCase() + option.slice(1)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={status} onValueChange={setStatus}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select status" />
          </SelectTrigger>
          <SelectContent>
            {['all', 'completed', 'incomplete'].map((option) => (
              <SelectItem key={option} value={option}>
                {option.charAt(0).toUpperCase() + option.slice(1)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={channel} onValueChange={setChannel}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select channel" />
          </SelectTrigger>
          <SelectContent>
            {['all', 'pos', 'online'].map((option) => (
              <SelectItem key={option} value={option}>
                {option.toUpperCase()}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select time range" />
          </SelectTrigger>
          <SelectContent>
            {['last 7 days', 'last 30 days', 'last 90 days', 'all'].map((option) => (
              <SelectItem key={option} value={option}>
                {option.charAt(0).toUpperCase() + option.slice(1)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </motion.div>

      <motion.div variants={itemVariants}>
        <VirtualList
          items={filteredItems}
          onItemClick={handleItemClick}
          itemHeight={90}
          viewportHeight={500}
          className="space-y-2"
        />
      </motion.div>
    </motion.div>
  );
};


