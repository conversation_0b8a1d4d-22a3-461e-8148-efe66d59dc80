import { Banknote } from "lucide-react";
import { PayoutType } from "@/app/types";
import { FC } from "react";
import { formatTimestamp } from "@/utils/timeFormat";
import { BubbleTag } from "@/components/custom/BubbleTag";
import { motion } from 'framer-motion';

const itemVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: {
      duration: 0.3
    }
  }
};

export const PayoutItem: FC<{ payout: PayoutType }> = ({ payout }) => {
    const getStatusConfig = () => {
      switch (payout.status) {
        case 'processed':
          return { color: 'green' as const, text: 'Processed' };
        case 'pending':
          return { color: 'yellow' as const, text: 'Pending' };
        case 'failed':
          return { color: 'red' as const, text: 'Failed' };
        default:
          return { color: 'gray' as const, text: payout.status };
      }
    };
  
    const config = getStatusConfig();
  
    return (
      <motion.div
        className="grid grid-cols-[auto_1fr_auto] gap-3 p-2 rounded-md cursor-pointer hover:bg-gray-50 h-[80px]"
        variants={itemVariants}
        initial="hidden"
        animate="visible"
      >
        <div className="flex items-center">
          <Banknote className="h-5 w-5 text-blue-500" />
        </div>
        <div className="min-w-0 flex flex-col justify-between">
          <div className="text-sm text-gray-900">
            ₹{payout.amount} • {payout.bank_account}
          </div>
          <div className="text-xs text-gray-500">
            UTR: {payout.utr}
          </div>
        </div>
        <div className="flex flex-col items-end justify-between h-full">
          <div className="text-xs text-gray-500">
            {formatTimestamp(payout.timestamp, 'verbose')}
          </div>
          <BubbleTag text={config.text} color={config.color} />
          <div className="text-xs text-gray-500">
            &nbsp;
          </div>
        </div>
      </motion.div>
    );
  };

export const PayoutArtifact: FC<{ payout: PayoutType }> = ({ payout }) => {
    const getStatusConfig = () => {
      switch (payout.status) {
        case 'processed':
          return { color: 'text-green-600', text: 'Processed' };
        case 'pending':
          return { color: 'text-yellow-600', text: 'Pending' };
        case 'failed':
          return { color: 'text-red-600', text: 'Failed' };
        default:
          return { color: 'text-gray-600', text: payout.status };
      }
    };
  
    const config = getStatusConfig();
  
    return (
      <motion.div
        className="space-y-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="flex items-center gap-2">
          <Banknote className="h-5 w-5 text-blue-500" />
          <h2 className="text-xl font-semibold">Payout Details</h2>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-500">Amount</p>
            <p className="text-sm font-medium">₹{payout.amount}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Status</p>
            <p className={`text-sm capitalize ${config.color}`}>
              {config.text}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Bank Account</p>
            <p className="text-sm">{payout.bank_account}</p>
          </div>
        </div>
      </motion.div>
    );
  };