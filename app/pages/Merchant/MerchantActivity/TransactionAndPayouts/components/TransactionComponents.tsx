import { FC } from "react";
import { formatTimestamp } from "@/utils/timeFormat";
import { TransactionType } from "@/app/types";
import { BubbleTag } from "@/components/custom/BubbleTag";
import { motion } from 'framer-motion';
import { Banknote } from "lucide-react";

const itemVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: {
      duration: 0.3
    }
  }
};

export const TransactionItem: FC<TransactionType> = (item) => {
    const getStatusConfig = () => {
      switch (item.status) {
        case 'completed':
          return {
            color: 'green' as const,
            text: 'Completed'
          };
        case 'failed':
          return {
            color: 'red' as const,
            text: 'Failed'
          };
        case 'pending':
          return {
            color: 'yellow' as const,
            text: 'Pending'
          };
        default:
          return {
            color: 'gray' as const,
            text: item.status
          };
      }
    };
  
    const config = getStatusConfig();
  
    return (
      <motion.div 
        className="grid grid-cols-[auto_1fr_auto] gap-3 p-2 rounded-md cursor-pointer hover:bg-gray-50 h-[80px]"
        variants={itemVariants}
        initial="hidden"
        animate="visible"
      >
        <div className="flex items-center">
          <Banknote className="h-5 w-5 text-green-500" />
        </div>
        <div className="min-w-0 flex flex-col justify-between">
          <div className="text-sm text-gray-900">
            {item.transaction_type} at {item.merchant_type} • ₹{item.amount}
          </div>
          <div className="text-xs text-gray-500">
            {item.merchant_name} • {item.city} {item.country_code} • {item.product_name}
          </div>
        </div>
        <div className="flex flex-col items-end justify-between h-full">
          <div className="text-xs text-gray-500">
            {formatTimestamp(item.timestamp, 'verbose')}
          </div>
          <div className="flex gap-1">
            {item.complain_date && (
              <BubbleTag
                text="Complaint"
                color="orange"
              />
            )}
            {item.dispute_date && (
              <BubbleTag
                text="Disputed"
                color="red"
              />
            )}
            <BubbleTag
              text={config.text}
              color={config.color}
            />
          </div>
          <div className="text-xs text-gray-500">
            ID: {item.transaction_id}
          </div>
        </div>
      </motion.div>
    );
  }; 

export const TransactionArtifact: FC<{ transaction: TransactionType }> = ({ transaction }) => {
  
    const getStatusConfig = () => {
      switch (transaction.status) {
        case 'completed':
          return {
            color: 'text-green-600',
            bg: 'bg-green-50',
            text: 'Completed'
          };
        case 'failed':
          return {
            color: 'text-red-600',
            bg: 'bg-red-50',
            text: 'Failed'
          };
        case 'pending':
          return {
            color: 'text-yellow-600',
            bg: 'bg-yellow-50',
            text: 'Pending'
          };
        default:
          return {
            color: 'text-gray-600',
            bg: 'bg-gray-50',
            text: status
          };
      }
    };
  
    const config = getStatusConfig();
  
    return (
      <motion.div 
        className="space-y-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="flex items-center gap-2">
          {/* {transaction.icon} */}
          <h2 className="text-xl font-semibold">{transaction.transaction_type} at {transaction.merchant_type}</h2>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-500">Transaction ID</p>
            <p className="text-sm">{transaction.transaction_id}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Merchant</p>
            <p className="text-sm">{transaction.merchant_name}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Amount</p>
            <p className="text-sm font-medium">₹{transaction.amount}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Status</p>
            <div className="flex gap-2 items-center">
              {transaction.complain_date && (
                <span className="px-2 py-0.5 text-xs font-medium text-orange-600 bg-orange-50 rounded">
                  Complaint
                </span>
              )}
              {transaction.dispute_date && (
                <span className="px-2 py-0.5 text-xs font-medium text-red-600 bg-red-50 rounded">
                  Disputed
                </span>
              )}
              <span className={`px-2 py-0.5 text-xs font-medium ${config.color} ${config.bg} rounded capitalize`}>
                {config.text}
              </span>
            </div>
          </div>
          <div>
            <p className="text-sm text-gray-500">Timestamp</p>
            <p className="text-sm">{formatTimestamp(transaction.timestamp, 'verbose')}</p>
          </div>
          {transaction.complain_date && (
            <div>
              <p className="text-sm text-gray-500">Complaint Date</p>
              <p className="text-sm">{formatTimestamp(transaction.complain_date, 'verbose')}</p>
            </div>
          )}
          {transaction.dispute_date && (
            <div>
              <p className="text-sm text-gray-500">Dispute Date</p>
              <p className="text-sm">{formatTimestamp(transaction.dispute_date, 'verbose')}</p>
            </div>
          )}
        </div>
      </motion.div>
    );
  };