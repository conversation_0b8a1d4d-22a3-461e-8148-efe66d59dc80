import { FC } from 'react';
import { AlertOctagon, AlertTriangle, AlertCircle, Flag } from 'lucide-react';
import { BubbleTag } from '@/components/custom/BubbleTag';
import { Badge } from "@/components/ui/badge";
import { format } from 'date-fns';
import { motion } from 'framer-motion';

interface FlagType {
  id: string;
  text: string;
  severity: string;
  timestamp: string;
  description?: string;
}

const itemVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: { opacity: 1, x: 0 }
};

export const FlagItem: FC<{ flag: FlagType }> = ({ flag }) => {
  const getSeverityConfig = () => {
    switch (flag.severity) {
      case 'critical':
        return {
          icon: <AlertOctagon className="h-4 w-4" />,
          color: 'red' as const,
          text: 'Critical'
        };
      case 'high':
        return {
          icon: <AlertTriangle className="h-4 w-4" />,
          color: 'orange' as const,
          text: 'High'
        };
      case 'medium':
        return {
          icon: <AlertCircle className="h-4 w-4" />,
          color: 'yellow' as const,
          text: 'Medium'
        };
      case 'low':
        return {
          icon: <Flag className="h-4 w-4" />,
          color: 'blue' as const,
          text: 'Low'
        };
      default:
        return {
          icon: <Flag className="h-4 w-4" />,
          color: 'gray' as const,
          text: flag.severity
        };
    }
  };

  const config = getSeverityConfig();

  return (
    <motion.div 
      className="grid grid-cols-[auto_1fr_auto] gap-3 p-2 rounded-md"
      variants={itemVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="flex items-center">
        {config.icon}
      </div>
      <div className="min-w-0 space-y-1">
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-xs">Red Flag</Badge>
          <span className="text-sm text-gray-900">{flag.text}</span>
        </div>
        <div className="text-xs text-gray-500">
          {format(new Date(flag.timestamp), 'MMM dd, yyyy')}
        </div>
      </div>
      <div className="flex items-center">
        <BubbleTag
          text={config.text}
          color={config.color}
        />
      </div>
    </motion.div>
  );
};

export const FlagArtifact: FC<{ flag: FlagType }> = ({ flag }) => {
  const getSeverityConfig = () => {
    switch (flag.severity) {
      case 'critical':
        return {
          icon: <AlertOctagon className="h-4 w-4" />,
          color: 'red' as const,
          text: 'Critical'
        };
      case 'high':
        return {
          icon: <AlertTriangle className="h-4 w-4" />,
          color: 'orange' as const,
          text: 'High'
        };
      case 'medium':
        return {
          icon: <AlertCircle className="h-4 w-4" />,
          color: 'yellow' as const,
          text: 'Medium'
        };
      case 'low':
        return {
          icon: <Flag className="h-4 w-4" />,
          color: 'blue' as const,
          text: 'Low'
        };
      default:
        return {
          icon: <Flag className="h-4 w-4" />,
          color: 'gray' as const,
          text: flag.severity
        };
    }
  };

  const config = getSeverityConfig();

  return (
    <motion.div 
      className="space-y-4"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex items-center gap-2">
        <div className={config.color}>{config.icon}</div>
        <h2 className="text-xl font-semibold">{flag.text}</h2>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <p className="text-sm text-gray-500">Severity</p>
          <p className="text-sm capitalize">{flag.severity}</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Timestamp</p>
          <p className="text-sm">{format(new Date(flag.timestamp), 'MMM dd, yyyy')}</p>
        </div>
        <div className="col-span-2">
          <p className="text-sm text-gray-500">Description</p>
          <p className="text-sm">{flag.description}</p>
        </div>
      </div>
    </motion.div>
  );
};
