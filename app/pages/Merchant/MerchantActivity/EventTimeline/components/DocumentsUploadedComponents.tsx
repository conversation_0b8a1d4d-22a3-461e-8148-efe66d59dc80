import { FC } from 'react';
import { FileText, CheckCircle, XCircle, Clock } from 'lucide-react';
import { BubbleTag } from '@/components/custom/BubbleTag';
import { Document } from '@/app/types';
import { Badge } from "@/components/ui/badge";
import { format } from 'date-fns';
import { motion } from 'framer-motion';

const itemVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: { opacity: 1, x: 0 }
};

export const DocumentItem: FC<{ document: Document }> = ({ document }) => {
  const getStatusConfig = () => {
    switch (document.status.toLowerCase()) {
      case 'approved':
        return { color: 'green' as const, text: 'Approved' };
      case 'rejected':
        return { color: 'red' as const, text: 'Rejected' };
      case 'pending':
        return { color: 'yellow' as const, text: 'Pending' };
      default:
        return { color: 'green' as const, text: document.status };
    }
  };

  const config = getStatusConfig();

  return (
    <motion.div 
      className="grid grid-cols-[auto_1fr_auto] gap-3 p-2 rounded-md"
      variants={itemVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="flex items-center">
        <FileText className="h-4 w-4" />
      </div>
      <div className="min-w-0 space-y-1">
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-xs">Document</Badge>
          <span className="text-sm text-gray-900">{document.document_type}</span>
        </div>
        <div className="text-xs text-gray-500">
          {format(new Date(document.date_of_upload), 'MMM dd, yyyy')}
        </div>
      </div>
      <div className="flex items-center">
        <BubbleTag
          text={config.text}
          color={config.color}
        />
      </div>
    </motion.div>
  );
};

export const DocumentArtifact: FC<{ document: Document }> = ({ document }) => {
  const getStatusIcon = () => {
    switch (document.status.toLowerCase()) {
      case 'approved':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'rejected':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      default:
        return <FileText className="h-5 w-5 text-gray-500" />;
    }
  };

  return (
    <motion.div 
      className="space-y-4"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex items-center gap-2">
        {getStatusIcon()}
        <h2 className="text-xl font-semibold">{document.document_type}</h2>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <p className="text-sm text-gray-500">Document Number</p>
          <p className="text-sm">{document.document_number}</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Status</p>
          <p className="text-sm capitalize">{document.status}</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Upload Date</p>
          <p className="text-sm">{format(new Date(document.date_of_upload), 'MMM dd, yyyy')}</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Created At</p>
          <p className="text-sm">{format(new Date(document.created_at), 'MMM dd, yyyy')}</p>
        </div>
      </div>
    </motion.div>
  );
};