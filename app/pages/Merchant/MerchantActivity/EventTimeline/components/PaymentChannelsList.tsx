import { FC } from 'react';
import { Globe, Smartphone, QrCode, CreditCard, Repeat, Link, FileText, Store } from 'lucide-react';
import { BubbleTag } from '@/components/custom/BubbleTag';
import { PaymentChannelType } from '@/app/types';
import { Badge } from "@/components/ui/badge";
import { format } from 'date-fns';
import { motion } from 'framer-motion';

const itemVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: { opacity: 1, x: 0 }
};

export const PaymentChannelItem: FC<{ channel: PaymentChannelType }> = ({ channel }) => {
  const getChannelIcon = () => {
    switch (channel.type) {
      case 'website': return <Globe className="h-5 w-5 text-blue-500" />;
      case 'app': return <Smartphone className="h-5 w-5 text-purple-500" />;
      case 'qr': return <QrCode className="h-5 w-5 text-green-500" />;
      case 'pos': return <CreditCard className="h-5 w-5 text-orange-500" />;
      case 'subscription': return <Repeat className="h-5 w-5 text-pink-500" />;
      case 'payment_link': return <Link className="h-5 w-5 text-cyan-500" />;
      case 'invoice': return <FileText className="h-5 w-5 text-yellow-500" />;
      case 'marketplace': return <Store className="h-5 w-5 text-indigo-500" />;
      default: return <Store className="h-5 w-5 text-indigo-500" />;
    }
  };

  return (
    <motion.div 
      className="grid grid-cols-[auto_1fr_auto] gap-3 p-2 rounded-md"
      variants={itemVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="flex items-center">
        {getChannelIcon()}
      </div>
      <div className="min-w-0 space-y-1">
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-xs">Channel</Badge>
          <span className="text-sm text-gray-900">{channel.name}</span>
        </div>
        <div className="text-xs text-gray-500">
          Added on {format(new Date(channel.added_on), 'MMM dd, yyyy')}
        </div>
      </div>
      <div className="flex items-center">
        <BubbleTag 
          text={channel.status === 'active' ? 'Active' : 'Inactive'}
          color={channel.status === 'active' ? 'green' : 'gray'}
        />
      </div>
    </motion.div>
  );
};

export const PaymentChannelArtifact: FC<{ channel: PaymentChannelType }> = ({ channel }) => {
  const getChannelIcon = () => {
    switch (channel.type) {
      case 'website': return <Globe className="h-5 w-5 text-blue-500" />;
      case 'app': return <Smartphone className="h-5 w-5 text-purple-500" />;
      case 'qr': return <QrCode className="h-5 w-5 text-green-500" />;
      case 'pos': return <CreditCard className="h-5 w-5 text-orange-500" />;
      case 'subscription': return <Repeat className="h-5 w-5 text-pink-500" />;
      case 'payment_link': return <Link className="h-5 w-5 text-cyan-500" />;
      case 'invoice': return <FileText className="h-5 w-5 text-yellow-500" />;
      case 'marketplace': return <Store className="h-5 w-5 text-indigo-500" />;
      default: return <Store className="h-5 w-5 text-indigo-500" />;
    }
  };

  return (
    <motion.div 
      className="space-y-4"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex items-center gap-2">
        {getChannelIcon()}
        <h2 className="text-xl font-semibold">{channel.name}</h2>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <p className="text-sm text-gray-500">Type</p>
          <p className="text-sm capitalize">{channel.type.replace('_', ' ')}</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Status</p>
          <p className="text-sm capitalize">{channel.status}</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Added On</p>
          <p className="text-sm">{format(new Date(channel.added_on), 'MMM dd, yyyy')}</p>
        </div>
        {/* {Object.entries(channel.details).map(([key, value]) => (
          <div key={key}>
            <p className="text-sm text-gray-500">{key}</p>
            <p className="text-sm">{value}</p>
          </div>
        ))} */}
      </div>
    </motion.div>
  );
};
