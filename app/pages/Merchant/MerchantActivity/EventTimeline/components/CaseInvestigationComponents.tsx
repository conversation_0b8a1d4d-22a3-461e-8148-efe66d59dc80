import { FC } from 'react';
import { FileSearch, AlertTriangle, Clock, CheckCircle } from 'lucide-react';
import { BubbleTag } from '@/components/custom/BubbleTag';
import { Badge } from "@/components/ui/badge";
import { format } from 'date-fns';
import { motion } from 'framer-motion';
import { CaseInvestigationType } from '@/app/types';

const itemVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: { opacity: 1, x: 0 }
};

export const CaseInvestigationItem: FC<{ investigation: CaseInvestigationType }> = ({ investigation }) => {
  const getPriorityConfig = () => {
    switch (investigation.priority.toLowerCase()) {
      case 'high':
        return { color: 'red' as const, text: 'High Priority' };
      case 'medium':
        return { color: 'yellow' as const, text: 'Medium Priority' };
      case 'low':
        return { color: 'blue' as const, text: 'Low Priority' };
      default:
        return { color: 'gray' as const, text: investigation.priority };
    }
  };

  const config = getPriorityConfig();

  return (
    <motion.div 
      className="grid grid-cols-[auto_1fr_auto] gap-3 p-2 rounded-md"
      variants={itemVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="flex items-center">
        <FileSearch className="h-4 w-4" />
      </div>
      <div className="min-w-0 space-y-1">
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-xs">Case #{investigation.case_number}</Badge>
          <span className="text-sm text-gray-900">{investigation.title}</span>
        </div>
        <div className="text-xs text-gray-500">
          {format(new Date(investigation.created_at), 'MMM dd, yyyy')}
        </div>
      </div>
      <div className="flex items-center">
        <BubbleTag
          text={config.text}
          color={config.color}
        />
      </div>
    </motion.div>
  );
};

export const CaseInvestigationArtifact: FC<{ investigation: CaseInvestigationType }> = ({ investigation }) => {
  const getStatusIcon = () => {
    switch (investigation.status.toLowerCase()) {
      case 'open':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'in progress':
        return <AlertTriangle className="h-5 w-5 text-orange-500" />;
      case 'closed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      default:
        return <FileSearch className="h-5 w-5 text-gray-500" />;
    }
  };

  return (
    <motion.div 
      className="space-y-4"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex items-center gap-2">
        {getStatusIcon()}
        <h2 className="text-xl font-semibold">Case Investigation #{investigation.case_number}</h2>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <p className="text-sm text-gray-500">Title</p>
          <p className="text-sm">{investigation.title}</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Status</p>
          <p className="text-sm capitalize">{investigation.status}</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Priority</p>
          <p className="text-sm capitalize">{investigation.priority}</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Assignee</p>
          <p className="text-sm">{investigation.assignee_Name || 'Unassigned'}</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Created At</p>
          <p className="text-sm">{format(new Date(investigation.created_at), 'MMM dd, yyyy')}</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">SLA Deadline</p>
          <p className="text-sm">{format(new Date(investigation.sla_deadline), 'MMM dd, yyyy')}</p>
        </div>
      </div>
    </motion.div>
  );
}; 