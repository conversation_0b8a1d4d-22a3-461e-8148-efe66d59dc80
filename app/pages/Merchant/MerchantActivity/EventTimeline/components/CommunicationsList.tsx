import { FC } from 'react';
import { Mail, Phone, MessageSquare } from 'lucide-react';
import { BubbleTag } from '@/components/custom/BubbleTag';
import { useMerchantIdStore } from '@/app/store/merchant/merchantIdStore';
import { Badge } from "@/components/ui/badge";
import { motion } from 'framer-motion';



// Communication Interface
export interface CommunicationType {
  subject: string;
  sender_id: string;
  content: string;
  created_at: string;
  id: string;
  type: string;
  receiver_id: string;
  timestamp: string;
}

const itemVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: { opacity: 1, x: 0 }
};

export const CommunicationItem: FC<{ communication: CommunicationType }> = ({ communication }) => {
  const { selectedMerchantId } = useMerchantIdStore();

  const getTypeConfig = () => {
    switch (communication.type) {
      case 'email':
        return {
          icon: <Mail className="h-4 w-4" />,
          color: 'blue' as const,
          text: 'Email'
        };
      case 'phone':
        return {
          icon: <Phone className="h-4 w-4" />,
          color: 'green' as const,
          text: 'Phone'
        };
      case 'chat':
        return {
          icon: <MessageSquare className="h-4 w-4" />,
          color: 'purple' as const,
          text: 'Chat'
        };
      default:
        return {
          icon: <Mail className="h-4 w-4" />,
          color: 'gray' as const,
          text: communication.type
        };
    }
  };

  const config = getTypeConfig();

  return (
    <motion.div 
      className="grid grid-cols-[auto_1fr_auto] gap-3 p-2 rounded-md"
      variants={itemVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="flex items-center">
        {config.icon}
      </div>
      <div className="min-w-0 space-y-1">
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-xs">Communication</Badge>
          <span className="text-sm text-gray-900">{communication.subject}</span>
        </div>
        <div className="text-xs text-gray-500 truncate">
          {communication.content}
        </div>
      </div>
      <div className="flex items-center">
        <BubbleTag
          text={communication.sender_id === selectedMerchantId ? 'Sent' : 'Received'}
          color={communication.sender_id === selectedMerchantId ? 'blue' : 'green'}
        />
      </div>
    </motion.div>
  );
};

export const CommunicationArtifact: FC<{ communication: CommunicationType }> = ({ communication }) => {
  const getTypeConfig = () => {
    switch (communication.type) {
      case 'email':
        return { icon: <Mail className="h-5 w-5" />, color: 'text-blue-500' };
      case 'phone':
        return { icon: <Phone className="h-5 w-5" />, color: 'text-green-500' };
      case 'chat':
        return { icon: <MessageSquare className="h-5 w-5" />, color: 'text-purple-500' };
      default:
        return { icon: <Mail className="h-5 w-5" />, color: 'text-gray-500' };
    }
  };

  const config = getTypeConfig();

  return (
    <motion.div 
      className="space-y-4"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex items-center gap-2">
        <div className={config.color}>{config.icon}</div>
        <h2 className="text-xl font-semibold">{communication.subject}</h2>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <p className="text-sm text-gray-500">Type</p>
          <p className="text-sm capitalize">{communication.type}</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Status</p>
          <p className="text-sm capitalize">{communication.sender_id}</p>
          <p className="text-sm capitalize">{communication.receiver_id}</p>
        </div>
        <div className="col-span-2">
          <p className="text-sm text-gray-500">Content</p>
          <p className="text-sm">{communication.content}</p>
        </div>
      </div>
    </motion.div>
  );
};
