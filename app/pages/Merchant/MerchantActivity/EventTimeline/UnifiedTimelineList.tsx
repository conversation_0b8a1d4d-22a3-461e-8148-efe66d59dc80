import { FC, useState, useMemo, useEffect } from 'react';
import { VirtualList, VirtualListItemProps } from '@/components/custom/VirtualList';
import { MultiSelect } from '@/components/ui/multi-select2';
import { useActivityEventTimelineStore } from '@/app/store/merchant/activityEventTimelineStore';
import { CreditCard, MessageSquare, Flag, FileText, RefreshCw, FileSearch } from 'lucide-react';
import { useMerchantIdStore } from '@/app/store/merchant/merchantIdStore';
import { useInvestigationRedFlagsStore } from '@/app/store/merchant/InvestigationRedFlagsStore';
import { Button } from "@/components/ui/button";
import { format } from 'date-fns';
import { useArtifactStore } from '@/app/store/artifact/artifactStore';
import { motion } from 'framer-motion';
import { useCaseManagementStore } from '@/app/store/caseManagement/QueueManagerStore';
import {
  PaymentChannelItem,
  PaymentChannelArtifact,
  CommunicationItem,
  CommunicationArtifact,
  FlagItem,
  FlagArtifact,
  DocumentItem,
  DocumentArtifact,
  CaseInvestigationItem,
  CaseInvestigationArtifact
} from './components';

interface UnifiedTimelineProps {
  onTimelineClick?: (item: VirtualListItemProps) => void;
}

interface UnifiedItem {
  id: string;
  timestamp: string;
  type: 'channel' | 'communication' | 'flag' | 'document' | 'investigation';
  content: React.ReactNode;
  metadata: any;
  title: string;
}

// Add animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 }
};

export const UnifiedTimelineList: FC<UnifiedTimelineProps> = ({ onTimelineClick }) => {
  const { paymentChannels, communications, documentsUploaded, fetchPaymentChannels, fetchCommunications, fetchDocumentsUploaded } = useActivityEventTimelineStore();
  const { flagsList, fetchFlagsList } = useInvestigationRedFlagsStore();
  const { caseInvestigations } = useCaseManagementStore();
  const { selectedMerchantId } = useMerchantIdStore();

  useEffect(() => {
    console.log("UnifiedTimelineList mounted");
    if (selectedMerchantId) {
      fetchPaymentChannels(selectedMerchantId);
      fetchCommunications(selectedMerchantId);
      fetchFlagsList(selectedMerchantId);
      fetchDocumentsUploaded(selectedMerchantId);
    }
  }, [fetchPaymentChannels, fetchCommunications, fetchFlagsList, fetchDocumentsUploaded, selectedMerchantId]);

  // const { setArtifact } = useWorkspace();
  const { addTab, setActiveTabId, setCollapsed } = useArtifactStore();
  const [selectedTypes, setSelectedTypes] = useState<string[]>([
    'channels', 'communications', 'flags', 'documents'
  ]);

  const filterOptions = [
    { value: 'channels', label: 'Payment Channels', icon: CreditCard },
    { value: 'communications', label: 'Communications', icon: MessageSquare },
    { value: 'flags', label: 'Red Flags', icon: Flag },
    { value: 'documents', label: 'Documents', icon: FileText },
    { value: 'investigations', label: 'Case Investigations', icon: FileSearch }
  ];

  const unifiedItems: UnifiedItem[] = useMemo(() => {
    const items: UnifiedItem[] = [];

    if (selectedTypes.includes('channels')) {
      items.push(...(paymentChannels.payment_channels || []).map(channel => ({
        id: channel.id,
        timestamp: format(new Date(channel.added_on), 'MMM dd, yyyy'),
        type: 'channel' as const,
        content: <PaymentChannelItem channel={channel} />,
        metadata: {
          type: 'channel',
          component: <PaymentChannelArtifact channel={channel} />,
          title: channel.name
        },
        title: channel.name
      })));
    }

    if (selectedTypes.includes('communications')) {
      items.push(...(communications?.communications || []).map(comm => ({
        id: comm.id,
        timestamp: format(new Date(comm.timestamp), 'MMM dd, yyyy'),
        type: 'communication' as const,
        content: <CommunicationItem communication={comm} />,
        metadata: {
          type: 'communication',
          component: <CommunicationArtifact communication={comm} />,
          title: comm.subject
        },
        title: comm.subject
      })));
    }

    if (selectedTypes.includes('flags')) {
      items.push(...(flagsList || []).map(flag => ({
        id: flag.id,
        timestamp: format(new Date(flag.created_at), 'MMM dd, yyyy'),
        type: 'flag' as const,
        content: <FlagItem flag={{
          id: flag.id,
          text: flag.description,
          severity: flag.severity,
          timestamp: flag.created_at,
          description: flag.rule_description
        }} />,
        metadata: {
          type: 'flag',
          component: <FlagArtifact flag={{
            id: flag.id,
            text: flag.description,
            severity: flag.severity,
            timestamp: flag.created_at,
            description: flag.rule_description
          }} />,
          title: flag.rule_type
        },
        title: flag.rule_type
      })));
    }

    if (selectedTypes.includes('documents')) {
      items.push(...(documentsUploaded?.documents_uploaded || []).map(doc => ({
        id: doc.id,
        timestamp: format(new Date(doc.date_of_upload), 'MMM dd, yyyy'),
        type: 'document' as const,
        content: <DocumentItem document={doc} />,
        metadata: {
          type: 'document',
          component: <DocumentArtifact document={doc} />,
          title: doc.document_type
        },
        title: doc.document_type
      })));
    }

    if (selectedTypes.includes('investigations')) {
      items.push(...(caseInvestigations?.investigations || []).map(investigation => ({
        id: investigation.investigation_id,
        timestamp: format(new Date(investigation.created_at), 'MMM dd, yyyy'),
        type: 'investigation' as const,
        content: <CaseInvestigationItem investigation={investigation} />,
        metadata: {
          type: 'investigation',
          component: <CaseInvestigationArtifact investigation={investigation} />,
          title: `Case #${investigation.case_number}`
        },
        title: `Case #${investigation.case_number}`
      })));
    }

    return items.sort((a, b) =>
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
  }, [selectedTypes, paymentChannels.payment_channels, communications?.communications, flagsList, documentsUploaded?.documents_uploaded, caseInvestigations?.investigations]);

  const handleItemClick = (item: VirtualListItemProps) => {
    addTab({
      id: item.id,
      title: item.title,
      renderArtifact: () => item.metadata.component
    });
    setActiveTabId(item.id);
    setCollapsed(false);
  };

  const handleRefresh = () => {
    if (selectedMerchantId) {
      fetchPaymentChannels(selectedMerchantId);
      fetchCommunications(selectedMerchantId);
      fetchFlagsList(selectedMerchantId);
      fetchDocumentsUploaded(selectedMerchantId);
      console.log("Refreshed");
    }
  };

  return (
    <div className="space-y-8">
      <div className="flex gap-2 items-center">
        <MultiSelect
          options={filterOptions}
          onValueChange={setSelectedTypes}
          defaultValue={selectedTypes}
          placeholder="Select types to show..."
          className="flex-1"
        />
      </div>

      <VirtualList
        items={unifiedItems}
        onItemClick={handleItemClick}
        itemHeight={72}
        viewportHeight={640}
        itemsPerPage={20}
      />
    </div>
  );
}; 