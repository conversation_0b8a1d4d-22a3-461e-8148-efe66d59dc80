import { FC, useEffect } from 'react';
import { motion } from 'framer-motion';
import { AlertTriangle } from 'lucide-react';
import { useRiskMetricsStore } from '@/app/store/merchant/riskMetricsStore';

interface ProbabilityOfDefaultProps {
  merchantId: string;
}

const ProbabilityOfDefault: FC<ProbabilityOfDefaultProps> = ({ merchantId }) => {
  const { metrics, loading, error, fetchRiskMetrics } = useRiskMetricsStore();

  useEffect(() => {
    if (merchantId) {
      fetchRiskMetrics(merchantId);
    }
  }, [merchantId, fetchRiskMetrics]);

  // Generate histogram data for 0-100% in 5% increments with a declining trend
  const histogramData = Array.from({ length: 20 }, (_, i) => {
    const start = i * 5;
    const end = start + 5;
    const count = Math.round(450 * Math.exp(-0.25 * i));
    return {
      bucket: `${start}%`, // Only show lower bound
      count,
      pdRange: [start, end],
    };
  });

  
  const getRiskSegment = (risk: string | null | undefined) => {
    if (!risk) return { label: 'Medium', color: 'text-yellow-600' };
    switch (risk.toLowerCase()) {
      case 'low risk': return { label: 'Low', color: 'text-green-600' };
      case 'medium risk': return { label: 'Medium', color: 'text-yellow-600' };
      case 'high risk': return { label: 'High', color: 'text-red-600' };
      default: return { label: 'Medium', color: 'text-yellow-600' };
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error || !metrics) {
    return (
      <div className="p-4 border border-gray-200 rounded-lg bg-white shadow-sm">
        <div className="flex items-center gap-2 text-amber-600">
          <AlertTriangle size={18} />
          <p>Error loading risk metrics data. Risk assessment may not be available for this merchant.</p>
        </div>
      </div>
    );
  }

  // Safely access data with defaults to prevent null/undefined errors
  const riskSegment = getRiskSegment(metrics?.risk_segmentation || 'Medium Risk');
  const pdScore = typeof metrics?.pd_score === 'number'
    ? Number(Math.abs(metrics.pd_score).toFixed(2))
    : 0;
  const lgd = typeof metrics?.loss_given_default === 'number'
    ? Number(Math.abs(metrics.loss_given_default * 100).toFixed(2))
    : 0;
  const ead = typeof metrics?.exposure_at_default === 'number'
    ? Number(metrics.exposure_at_default.toFixed(2))
    : 0;
  const expectedLoss = typeof metrics?.expected_loss === 'number'
    ? Number(metrics.expected_loss.toFixed(2))
    : 0;
  const average_days_deferred = typeof metrics?.average_days_deferred === 'number'
    ? Number(metrics.average_days_deferred.toFixed(2))
    : 0;
  const average_daily_transactions = 1000000; // 10L as shown in UI
  const expectedLossatDefault = Number(((lgd/100) * average_daily_transactions * average_days_deferred).toFixed(2));


  return (
    <div className="space-y-6 pt-3">
      <motion.div 
        className="space-y-6 bg-white rounded-lg p-6 shadow-sm border border-gray-100"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {/* PD Score, Risk Segmentation, and Histogram */}
        <div className="grid grid-cols-7 gap-4">
          <div className="flex flex-col justify-between col-span-4">
            {/* PD Score */}
            <div className="space-y-2">
              <p className="text-sm text-gray-600">Default Risk Segmentation</p>
              <div className="flex items-baseline gap-2">
                <span className={`text-3xl font-bold ${riskSegment.color}`}>
                  {riskSegment.label} Risk
                </span>
              </div>
              <div className="mt-1 flex items-center gap-1 text-xs text-gray-500">
                <span>PD Score: <span className="font-medium text-gray-700">{pdScore}%</span></span>
                <span>Model Version: <span className="font-medium text-gray-700">v1.0.2</span></span>
                <span>Assessment Date: <span className="font-medium text-gray-700">2025-05-08</span></span>
              </div>
            </div>

            {/* Risk Segmentation */}
            <div className="space-y-2">
              <p className="text-sm text-gray-600">Risk Segmentation</p>
              <div className="flex gap-4">
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 rounded-full bg-green-600" />
                  <span className="text-sm">Low (0-5%)</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 rounded-full bg-yellow-600" />
                  <span className="text-sm">Medium (5-15%)</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 rounded-full bg-red-600" />
                  <span className="text-sm">High (15%+)</span>
                </div>
              </div>
            </div>
          </div>

          {/* Histogram */}
          <div className="space-y-2 col-span-3">
            <p className="text-sm text-gray-600">PD Score Distribution</p>
            <div className="h-40 flex items-end gap-1 w-full">
              {histogramData.map((item, index) => {
                const isCurrentBucket = pdScore >= item.pdRange[0] && pdScore < item.pdRange[1];
                const getBarColor = () => {
                  if (!isCurrentBucket) return 'bg-gray-200 hover:bg-gray-300';
                  if (item.pdRange[0] < 5) return 'bg-green-500 hover:bg-green-600';
                  if (item.pdRange[0] < 10) return 'bg-yellow-500 hover:bg-yellow-600';
                  return 'bg-red-500 hover:bg-red-600';
                };
                return (
                  <div key={index} className="flex-1 flex flex-col-reverse items-center group h-full">
                    <span className="text-xs text-gray-500 mt-1">{index === histogramData.length - 1 ? '100%' : item.bucket}</span>
                    <div
                      className={`w-full transition-colors relative ${getBarColor()}`}
                      style={{ height: `${(item.count / 450) * 100}%`, minHeight: '8px' }}
                    >
                    </div>
                    <div className="absolute bottom-full mb-2 hidden group-hover:block bg-gray-800 text-white text-xs rounded px-2 py-1">
                      {item.count} companies
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* LGD, EAD, and Expected Loss */}
        <div className="grid grid-cols-4 gap-6 pt-4 border-t">
          <div className="space-y-2">
            <p className="text-sm text-gray-600">Loss Given Default (LGD)</p>
            <p className="text-xl font-semibold">{lgd}%</p>
            <p className="text-xs text-gray-500 italic">1 - (Assets Available / Amount Owed) to Operational Creditors</p>
          </div>
          <div className="space-y-2">
            <p className="text-sm text-gray-600">Average Daily Transactions (ADT)</p>
            <p className="text-xl font-semibold">{Number(average_daily_transactions).toLocaleString()}</p>
            <p className="text-xs text-gray-500 italic">Average Daily Transactions at for past 365 days</p>
          </div>
          <div className="space-y-2">
            <p className="text-sm text-gray-600">Average Delivery Days (ADD)</p>
            <p className="text-xl font-semibold text-red-600">
              {Number(average_days_deferred).toLocaleString()}
            </p>
            <p className="text-xs text-gray-500 italic">(Unearned Revenue/ Total Revenue) * 365</p>
          </div>
          <div className="space-y-2">
            <p className="text-sm text-gray-600">Expected Loss at Default (ELD)</p>
            <p className="text-xl font-semibold text-red-600">
              ₹{Number(expectedLossatDefault).toLocaleString()}
            </p>
            <p className="text-xs text-gray-500 italic">LGD * ADT * ADD</p>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default ProbabilityOfDefault;
