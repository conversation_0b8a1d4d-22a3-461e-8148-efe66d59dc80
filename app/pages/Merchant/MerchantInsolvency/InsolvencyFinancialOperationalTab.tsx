'use client';

import { FC, useState, useEffect, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Visualization } from '@/components/custom/visualization';
import { VisualizationData } from '@/components/custom/visualization/types';
import FinancialStatement from './Components/FinancialStatement';
import { 
  revenueTrendConfig,
  profitTrendConfig,
  cashFlowTrendConfig
} from './SampleData/sampleData';
import { BarChart3, LineChart } from 'lucide-react';
import SectionHeaderWithRedFlags from '@/components/custom/SectionHeaderWithRedFlags';
import { KeyMetrics } from '@/components/custom/KeyMetrics';
import { useMerchantIdStore } from '@/app/store/merchant/merchantIdStore';
import { useActiveContext } from '@/app/layout/ActiveContext/useActiveContext';
import { useInsolvencyFinancialStore } from '@/app/store/merchant/insolvencyFinancialStore';
import { API } from '@/app/services/axios';
import { useInvestigationRedFlagsStore } from '@/app/store/merchant/InvestigationRedFlagsStore';
import { useInsolvencyRedFlags } from './InsolvencyRedFlagsTab';

interface FinancialMetric {
  label: string;
  value: number;
  icon: string;
}

const InsolvencyFinancialOperationalTab: FC = () => {
  const [isMetricsExpanded, setIsMetricsExpanded] = useState(false);
  const [financialMetrics, setFinancialMetrics] = useState<FinancialMetric[]>([]);
  const { financialsData } = useInsolvencyFinancialStore();
  const { selectedMerchantId } = useMerchantIdStore();
  const { activeContexts } = useActiveContext();
  const merchantId = useMemo(() => activeContexts?.merchant || selectedMerchantId, [activeContexts, selectedMerchantId]);
  const { fetchFlagsList } = useInvestigationRedFlagsStore();
  const flagsList = useInsolvencyRedFlags();
  
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  const revenueData = useMemo(() => {
    if (!financialsData?.data || financialsData.data.length === 0) {
      return [];
    }

    return financialsData.data.map(item => {
      // Extract year from the date
      const year = new Date(item.year).getFullYear().toString();
      
      return {
        name: year,
        year: year,
        revenue: item.revenue_from_operations || 0,
        profit: item.profit_after_tax || 0,
        cashFlow: item.operating_profit || 0
      };
    }).sort((a, b) => parseInt(a.year) - parseInt(b.year));
  }, [financialsData]);

  useEffect(() => {
    const fetchFinancialMetrics = async () => {
      if (!selectedMerchantId) return;
      
      try {
        const { data } = await API.get(`/api/v1/merchants/${selectedMerchantId}/financial-metrics`);
        setFinancialMetrics(data);
      } catch (error) {
        console.error('Error fetching financial metrics:', error);
      }
    };

    fetchFinancialMetrics();
  }, [selectedMerchantId]);

  useEffect(() => {
    if (merchantId) {
      fetchFlagsList(merchantId);
    }
  }, [merchantId, fetchFlagsList]);

  return (
    <motion.div 
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div variants={itemVariants}>
        <SectionHeaderWithRedFlags 
          redFlags={flagsList}
          title="Financial Metrics"
          icon={LineChart}
          iconColorClass="text-blue-600"
          redFlag_recepient_id="insolvency_financial_financialMetrics"
        />
      </motion.div>
      
      <motion.div variants={itemVariants}>
        <KeyMetrics
          keyMetricList={{ key_metrics: financialMetrics }}
          isMetricsExpanded={isMetricsExpanded}
          setIsMetricsExpanded={setIsMetricsExpanded}
          showHeader={false}
        />
      </motion.div>

      <div className="grid grid-cols-3 gap-4">
        <motion.div variants={itemVariants}>
          <Visualization
            {...revenueTrendConfig}
            data={revenueData}
            title="Revenue Trend"
            isEnclosedInCard={true}
            showCAGRview={true}
          />
        </motion.div>

        <motion.div variants={itemVariants}>
          <Visualization
            {...profitTrendConfig}
            data={revenueData}
            title="Profit Trend"
            isEnclosedInCard={true}
            showCAGRview={true}
          />
        </motion.div>

        <motion.div variants={itemVariants}>
          <Visualization
            {...cashFlowTrendConfig}
            data={revenueData}
            title="Cash Flow Trend"
            isEnclosedInCard={true}
            showCAGRview={true}
          />
        </motion.div>
      </div>

      <motion.div variants={itemVariants}>
        <SectionHeaderWithRedFlags 
          redFlags={flagsList}
          title="Financial Statements"
          icon={BarChart3}
          iconColorClass="text-blue-600"
          redFlag_recepient_id="insolvency_financial_financialStatements"
        />
        <div className="mt-4">
          <FinancialStatement />
        </div>
      </motion.div>
    </motion.div>
  );
};

export default InsolvencyFinancialOperationalTab;
