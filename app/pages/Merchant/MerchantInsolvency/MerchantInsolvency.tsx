'use client';

import { FC } from 'react';
import { Workspace } from '@/app/layout/Workspace/Workspace';
import InsolvencyOverviewTab from './InsolvencyOverviewTab';
import InsolvencyRedFlagsTab from './InsolvencyRedFlagsTab';
import InsolvencyFinancialOperationalTab from './InsolvencyFinancialOperationalTab';
import InsolvencyExternalInsightsTab from './InsolvencyExternalInsightsTab';

const MerchantInsolvency: FC = () => {
  const tabs = [
    {
      id: 'overview',
      label: 'Overview',
      content: <InsolvencyOverviewTab />
    },
    {
      id: 'red-flags',
      label: 'Red Flags',
      content: <InsolvencyRedFlagsTab />
    },
    {
      id: 'financial-operational',
      label: 'Financial & Operational',
      content: <InsolvencyFinancialOperationalTab />
    },
    {
      id: 'external-insights',
      label: 'External Insights',
      content: <InsolvencyExternalInsightsTab />
    },
  ];

  return <Workspace tabs={tabs} />;
};

export default MerchantInsolvency;
