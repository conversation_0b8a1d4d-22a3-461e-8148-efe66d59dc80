'use client';

import { FC, useState, useEffect, useMemo } from 'react';
import { 
  Globe, Newspaper, Search, ChevronDown, ChevronUp, 
  Shield, Users, AlertTriangle, Smile, DollarSign, LineChart, FileText,
  ExternalLink, Calendar, Link2, Loader2, Clipboard, FileBarChart
} from 'lucide-react';
import SectionHeaderWithRedFlags from '@/components/custom/SectionHeaderWithRedFlags';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { useInsolvencyExternalInsightsStore } from '@/app/store/merchant/insolvencyExternalInsightsStore';
import { useMerchantIdStore } from '@/app/store/merchant/merchantIdStore';

// Section configuration with icons
const sectionConfig = {
  legal_regulatory_compliance: {
    title: "Legal & Regulatory Issues",
    icon: Shield,
    iconColorClass: "text-red-600"
  },
  executive_workforce_developments: {
    title: "Executive & Workforce Developments",
    icon: Users,
    iconColorClass: "text-blue-600"
  },
  operational_disruptions: {
    title: "Operational Incidents",
    icon: AlertTriangle,
    iconColorClass: "text-amber-600"
  },
  sentiment_brand_reputation: {
    title: "Brand & Reputational Signals",
    icon: Smile,
    iconColorClass: "text-purple-600"
  },
  financial_warning_signs: {
    title: "Financial Warning Signs",
    icon: DollarSign,
    iconColorClass: "text-emerald-600"
  },
  industry_macroeconomic: {
    title: "Industry & Macro Pressures",
    icon: LineChart,
    iconColorClass: "text-indigo-600"
  },
  financial_disclosures: {
    title: "Financial Disclosures Insights",
    icon: FileText,
    iconColorClass: "text-teal-600"
  },
  audit_report_insights: {
    title: "Insights from Audit Report",
    icon: Clipboard,
    iconColorClass: "text-orange-600"
  },
  annual_report_insights: {
    title: "Insights from Annual Report",
    icon: FileBarChart,
    iconColorClass: "text-pink-600"
  }
};

// Format date from API (MM-DD-YYYY) to more readable format
const formatDate = (dateString: string) => {
  const [day, month, year] = dateString.split("-");
  const months = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ];
  return `${parseInt(day)} ${months[parseInt(month) - 1]} ${year}`;
};

// Convert insight issues to red flags format
const mapInsightsToRedFlags = (insights: any[], category: string, severity: 'critical' | 'high' | 'medium') => {
  return insights.map((insight, index) => ({
    id: `${category}-${index}`,
    text: insight.title,
    severity,
    category,
    description: insight.summary
  }));
};

interface IssueCardProps {
  item: any;
  index: number;
}

const IssueCard: FC<IssueCardProps> = ({ item, index }) => {
  // Helper function to extract domain from URL
  const extractDomain = (url: string) => {
    try {
      const domain = new URL(url).hostname.replace('www.', '');
      return domain;
    } catch {
      return url;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-4 hover:shadow-md transition-shadow duration-200">
      <div className="p-4">
        <div className="flex justify-between items-center mb-3">
          <h4 className="text-gray-900">{item.summary}</h4>
          <div className="flex items-center text-xs text-gray-500">
            <Calendar className="h-3.5 w-3.5 mr-1.5" />
            {formatDate(item.date)}
          </div>
        </div>
        
        <div className="bg-gray-50 rounded-md p-3 text-sm text-gray-700">
          <p>
            {item.legal_issue || item.executive_issue || item.operational_issue || 
             item.sentiment_issue || item.financial_issue || item.industry_issue}
            {item.source_urls && item.source_urls.length > 0 && (
              <span className="text-xs text-gray-500 ml-1">
                [Sources: {item.source_urls.map((url: string, i: number) => (
                  <span key={i}>
                    {i > 0 && ', '}
                    <a 
                      href={url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      {extractDomain(url)}
                    </a>
                  </span>
                ))}]
              </span>
            )}
          </p>
        </div>
      </div>
    </div>
  );
};

// Section selector component
interface SectionSelectorProps {
  sections: string[];
  activeSection: string;
  onChange: (section: string) => void;
  sectionConfig: any;
}

// Get primary sections only to avoid overcrowding
const getPrimaryCategories = () => {
  return [
    'legal_regulatory_compliance',
    'financial_warning_signs', 
    'operational_disruptions',
    'industry_macroeconomic',
    'audit_report_insights',
    'annual_report_insights'
  ];
};

const SectionSelector: FC<SectionSelectorProps> = ({ 
  sections, 
  activeSection, 
  onChange,
  sectionConfig
}) => {
  const primaryCategories = getPrimaryCategories();
  
  // Create a shorter display name for each section
  const getShortName = (key: string) => {
    switch(key) {
      case 'legal_regulatory_compliance': return 'Legal';
      case 'executive_workforce_developments': return 'Executive'; 
      case 'operational_disruptions': return 'Operations';
      case 'sentiment_brand_reputation': return 'Brand';
      case 'financial_warning_signs': return 'Financial';
      case 'industry_macroeconomic': return 'Industry';
      case 'financial_disclosures': return 'Disclosures';
      case 'audit_report_insights': return 'Audit';
      case 'annual_report_insights': return 'Annual';
      default: return key;
    }
  };
  
  // Get count of items for each section from the external data
  const { externalData, auditReportInsights, annualReportInsights } = useInsolvencyExternalInsightsStore();
  
  // Calculate counts based on the current data structure
  const getCounts = () => {
    const counts: Record<string, number> = {};
    
    if (externalData?.data && externalData.data.length > 0) {
      const insightRecord = externalData.data.find(item => item.insight_type === 'external_data');
      
      if (insightRecord?.insight_value) {
        // Type safe way to handle insight values
        const insightValue = insightRecord.insight_value;
        
        if (Array.isArray(insightValue.legal_regulatory_compliance)) {
          counts['legal_regulatory_compliance'] = insightValue.legal_regulatory_compliance.length;
        }
        
        if (Array.isArray(insightValue.executive_workforce_developments)) {
          counts['executive_workforce_developments'] = insightValue.executive_workforce_developments.length;
        }
        
        if (Array.isArray(insightValue.operational_disruptions)) {
          counts['operational_disruptions'] = insightValue.operational_disruptions.length;
        }
        
        if (Array.isArray(insightValue.sentiment_brand_reputation)) {
          counts['sentiment_brand_reputation'] = insightValue.sentiment_brand_reputation.length;
        }
        
        if (Array.isArray(insightValue.financial_warning_signs)) {
          counts['financial_warning_signs'] = insightValue.financial_warning_signs.length;
        }
        
        if (Array.isArray(insightValue.industry_macroeconomic)) {
          counts['industry_macroeconomic'] = insightValue.industry_macroeconomic.length;
        }
        
        if (Array.isArray(insightValue.financial_disclosures)) {
          counts['financial_disclosures'] = insightValue.financial_disclosures.length;
        }
      }
    }
    
    // Add counts for audit report insights with new structure
    if (auditReportInsights?.data && Array.isArray(auditReportInsights.data)) {
      const auditRecord = auditReportInsights.data.find(item => item.insight_type === 'audit_flags');
      if (auditRecord && Array.isArray(auditRecord.insight_value)) {
        counts['audit_report_insights'] = auditRecord.insight_value.length;
      }
    }
    
    // Update for the new annual report insights structure
    if (annualReportInsights?.data && Array.isArray(annualReportInsights.data)) {
      const annualRecord = annualReportInsights.data.find(item => item.insight_type === 'annual_report_insights');
      if (annualRecord && Array.isArray(annualRecord.insight_value)) {
        counts['annual_report_insights'] = annualRecord.insight_value.length;
      }
    }
    
    return counts;
  };
  
  const counts = getCounts();
  
  return (
    <div className="mb-6">
      <Tabs value={activeSection} onValueChange={onChange} className="w-auto inline-block">
        <div className="overflow-x-auto">
          <TabsList className="bg-gray-100 inline-flex rounded-lg">
            {/* First show primary categories */}
            {primaryCategories.map(section => {
              const config = sectionConfig[section];
              const isActive = activeSection === section;
              const count = counts[section] || 0;
              return (
                <TabsTrigger 
                  key={section} 
                  value={section}
                  className={`px-3 py-1.5 text-xs flex items-center justify-center gap-1.5 min-w-[70px] ${isActive ? '!text-blue-600' : ''}`}
                  style={{ color: isActive ? '#2563eb' : '' }}
                >
                  <config.icon className={`h-3.5 w-3.5 ${isActive ? 'text-blue-600' : ''}`} />
                  <span className={`hidden sm:inline text-xs ${isActive ? '!text-blue-600' : ''}`}>
                    {getShortName(section)}
                    {count > 0 && <span className="ml-1 px-1.5 py-0.5 bg-gray-200 rounded-full text-xs">{count}</span>}
                  </span>
                </TabsTrigger>
              );
            })}
            
            {/* Then show remaining categories */}
            {sections.filter(s => !primaryCategories.includes(s)).map(section => {
              const config = sectionConfig[section];
              const isActive = activeSection === section;
              const count = counts[section] || 0;
              
              return (
                <TabsTrigger 
                  key={section} 
                  value={section}
                  className={`px-3 py-1.5 text-xs flex items-center justify-center gap-1.5 min-w-[70px] ${isActive ? '!text-blue-600' : ''}`}
                  style={{ color: isActive ? '#2563eb' : '' }}
                >
                  <config.icon className={`h-3.5 w-3.5 ${isActive ? 'text-blue-600' : ''}`} />
                  <span className={`hidden sm:inline text-xs ${isActive ? '!text-blue-600' : ''}`}>
                    {getShortName(section)}
                    {count > 0 && <span className="ml-1 px-1.5 py-0.5 bg-gray-200 rounded-full text-xs">{count}</span>}
                  </span>
                </TabsTrigger>
              );
            })}
          </TabsList>
        </div>
      </Tabs>
    </div>
  );
};

// Define interfaces for the new data structures
interface AuditConcern {
  year: string;
  title: string;
  summary: string;
  audit_concern: string;
}

interface AnnualReportInsight {
  tag: string;
  year: string;
  insight: string;
  filename: string;
  bare_text?: string;
  page_number?: string;
}

// Specialized card components for each insight type
const AuditReportCard: FC<{ item: AuditConcern, index: number }> = ({ item, index }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-4 hover:shadow-md transition-shadow duration-200">
      <div className="p-4">
        <div className="flex justify-between items-center mb-3">
          <h4 className="text-gray-900 font-medium">{item.title}</h4>
          <div className="flex items-center text-xs text-gray-500 font-medium">
            <Calendar className="h-3.5 w-3.5 mr-1.5" />
            FY {item.year}
          </div>
        </div>
        
        <div className="bg-gray-50 rounded-md p-3 text-sm text-gray-700">
          <p className="mb-2 font-medium text-orange-700">{item.summary}</p>
          <p className="text-gray-600">{item.audit_concern}</p>
        </div>
      </div>
    </div>
  );
};

const AnnualReportCard: FC<{ item: AnnualReportInsight, index: number }> = ({ item, index }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-4 hover:shadow-md transition-shadow duration-200">
      <div className="p-4">
        <div className="flex justify-between items-center mb-3">
          <div className="flex items-center">
            {item.tag === 'redflag' && (
              <span className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-red-100 text-red-600 mr-2">
                <AlertTriangle className="h-3.5 w-3.5" />
              </span>
            )}
            <h4 className="text-gray-900 font-medium">
              {item.filename ? item.filename.replace('AnnualReport_', '').replace('.pdf', '') : 'Annual Report'} 
              {item.year && <span className="ml-2 text-sm text-gray-500">FY {item.year}</span>}
            </h4>
          </div>
          {item.page_number && (
            <div className="flex items-center text-xs text-gray-500 font-medium">
              <FileText className="h-3.5 w-3.5 mr-1.5" />
              Page {parseInt(item.page_number)}
            </div>
          )}
        </div>
        
        <div className="bg-gray-50 rounded-md p-3 text-sm text-gray-700">
          <p className="text-gray-800">{item.insight}</p>
          {item.bare_text && (
            <div className="mt-2 p-2 bg-gray-100 rounded text-xs text-gray-600 italic">
              "{item.bare_text}"
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Modified InsolvencyExternalInsightsTab component
const InsolvencyExternalInsightsTab: FC = () => {
  // Set default section to the first primary category
  const primaryCategories = getPrimaryCategories();
  const [activeSection, setActiveSection] = useState<string>(primaryCategories[0]);
  
  // Get merchant ID and fetch external data
  const { selectedMerchantId } = useMerchantIdStore();
  const { 
    externalData, 
    loading, 
    error, 
    fetchExternalData,
    auditReportInsights,
    annualReportInsights,
    auditReportLoading,
    annualReportLoading,
    auditReportError,
    annualReportError,
    fetchAuditReportInsights,
    fetchAnnualReportInsights
  } = useInsolvencyExternalInsightsStore();
  
  // Fetch external data when the component mounts or when the merchant ID changes
  useEffect(() => {
    if (selectedMerchantId) {
      fetchExternalData(selectedMerchantId);
      fetchAuditReportInsights(selectedMerchantId);
      fetchAnnualReportInsights(selectedMerchantId);
    }
  }, [selectedMerchantId, fetchExternalData, fetchAuditReportInsights, fetchAnnualReportInsights]);
  
  // Show loading state while fetching data
  if (loading || auditReportLoading || annualReportLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }
  
  // Show error state if there's an error
  if (error || auditReportError || annualReportError) {
    return (
      <div className="flex flex-col justify-center items-center h-64">
        <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
        <p className="text-red-500 font-medium">Error fetching external data</p>
        <p className="text-gray-500 text-sm mt-2">{error || auditReportError || annualReportError}</p>
      </div>
    );
  }
  
  // Show empty state if there's no data
  if (!externalData || !externalData.data || externalData.data.length === 0) {
    return (
      <div className="flex flex-col justify-center items-center h-64">
        <Globe className="h-12 w-12 text-gray-400 mb-4" />
        <p className="text-gray-500">No external data available</p>
      </div>
    );
  }

  // Extract the insight_value from the first item in the data array (new response format)
  const { data } = externalData;
  const insightRecord = data.find(item => item.insight_type === 'external_data');
  const insightData = insightRecord?.insight_value || {};
  
  // Get annual report insights from the new API response
  let annualReportData: AnnualReportInsight[] = [];
  if (annualReportInsights?.data && Array.isArray(annualReportInsights.data)) {
    // Find the insight record with annual report insights
    const annualRecord = annualReportInsights.data.find(item => item.insight_type === 'annual_report_insights');
    if (annualRecord && Array.isArray(annualRecord.insight_value)) {
      annualReportData = annualRecord.insight_value;
    }
  }
  
  // Get audit report insights from the new API response
  let auditReportData: AuditConcern[] = [];
  if (auditReportInsights?.data && Array.isArray(auditReportInsights.data)) {
    // Find the insight record with audit flags
    const auditRecord = auditReportInsights.data.find(item => item.insight_type === 'audit_flags');
    if (auditRecord && Array.isArray(auditRecord.insight_value)) {
      auditReportData = auditRecord.insight_value;
    }
  }
  
  // Combine existing data with API data for the two new sections
  const enhancedData = {
    ...insightData,
    // Use the data from the API endpoints
    audit_report_insights: auditReportData || [],
    annual_report_insights: annualReportData || []
  };
  
  // Get all section keys that have data
  const sectionKeys = Object.keys(enhancedData).filter(key => 
    Array.isArray(enhancedData[key as keyof typeof enhancedData]) && 
    (enhancedData[key as keyof typeof enhancedData] as any[]).length > 0
  );

  // Function to filter sections based on active selection
  const getFilteredSections = () => {
    return [activeSection];
  };

  // Map insights to red flags format differently based on section type
  const mapInsightsToRedFlags = (sectionKey: string, insights: any[]) => {
    if (sectionKey === 'audit_report_insights') {
      // Map audit report insights
      return insights.map((item, index) => ({
        id: `audit-${index}`,
        merchant_id: selectedMerchantId || '',
        rule_code: `audit_insight_${index}`,
        description: item.title || item.summary,
        severity: 'high',
        rule_type: 'insolvency_external_audit',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        metric_values: null,
        metric_data_timestamp: null,
        notes: null
      }));
    } else if (sectionKey === 'annual_report_insights') {
      // Map annual report insights with new data structure
      return insights.map((item, index) => ({
        id: `annual-${index}`,
        merchant_id: selectedMerchantId || '',
        rule_code: `annual_insight_${index}`,
        description: item.insight.substring(0, 120) + (item.insight.length > 120 ? '...' : ''),
        severity: item.tag === 'redflag' ? 'high' : 'medium',
        rule_type: 'insolvency_external_annualreport',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        metric_values: null,
        metric_data_timestamp: null,
        notes: null
      }));
    } else {
      // Original mapping for other sections
      return insights.map((insight, index) => ({
        id: `${sectionKey}-${index}`,
        merchant_id: selectedMerchantId || '',
        rule_code: `${sectionKey}_insight_${index}`,
        description: insight.title || insight.summary,
        severity: sectionKey === 'legal_regulatory_compliance' || sectionKey === 'financial_warning_signs' ? 'high' : 'medium',
        rule_type: sectionKey === 'legal_regulatory_compliance' ? 'insolvency_external_legal' : 
                 sectionKey === 'financial_warning_signs' ? 'insolvency_financial_financialmetrics' : 
                 sectionKey === 'operational_disruptions' ? 'insolvency_external_operational' : 
                 sectionKey === 'sentiment_brand_reputation' ? 'insolvency_external_reputation' : 
                 sectionKey === 'industry_macroeconomic' ? 'insolvency_external_industry' : 
                 sectionKey === 'financial_disclosures' ? 'insolvency_external_financial' : 
                 sectionKey === 'executive_workforce_developments' ? 'insolvency_external_executive' : 'insolvency_external_other',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        metric_values: null,
        metric_data_timestamp: null,
        notes: null
      }));
    }
  };

  // Render function for section content based on section type
  const renderSectionContent = (sectionKey: string, items: any[]) => {
    if (sectionKey === 'audit_report_insights') {
      return (
        <div className="mt-4 space-y-4">
          {items.map((item: AuditConcern, index: number) => (
            <AuditReportCard
              key={`${sectionKey}-${index}`}
              item={item}
              index={index}
            />
          ))}
        </div>
      );
    } else if (sectionKey === 'annual_report_insights') {
      return (
        <div className="mt-4 space-y-4">
          {items.map((item: AnnualReportInsight, index: number) => (
            <AnnualReportCard
              key={`${sectionKey}-${index}`}
              item={item}
              index={index}
            />
          ))}
        </div>
      );
    } else {
      // Original rendering for other sections
      return (
        <div className="mt-4 space-y-4">
          {items.map((item: any, index: number) => (
            <IssueCard
              key={`${sectionKey}-${index}`}
              item={item}
              index={index}
            />
          ))}
        </div>
      );
    }
  };

  return (
    <div className="space-y-6">
      <SectionSelector 
        sections={sectionKeys}
        activeSection={activeSection}
        onChange={setActiveSection}
        sectionConfig={sectionConfig}
      />

      {/* Main Content */}
      <div className="space-y-8">
        {getFilteredSections().map((key) => {
          if (!enhancedData[key as keyof typeof enhancedData] || (enhancedData[key as keyof typeof enhancedData] as any[]).length === 0) {
            return null;
          }

          const items = enhancedData[key as keyof typeof enhancedData] as any[];
          const config = sectionConfig[key as keyof typeof sectionConfig];
          const redFlags = mapInsightsToRedFlags(key, items);
          
          return (
            <div key={key}>
              <SectionHeaderWithRedFlags 
                redFlags={redFlags}
                title={config.title}
                icon={config.icon}
                iconColorClass={config.iconColorClass}
              />
              {renderSectionContent(key, items)}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default InsolvencyExternalInsightsTab;
