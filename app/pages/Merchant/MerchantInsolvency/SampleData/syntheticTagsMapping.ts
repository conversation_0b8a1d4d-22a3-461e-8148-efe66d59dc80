export const tagsMapping: Record<string, string> = {
  // External insolvency mappings
  'insolvency_external_audit': 'Audit',
  'insolvency_external_executive': 'Executive',
  'insolvency_external_disclosures': 'Disclosures',
  'insolvency_external_financial': 'Financial',
  'insolvency_external_legal': 'Legal',
  'insolvency_external_operational': 'Operational',
  'insolvency_external_annualReport': 'Annual Report',
  'insolvency_external_industry': 'Industry',
  'insolvency_external_brand': 'Brand',
  
  // Financial metrics
  'insolvency_financial_financialStatements': 'Financial Statements',
  'insolvency_financial_financialMetrics': 'Financial Metrics',
  'transaction_monitoring': 'Transaction Monitoring',

  'insolvency_overview_pdAnalysis': 'Default Analysis',
  
  'insolvency_overview_company': 'Company',
  'insolvency_overview_industry': 'Industry',
  'insolvency_overview_transactionMetrics': 'Transaction Metrics'
};

export const getTagCategory = (tag: string): string => {
  return tagsMapping[tag] || 'Other';
};
