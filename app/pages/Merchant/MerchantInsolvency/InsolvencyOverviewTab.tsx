'use client';

import { FC, useState, useEffect, useMemo, useRef } from 'react';
import { motion } from 'framer-motion';
import { Building2, Plane, Activity, Building, Store, TrendingUp, Users, AlertTriangle, Calendar, ArrowLeftRight, Wallet, CircleCheck, FileCheck, Puzzle, ShieldCheck, MapPin, Factory, Clock, FileWarning } from 'lucide-react';
import { useActiveContext } from '@/app/layout/ActiveContext/useActiveContext';
import { useMerchantIdStore } from '@/app/store/merchant/merchantIdStore';
import { ReportableSection } from '@/app/pages/ReportGeneration/utils/ReportSectionHelpers';
import { RiskAssessmentSection, MerchantHeader } from '@/app/pages/Merchant/MerchantInvestigation/Overview/components';
import ProbabilityOfDefault from './Components/ProbabilityOfDefault';
import { KeyMetric } from '@/app/types';
import { Visualization } from '@/components/custom/visualization';
import SectionHeaderWithRedFlags from '@/components/custom/SectionHeaderWithRedFlags';
import { KeyMetrics } from '@/components/custom/KeyMetrics';
import { API } from '@/app/services/axios';
import { RedFlag, useInvestigationRedFlagsStore } from '@/app/store/merchant/InvestigationRedFlagsStore';
import {
  monthlyMetricsData,
  transactionsConfig,
  chargebacksConfig,
  balanceConfig,
  financialStatsData
} from './SampleData/sampleData';
import {
  allRedFlags,
  financialRedFlags,
  operationalRedFlags,
  legalRedFlags,
  marketRedFlags,
  transactionRedFlags
} from './SampleData/redFlagsSampleData';
import { useInvestigationOverviewStore } from '@/app/store/merchant/investigationOverviewStore';
import { useInsolvencyRedFlags } from './InsolvencyRedFlagsTab';
import html2canvas from 'html2canvas';
import { RiskAssessmentParagraph } from '@/components/custom/RiskAssessmentParagraph';

interface CompanyMetric {
  label: string;
  value: string | number;
  icon: string;
}

interface CompanyMetricsResponse {
  success: boolean;
  message: string;
  data: CompanyMetric[];
}

interface CompanyData {
  company_name: string;
  about_the_company: string;
  source_urls: string[];
}

interface IndustryData {
  about_the_industry: string;
  is_industry_risky: string;
  justification: string;
}

// Add this generic wrapper at the top (or in a shared file if you want to reuse elsewhere)
const ReportVisualization = ({ config, data, title, chartRef }: any) => {
  // Use useRef to store state to prevent re-renders
  const previewRef = useRef({
    isActive: false,
  });
  
  // Use a ref to store the toggle function to avoid recreation
  const togglePreview = useRef((newState?: boolean) => {
    const nextState = newState !== undefined ? newState : !previewRef.current.isActive;
    previewRef.current.isActive = nextState;
    
    // Force a re-render only when the button is clicked
    setForceUpdate(prev => prev + 1);
  }).current;
  
  // Simple counter to force re-render only when needed
  const [forceUpdate, setForceUpdate] = useState(0);
  
  // Create the test config once and store in a ref
  const testConfigRef = useRef({
    ...config,
    title: "Live Preview Test - " + title,
    data: data,
    leftYAxisLabel: "Test Left Label",
    rightYAxisLabel: "Test Right Label"
  });
  
  // Update the test config ref if inputs change
  useEffect(() => {
    testConfigRef.current = {
      ...config,
      title: "Live Preview Test - " + title,
      data: data,
      leftYAxisLabel: "Test Left Label",
      rightYAxisLabel: "Test Right Label"
    };
  }, [config, data, title]);

  return (
    <div ref={chartRef}>
      {/* Test button to toggle live preview */}
      <button
        onClick={() => togglePreview()}
        className="mb-2 px-2 py-1 bg-blue-500 text-white text-xs rounded"
      >
        {previewRef.current.isActive ? 'Exit Live Preview' : 'Test Live Preview'}
      </button>

      <Visualization
        {...config}
        data={data}
        className="w-full"
        isEnclosedInCard={true}
        title={title}
        height={250}
        isLivePreview={previewRef.current.isActive}
        livePreviewConfig={previewRef.current.isActive ? testConfigRef.current : undefined}
        onUpdate={(updatedProps) => {
          console.log('Visualization updated:', updatedProps);
          // Here you could update the config or data if needed
          // For now, just log the changes
        }}
      />
    </div>
  );
};

const InsolvencyOverviewTab: FC = () => {
  const { activeContexts } = useActiveContext();
  const { merchantIdList, selectedMerchantId } = useMerchantIdStore();
  const [isCompanyExpanded, setIsCompanyExpanded] = useState(false);
  const [isIndustryExpanded, setIsIndustryExpanded] = useState(false);
  const [isMetricsExpanded, setIsMetricsExpanded] = useState(false);
  const [companyMetrics, setCompanyMetrics] = useState<CompanyMetric[]>([]);
  const { fetchTransactionMetrics, transactionMetrics } = useInvestigationOverviewStore();
  const [companyData, setCompanyData] = useState<CompanyData | null>(null);
  const [industryData, setIndustryData] = useState<IndustryData | null>(null);

  // Get red flags data from the store
  const { fetchFlagsList } = useInvestigationRedFlagsStore();
  const flagsList = useInsolvencyRedFlags();

  const merchantId = useMemo(() => activeContexts?.merchant || selectedMerchantId, [activeContexts, selectedMerchantId]);
  const activeMerchant = merchantIdList.find(m => m.id === merchantId);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  // Helper function to ensure numbers
  const toNumber = (value: string | number): number => {
    return typeof value === 'string' ? parseFloat(value) : value;
  };

  // Visualization configs array
  const visualizationConfigs = [
    { key: 'transactions', config: transactionsConfig, title: 'Transactions' },
    { key: 'chargebacks', config: chargebacksConfig, title: 'Chargebacks' },
    { key: 'balance', config: balanceConfig, title: 'Balance' },
  ];
  // Refs and images for each visualization
  const chartRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
  const [chartImages, setChartImages] = useState<{ [key: string]: string | null }>({});

  useEffect(() => {
    const fetchCompanyMetrics = async () => {
      if (!selectedMerchantId) return;

      try {
        const { data } = await API.get<CompanyMetricsResponse>(`/api/v1/credit-dashboard/${selectedMerchantId}/getCompanyMetrics`);
        if (data.success) {
          setCompanyMetrics(data.data);
        }
      } catch (error) {
        console.error('Error fetching company metrics:', error);
      }
    };

    fetchCompanyMetrics();
  }, [selectedMerchantId]);

  useEffect(() => {
    const fetchMetrics = async () => {
      if (!merchantId) return;
      try {
        await fetchTransactionMetrics(merchantId);
      } catch (error) {
        console.error('Error fetching transaction metrics:', error);
      }
    };

    fetchMetrics();
  }, [merchantId, fetchTransactionMetrics]);

  useEffect(() => {
    if (transactionMetrics?.data) {
      setCompanyMetrics(transactionMetrics.data);
    }
  }, [transactionMetrics]);

  useEffect(() => {
    const fetchCompanyData = async () => {
      if (!merchantId) return;

      try {
        const { data } = await API.get(`/api/v1/credit-dashboard/${merchantId}/aboutTheCompany`);
        if (data.success) {
          setCompanyData(data.data);
        }
      } catch (error) {
        console.error('Error fetching company data:', error);
      }
    };

    fetchCompanyData();
  }, [merchantId]);

  useEffect(() => {
    const fetchIndustryData = async () => {
      if (!merchantId) return;

      try {
        const { data } = await API.get(`/api/v1/credit-dashboard/${merchantId}/aboutIndustryAndRisk`);
        if (data.success) {
          setIndustryData(data.data);
        }
      } catch (error) {
        console.error('Error fetching industry data:', error);
      }
    };

    fetchIndustryData();
  }, [merchantId]);

  // Fetch red flags when component mounts or when merchantId changes
  useEffect(() => {
    if (merchantId) {
      fetchFlagsList(merchantId);
    }
  }, [merchantId, fetchFlagsList]);

  useEffect(() => {
    // Capture chart images after a short delay to ensure charts are rendered
    const timeout = setTimeout(() => {
      // Inject style to hide .hide-for-pdf elements
      const style = document.createElement('style');
      style.innerHTML = '.hide-for-pdf { display: none !important; }';
      document.head.appendChild(style);

      visualizationConfigs.forEach(({ key }) => {
        const ref = chartRefs.current[key];
        if (ref) {
          html2canvas(ref).then(canvas => {
            setChartImages(prev => ({ ...prev, [key]: canvas.toDataURL('image/png') }));
          });
        }
      });

      // Remove the style after a short delay to ensure all captures are done
      setTimeout(() => {
        document.head.removeChild(style);
      }, 500);
    }, 1000);
    return () => clearTimeout(timeout);
  }, [monthlyMetricsData]);

  // Handle report generation for specific sections
  const handleSectionReport = (sectionType: string, data: any, reportId?: string, isNewReport?: boolean) => {
    // Add detailed logging to help with debugging
    console.log(`Handling report for section ${sectionType}`, {
      sectionType,
      dataKeys: Object.keys(data),
      dataValues: JSON.stringify(data),
      reportId,
      isNewReport
    });

    const reportSectionRef = document.querySelector(`[data-report-section="${sectionType}"]`);

    if (reportSectionRef) {
      console.log(`Found report section element for ${sectionType}`);

      if (reportId) {
        // Add to existing report
        console.log(`Dispatching add-to-report event for ${sectionType} to report ${reportId}`);

        const event = new CustomEvent('add-to-report', {
          detail: { type: sectionType, data, reportId },
          bubbles: true
        });
        reportSectionRef.dispatchEvent(event);
      } else if (isNewReport) {
        // Create new report
        console.log(`Dispatching generate-report event for ${sectionType}`);

        const event = new CustomEvent('generate-report', {
          detail: { type: sectionType, data },
          bubbles: true
        });
        reportSectionRef.dispatchEvent(event);
      }
    } else {
      console.error(`Report section element not found for type: ${sectionType}`);
    }
  };

  return (
    <motion.div
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div variants={itemVariants}>
        {activeMerchant && (
          <MerchantHeader activeMerchant={activeMerchant} />
        )}
      </motion.div>

      <motion.div variants={itemVariants} className="space-y-4">
        <ReportableSection type="company-overview" data={{ companyData }}>
          <SectionHeaderWithRedFlags
            redFlags={flagsList}
            title="Company"
            icon={Building2}
            iconColorClass="text-blue-600"
            redFlag_recepient_id="insolvency_overview_company"
            onReport={(reportId, isNewReport) => handleSectionReport("company-overview", { companyData }, reportId, isNewReport)}
          />
          <div className="text-gray-600 leading-relaxed">
            {companyData ? (
              <>
                <p className="py-2">
                  {companyData.about_the_company.split('. ').slice(0, 2).join('. ')}.{' '}
                  {!isCompanyExpanded && (
                    <button
                      onClick={() => setIsCompanyExpanded(true)}
                      className="text-blue-600 underline hover:text-blue-800"
                    >
                      Read More
                    </button>
                  )}
                </p>
                {isCompanyExpanded && (
                  <>
                    <p className="mt-2">
                      {companyData.about_the_company.split('. ').slice(2).join('. ')}{' '}
                      <button
                        onClick={() => setIsCompanyExpanded(false)}
                        className="text-blue-600 underline hover:text-blue-800"
                      >
                        Show Less
                      </button>
                    </p>
                    {companyData.source_urls.length > 0 && (
                      <div className="mt-4 text-sm text-gray-500">
                        <p className="font-medium">Sources:</p>
                        <ul className="list-disc list-inside mt-1">
                          {companyData.source_urls.map((url, index) => (
                            <li key={index}>
                              <a href={url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                                {url}
                              </a>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </>
                )}
              </>
            ) : (
              <p>Loading company information...</p>
            )}
          </div>
        </ReportableSection>

        <ReportableSection type="key-stats" data={{ companyMetrics }}>
          <SectionHeaderWithRedFlags
            redFlags={flagsList}
            title="Company Metrics"
            icon={Activity}
            iconColorClass="text-blue-600"
            redFlag_recepient_id="insolvency_overview_company_metrics"
            onReport={(reportId, isNewReport) => handleSectionReport("key-stats", { keyMetricList: { key_metrics: companyMetrics } }, reportId, isNewReport)}
          />
          <div className="mt-4">
            <KeyMetrics
              keyMetricList={{ key_metrics: companyMetrics }}
              isMetricsExpanded={isMetricsExpanded}
              setIsMetricsExpanded={setIsMetricsExpanded}
              showHeader={false}
            />
          </div>
        </ReportableSection>
      </motion.div>


      <motion.div variants={itemVariants} className="space-y-4">
        <ReportableSection type="industry-overview" data={{ industryData }}>
          <SectionHeaderWithRedFlags
            redFlags={flagsList}
            title="About Industry"
            icon={Plane}
            iconColorClass="text-blue-600"
            redFlag_recepient_id="insolvency_overview_aboutIndustry"
            onReport={(reportId, isNewReport) => handleSectionReport("industry-overview", { industryData }, reportId, isNewReport)}
          />
          <div className="text-gray-600 leading-relaxed">
            {industryData ? (
              <>
                <p className="py-2">
                  {industryData.about_the_industry.split('. ').slice(0, 2).join('. ')}.{' '}
                  {!isIndustryExpanded && (
                    <button
                      onClick={() => setIsIndustryExpanded(true)}
                      className="text-blue-600 underline hover:text-blue-800"
                    >
                      Read More
                    </button>
                  )}
                </p>
                {isIndustryExpanded && (
                  <>
                    <p className="mt-2">
                      {industryData.about_the_industry.split('. ').slice(2).join('. ')}{' '}
                      <button
                        onClick={() => setIsIndustryExpanded(false)}
                        className="text-blue-600 underline hover:text-blue-800"
                      >
                        Show Less
                      </button>
                    </p>
                    {industryData.is_industry_risky === 'yes' && (
                      <RiskAssessmentParagraph
                        title="Industry Risk Assessment"
                        justification={industryData.justification}
                        riskLevel="high"
                      />
                    )}
                  </>
                )}
              </>
            ) : (
              <p>Loading industry information...</p>
            )}
          </div>
        </ReportableSection>
      </motion.div>

      <motion.div variants={itemVariants}>
        <ReportableSection type="default-probability" data={{ merchantId: selectedMerchantId || '' }}>
          <SectionHeaderWithRedFlags
            redFlags={flagsList}
            title="Probability of Default Analysis"
            icon={AlertTriangle}
            iconColorClass="text-blue-600"
            redFlag_recepient_id="insolvency_overview_pdAnalysis"
            onReport={(reportId, isNewReport) => handleSectionReport("default-probability", { merchantId: selectedMerchantId || '' }, reportId, isNewReport)}
          />
          <ProbabilityOfDefault merchantId={selectedMerchantId || ''} />
        </ReportableSection>
      </motion.div>

      <motion.div variants={itemVariants}>
        <ReportableSection type="risk-score" data={{}}>
          <RiskAssessmentSection
            riskAssessment={null}
            keyMetricList={{ key_metrics: [] }}
          />
        </ReportableSection>
      </motion.div>

      <motion.div variants={itemVariants}>
        <ReportableSection type="transaction-metrics" data={{ transactionMetrics }}>
          <SectionHeaderWithRedFlags
            redFlags={flagsList}
            title="Transaction Metrics"
            icon={Activity}
            iconColorClass="text-blue-600"
            redFlag_recepient_id="insolvency_overview_transactionMetrics"
            onReport={(reportId, isNewReport) => handleSectionReport("transaction-metrics", { transactionMetrics: transactionMetrics }, reportId, isNewReport)}
          />
          <div className="mt-4">
            <KeyMetrics
              keyMetricList={{ key_metrics: transactionMetrics }}
              isMetricsExpanded={isMetricsExpanded}
              setIsMetricsExpanded={setIsMetricsExpanded}
              showHeader={false}
            />
          </div>
        </ReportableSection>
      </motion.div>
      <motion.div variants={itemVariants}>
        <div className="grid grid-cols-3 gap-4">
          {visualizationConfigs.map(({ key, config, title }) => (
            <motion.div key={key} variants={itemVariants}>
              <ReportableSection
                type="transactions-visualizations"
                title={title}
                data={{
                  title,
                  chartImage: chartImages[key] || null,
                  config,
                  data: monthlyMetricsData,
                }}
              >
                <ReportVisualization
                  config={config}
                  data={monthlyMetricsData}
                  title={title}
                  chartRef={(el: HTMLDivElement | null) => (chartRefs.current[key] = el)}
                />
              </ReportableSection>
            </motion.div>
          ))}
        </div>
      </motion.div>


    </motion.div>
  );
};

export default InsolvencyOverviewTab;
