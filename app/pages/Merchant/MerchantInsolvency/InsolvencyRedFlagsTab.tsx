'use client';

import { FC, useEffect, useState, useMemo } from 'react';
import { AlertTriangle, AlertCircle } from 'lucide-react';
import { useInvestigationRedFlagsStore } from '@/app/store/merchant/InvestigationRedFlagsStore';
import { useMerchantIdStore } from '@/app/store/merchant/merchantIdStore';
import { useArtifactStore } from '@/app/store/artifact/artifactStore';
import { getTagCategory } from './SampleData/syntheticTagsMapping';
import RedFlagsRender, { RedFlag as ComponentRedFlag, FlagCategoryOption, FlagSeverityOption } from '@/components/custom/RedFlagsRender';

// Define severity filter options
const severityOptions: FlagSeverityOption[] = [
  { value: 'severe', label: 'Severe', icon: AlertTriangle },
  { value: 'high', label: 'High', icon: AlertTriangle },
  { value: 'medium', label: 'Medium', icon: AlertTriangle },
  { value: 'low', label: 'Low', icon: AlertTriangle }
];

// Dynamic icon mapping based on category
const categoryIconMap: Record<string, any> = {
  'audit': AlertCircle,
  'legal': AlertCircle,
  'operational': AlertTriangle,
  'financial': AlertCircle,
  'reputation': AlertCircle,
  'industry': AlertCircle,
  'annual_report': AlertCircle,
  'company': AlertCircle,
  'other': AlertTriangle
};

// Dynamic color mapping based on category
const categoryColorMap: Record<string, string> = {
  'audit': 'text-orange-600',
  'legal': 'text-red-600',
  'operational': 'text-amber-600',
  'financial': 'text-emerald-600',
  'reputation': 'text-purple-600',
  'industry': 'text-blue-600',
  'annual_report': 'text-pink-600',
  'company': 'text-blue-600',
  'other': 'text-gray-600'
};

// Function to get category and display info for a rule type
const getCategoryInfo = (ruleType: string) => {
  const category = getTagCategory(ruleType).toLowerCase().replace('_', '');
  return {
    category,
    label: getTagCategory(ruleType),
    icon: categoryIconMap[category] || categoryIconMap.other,
    color: categoryColorMap[category] || categoryColorMap.other
  };
};

// Export a hook to access the red flags data from other components
export const useInsolvencyRedFlags = () => {
  const { flagsList } = useInvestigationRedFlagsStore();
  
  const severityOrder: Record<string, number> = {
    'severe': 0,
    'high': 1,
    'medium': 2,
    'low': 3,
    'unknown': 4
  };
  
  return [...flagsList].sort((a, b) => {
    const severityA = (a.severity?.toLowerCase() || 'unknown').trim();
    const severityB = (b.severity?.toLowerCase() || 'unknown').trim();
    
    const orderA = severityOrder[severityA] ?? severityOrder.unknown;
    const orderB = severityOrder[severityB] ?? severityOrder.unknown;
    
    return orderA - orderB;
  });
};

const InsolvencyRedFlagsTab: FC = () => {
  const { 
    fetchFlagsList,
    filterSeverities,
    setFilterSeverities,
    clearFilters
  } = useInvestigationRedFlagsStore();
  const rawFlagsList = useInsolvencyRedFlags();
  const { selectedMerchantId } = useMerchantIdStore();
  const artifactStore = useArtifactStore();
  const [isLoading, setIsLoading] = useState(false);

  // Map store flags to component flags
  const flagsList = useMemo(() => {
    return rawFlagsList.map(flag => ({
      id: flag.id,
      description: flag.description,
      severity: flag.severity,
      rule_type: flag.rule_type,
      created_at: flag.created_at,
      rule_name: flag.rule_name,
      rule_code: flag.rule_code,
      metric_values: flag.metric_values || undefined
    } as ComponentRedFlag));
  }, [rawFlagsList]);

  useEffect(() => {
    if (selectedMerchantId) {
      fetchFlagsList(selectedMerchantId);
    }
  }, [fetchFlagsList, selectedMerchantId]);

  const handleRefresh = async () => {
    if (selectedMerchantId) {
      setIsLoading(true);
      await fetchFlagsList(selectedMerchantId);
      setIsLoading(false);
    }
  };

  // Get unique categories for dropdown
  const categoryOptions = useMemo(() => {
    const categories = flagsList.map(flag => getTagCategory(flag.rule_type || ''));
    const uniqueCategories = Array.from(new Set(categories));
    return uniqueCategories.map(category => ({
      value: category.toLowerCase().replace('_', ''),
      label: category,
      icon: AlertCircle
    }));
  }, [flagsList]);

  return (
    <div className="space-y-6">
      <RedFlagsRender
        flags={flagsList}
        severityOptions={severityOptions}
        categoryOptions={categoryOptions}
        onRefresh={handleRefresh}
        isLoading={isLoading}
        getCategoryInfo={getCategoryInfo}
        externalFilterSeverities={filterSeverities}
        setExternalFilterSeverities={setFilterSeverities}
        allowViewModeToggle={false}
        useArtifactTab={true}
        artifactStore={artifactStore}
      />
    </div>
  );
};

export default InsolvencyRedFlagsTab;
