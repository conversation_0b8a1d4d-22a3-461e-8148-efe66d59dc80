import { FC } from 'react';
import { CustomCard } from '@/components/custom/CustomCard';
import { Hash, Phone, Mail } from 'lucide-react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { EmptyState } from './EmptyState';

interface WebsiteContentCardProps {
  content: {
    keywords: {
      keyword: string;
      frequency: number;
    }[];
    contact_information: {
      type: string;
      value: string;
    }[];
  };
}

export const WebsiteContentCard: FC<WebsiteContentCardProps> = ({ content }) => {

  const hasData = content.keywords.length > 0 || content.contact_information.length > 0;
  if (!hasData) {
    return <Card className="p-4"><EmptyState message="No website content analysis available" /></Card>;
  }

  const getContactIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'phone':
        return <Phone className="h-4 w-4 text-gray-400" />;
      case 'email':
        return <Mail className="h-4 w-4 text-gray-400" />;
      default:
        return null;
    }
  };

  return (
    <CustomCard className="p-4">
      <div className="space-y-6">
        {/* Keywords */}
        <div>
          <h3 className="text-sm font-medium text-gray-500 mb-3">Top Keywords</h3>
          <div className="flex flex-wrap gap-2">
            {content.keywords.map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.05 }}
                className="flex items-center gap-1 px-3 py-1 bg-gray-100 rounded-full"
              >
                <Hash className="h-3 w-3 text-gray-400" />
                <span className="text-sm">{item.keyword}</span>
                <span className="text-xs text-gray-500">({item.frequency})</span>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Contact Information */}
        <div>
          <h3 className="text-sm font-medium text-gray-500 mb-3">Contact Information</h3>
          <div className="space-y-2">
            {content.contact_information.map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg"
              >
                {getContactIcon(item.type)}
                <span className="text-sm">{item.value}</span>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </CustomCard>
  );
};