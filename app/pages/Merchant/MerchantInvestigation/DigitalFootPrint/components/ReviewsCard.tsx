import { FC } from 'react';
import { CustomCard } from '@/components/custom/CustomCard';
import { MessageSquare, ThumbsUp, ThumbsDown, Minus } from 'lucide-react';
import { motion } from 'framer-motion';
import { EmptyState } from './EmptyState';
import { Card } from '@/components/ui/card';

interface ReviewsCardProps {
  reviews: {
    summary: {
      sentiment: string;
      summary: string;
    }[];
    detailed_reviews: {
      source: string;
      title: string;
      summary: string;
      content: string;
      url: string;
    }[];
  };
}

export const ReviewsCard: FC<ReviewsCardProps> = ({ reviews }) => {

  const hasData = reviews.summary.length > 0 || reviews.detailed_reviews.length > 0;
  if (!hasData) {
    return <Card className="p-4"><EmptyState message="No reviews available" /></Card>;
  }

  const getSentimentIcon = (sentiment: string) => {
    switch (sentiment.toLowerCase()) {
      case 'positive':
        return <ThumbsUp className="h-4 w-4 text-green-500" />;
      case 'negative':
        return <ThumbsDown className="h-4 w-4 text-red-500" />;
      default:
        return <Minus className="h-4 w-4 text-yellow-500" />;
    }
  };

  return (
    <CustomCard className="p-4">
      {/* <div className="flex items-center gap-2 mb-4">
        <MessageSquare className="h-5 w-5 text-purple-500" />
        <h3 className="font-semibold">Customer Reviews</h3>
      </div> */}

      <div className="space-y-6">
        {/* Review Summaries */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {reviews.summary.map((summary, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="p-3 bg-gray-50 rounded-lg"
            >
              <div className="flex items-center gap-2 mb-2">
                {summary.sentiment ? getSentimentIcon(summary.sentiment) : <Minus className="h-4 w-4 text-yellow-500" />}
                <span className="text-sm font-medium capitalize">{summary.sentiment}</span>
              </div>
              <p className="text-sm text-gray-600">{summary.summary}</p>
            </motion.div>
          ))}
        </div>

        {/* Detailed Reviews */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-500">Recent Reviews</h4>
          {reviews.detailed_reviews.map((review, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="p-4 border rounded-lg hover:shadow-sm transition-shadow"
            >
              <div className="flex justify-between items-start mb-2">
                <span className="text-sm font-medium">{review.source}</span>
                <a 
                  href={review.url || ''} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-xs text-blue-500 hover:underline"
                >
                  View Source
                </a>
              </div>
              <h5 className="font-medium mb-2">{review.title}</h5>
              <p className="text-sm text-gray-600">{review.summary}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </CustomCard>
  );
};