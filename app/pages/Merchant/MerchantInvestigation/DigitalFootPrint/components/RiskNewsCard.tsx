import { FC } from 'react';
import { CustomCard } from '@/components/custom/CustomCard';
import { AlertCircle, Calendar } from 'lucide-react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { EmptyState } from './EmptyState';

interface RiskNewsCardProps {
  data: {
    risk_summary: { summary: string; sentiment: string }[];
    incidents: {
      title: string;
      summary: string;
      content: string;
      time: string;
      link: string;
    }[];
  };
}

export const RiskNewsCard: FC<RiskNewsCardProps> = ({ data }) => {

  const hasData = data.risk_summary.length > 0 || data.incidents.length > 0;
  if (!hasData) {
    return <Card className="p-4"><EmptyState message="No risk or news information available" /></Card>;
  }   
  
  return (
    <CustomCard className="p-4">
      {/* <div className="flex items-center gap-2 mb-4">
        <AlertCircle className="h-5 w-5 text-red-500" />
        <h3 className="font-semibold">Risk & News Analysis</h3>
      </div> */}

      {/* Risk Summaries */}
      <div className="mb-6 space-y-3">
        {data.risk_summary.map((item, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: index * 0.1 }}
            className={`p-3 rounded-lg ${
              item.sentiment.toLowerCase() === 'negative' ? 'bg-red-50' : 
              item.sentiment.toLowerCase() === 'positive' ? 'bg-green-50' : 
              'bg-yellow-50'
            }`}
          >
            <p className="text-sm">{item.summary}</p>
          </motion.div>
        ))}
      </div>

      {/* Incidents */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium text-gray-500">Recent Incidents</h4>
        {data.incidents.map((incident, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
            className="border rounded-lg p-4 hover:shadow-sm transition-shadow"
          >
            <div className="flex justify-between items-start mb-2">
              <h5 className="font-medium">{incident.title}</h5>
              <div className="flex items-center gap-1 text-xs text-gray-500">
                <Calendar className="h-3 w-3" />
                {new Date(incident.time).toLocaleDateString()}
              </div>
            </div>
            <p className="text-sm text-gray-600 mb-2">{incident.summary}</p>
            <a 
              href={incident.link} 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-xs text-blue-500 hover:underline"
            >
              Read More
            </a>
          </motion.div>
        ))}
      </div>
    </CustomCard>
  );
};