import { FC, useMemo } from 'react';
import { CustomCard } from '@/components/custom/CustomCard';
import { Activity, CheckCircle, XCircle } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { EmptyState } from './EmptyState';
import {
  ResponsiveContainer,
  ComposedChart,
  Line,
  XAxis,
  YAxis,
  Tooltip,
  CartesianGrid
} from 'recharts';
import { motion } from 'framer-motion';

interface WebsiteMonitoringCardProps {
  monitoring: {
    timestamp: string | null;
    status_code: string | null;
    response_time_ms: string | null;
    is_active: boolean | null;
    error: string | null;
  }[];
}

export const WebsiteMonitoringCard: FC<WebsiteMonitoringCardProps> = ({ monitoring }) => {
  if (!monitoring || monitoring.length === 0) {
    return <Card className="p-4"><EmptyState message="No monitoring data available" /></Card>;
  }

  const latestStatus = monitoring[0];
  
  // Process and sort data for the chart
  const chartData = useMemo(() => {
    return [...monitoring]
      .filter(item => item.timestamp && item.response_time_ms)
      .map(item => ({
        timestamp: item.timestamp,
        responseTime: Number(item.response_time_ms),
        isActive: item.is_active
      }))
      .sort((a, b) => new Date(a.timestamp!).getTime() - new Date(b.timestamp!).getTime());
  }, [monitoring]);

  // Calculate average response time
  const avgResponseTime = useMemo(() => {
    const validTimes = chartData.filter(item => !isNaN(item.responseTime));
    return validTimes.reduce((acc, curr) => acc + curr.responseTime, 0) / validTimes.length;
  }, [chartData]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <CustomCard className="p-4">
        <div className="space-y-6">
          {/* Header with current status */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-blue-500" />
              <h3 className="font-semibold">Website Monitoring</h3>
            </div>
            <div className="flex items-center gap-2">
              {latestStatus.is_active ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <XCircle className="h-5 w-5 text-red-500" />
              )}
              <span className={`text-sm font-medium ${latestStatus.is_active ? 'text-green-500' : 'text-red-500'}`}>
                {latestStatus.is_active ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>

          {/* Summary Stats */}
          <div className="grid grid-cols-3 gap-4">
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-500">Latest Status Code</p>
              <p className="text-lg font-semibold">{latestStatus.status_code}</p>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-500">Avg Response Time</p>
              <p className="text-lg font-semibold">{avgResponseTime.toFixed(0)}ms</p>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-500">Monitoring Points</p>
              <p className="text-lg font-semibold">{monitoring.length}</p>
            </div>
          </div>

          {/* Response Time Chart */}
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <ComposedChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="timestamp"
                  tickFormatter={(value) => {
                    const date = new Date(value);
                    return date.toLocaleTimeString();
                  }}
                  stroke="#888"
                  tick={{ fill: '#666', fontSize: 12 }}
                  axisLine={{ stroke: '#e0e0e0' }}
                />
                <YAxis
                  dataKey="responseTime"
                  name="Response Time"
                  unit="ms"
                  stroke="#888"
                  tick={{ fill: '#666', fontSize: 12 }}
                  axisLine={{ stroke: '#e0e0e0' }}
                />
                <Tooltip
                  labelFormatter={(value) => {
                    const date = new Date(value);
                    return date.toLocaleString();
                  }}
                  formatter={(value: number) => [`${value}ms`, 'Response Time']}
                  contentStyle={{
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    border: '1px solid #e0e0e0',
                    borderRadius: '6px',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                  }}
                />
                <Line
                  type="monotone"
                  dataKey="responseTime"
                  stroke="#8884d8"
                  strokeWidth={2}
                  dot={{ fill: '#8884d8', r: 4 }}
                  activeDot={{ r: 6 }}
                  name="Response Time"
                />
              </ComposedChart>
            </ResponsiveContainer>
          </div>

          {/* Error Display */}
          {latestStatus.error && (
            <div className="p-3 bg-red-50 rounded-lg">
              <p className="text-sm text-red-600">{latestStatus.error}</p>
            </div>
          )}
        </div>
      </CustomCard>
    </motion.div>
  );
};