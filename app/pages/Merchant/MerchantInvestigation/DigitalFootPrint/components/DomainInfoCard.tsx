import { FC } from 'react';
import { CustomCard } from '@/components/custom/CustomCard';
import { Globe, Shield, Mail, User } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { EmptyState } from './EmptyState';

interface DomainInformation {
  domain: string;
  server_details: {
    ip_address: string;
    country: string;
    owner: string;
  };
  domain_details: {
    age: string;
    registrar: string;
    owner_email: string | null;
    admin_contact: string | null;
    privacy_protection: boolean;
  };
  business_model: string;
}

interface DomainInfoCardProps {
  domainInfo: DomainInformation;
}

export const DomainInfoCard: FC<DomainInfoCardProps> = ({ domainInfo }) => {

  const hasData = domainInfo.domain ||
    Object.values(domainInfo.server_details).some(v => v) ||
    Object.values(domainInfo.domain_details).some(v => v) ||
    domainInfo.business_model;

  if (!hasData) {
    return <Card className="p-4"><EmptyState message="No domain information available" /></Card>;
  }

  return <CustomCard className="p-4 space-y-4">
    <div className="flex items-center gap-2">
      <Globe className="h-5 w-5 text-blue-500" />
      <h3 className="font-semibold">Domain Related</h3>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-500">Server Details</h4>
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">IP Address</span>
            <span className="text-sm font-medium">{domainInfo.server_details.ip_address}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Country</span>
            <span className="text-sm font-medium">{domainInfo.server_details.country}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Owner</span>
            <span className="text-sm font-medium">{domainInfo.server_details.owner}</span>
          </div>
        </div>
      </div>

      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-500">Domain Details</h4>
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Age</span>
            <span className="text-sm font-medium">{domainInfo.domain_details.age}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Registrar</span>
            <span className="text-sm font-medium">{domainInfo.domain_details.registrar}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Privacy Protection</span>
            <span className={`text-sm font-medium ${domainInfo.domain_details.privacy_protection ? 'text-green-500' : 'text-red-500'}`}>
              {domainInfo.domain_details.privacy_protection ? 'Enabled' : 'Disabled'}
            </span>
          </div>
        </div>
      </div>
    </div>
  </CustomCard>
};