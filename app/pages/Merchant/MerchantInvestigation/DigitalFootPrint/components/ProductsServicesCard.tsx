import { FC } from 'react';
import { CustomCard } from '@/components/custom/CustomCard';
import { Package, Tag } from 'lucide-react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { EmptyState } from './EmptyState';

interface ProductsServicesCardProps {
  data: {
    overview: { description: string }[];
    details: {
      name: string;
      summary: string;
      price: string;
      industry_and_location: string;
    }[];
  };
}

export const ProductsServicesCard: FC<ProductsServicesCardProps> = ({ data }) => {

  const hasData = data.overview.length > 0 || data.details.length > 0;
  if (!hasData) {
    return <Card className="p-4"><EmptyState message="No products or services information available" /></Card>;
  }

  return (
    <CustomCard className="p-4">
      {/* <div className="flex items-center gap-2 mb-4">
        <Package className="h-5 w-5 text-indigo-500" />
        <h3 className="font-semibold">Products & Services</h3>
      </div> */}

      {/* Overview */}
      <div className="mb-6">
        {data.overview.map((item, index) => (
          <motion.p
            key={index}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="text-sm text-gray-600 mb-2"
          >
            {item.description}
          </motion.p>
        ))}
      </div>

      {/* Product Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {data.details.map((product, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="p-4 border rounded-lg hover:bg-gray-50 transition-colors"
          >
            <div className="flex items-start justify-between mb-2">
              <h4 className="font-medium">{product.name}</h4>
              <span className="text-sm font-medium text-green-600">{product.price}</span>
            </div>
            <p className="text-sm text-gray-600 mb-2">{product.summary}</p>
            <div className="flex items-center gap-2">
              <Tag className="h-4 w-4 text-gray-400" />
              <span className="text-xs text-gray-500">{product.industry_and_location}</span>
            </div>
          </motion.div>
        ))}
      </div>
    </CustomCard>
  );
};