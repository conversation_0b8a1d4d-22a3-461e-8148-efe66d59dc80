import { FC, useEffect, useState } from 'react';
import { useInvestigationDigitalFootprintStore } from '@/app/store/merchant/investigationDigitalFootprintStore';
import { useMerchantIdStore } from '@/app/store/merchant/merchantIdStore';
import { useInvestigationRedFlagsStore } from '@/app/store/merchant/InvestigationRedFlagsStore';
import { Globe, AlertCircle, Loader2, Package, MessageSquare } from 'lucide-react';
import { motion } from 'framer-motion';
import SectionHeaderWithRedFlags from '@/components/custom/SectionHeaderWithRedFlags';

// Import components
import { DomainInfoCard, WebsiteMonitoringCard, ReviewsCard, ProductsServicesCard, RiskNewsCard, WebsiteContentCard } from './components';

export const DigitalFootprintTab: FC = () => {
  const { digitalInformation, fetchDigitalInformation } = useInvestigationDigitalFootprintStore();
  const { selectedMerchantId } = useMerchantIdStore();
  const { flagsList, fetchFlagsList } = useInvestigationRedFlagsStore();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      if (selectedMerchantId) {
        setIsLoading(true);
        try {
          await Promise.all([
            fetchDigitalInformation(selectedMerchantId),
            fetchFlagsList(selectedMerchantId)
          ]);
        } finally {
          setIsLoading(false);
        }
      }
    };
    loadData();
  }, [selectedMerchantId]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  if (!digitalInformation) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  const { digital_information } = digitalInformation;

  // Add check for empty digital information
  const isEmptyData = !Object.values(digital_information).some(
    value => value && (
      (Array.isArray(value) && value.length > 0) ||
      (typeof value === 'object' && Object.values(value).some(v => v !== null))
    )
  );

  if (isEmptyData) {
    return (
      <div className="flex flex-col items-center justify-center h-64 space-y-4">
        <AlertCircle className="h-12 w-12 text-gray-400" />
        <p className="text-gray-500">No digital footprint data available</p>
      </div>
    );
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <motion.div 
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Domain Information Section */}
      <motion.div variants={itemVariants} className="space-y-2">
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2">
            <SectionHeaderWithRedFlags
              redFlags={flagsList}
              title="Website Functionality"
              icon={Globe}
              iconColorClass="text-blue-500"
              redFlag_recepient_id="investigation_digitalfootprint_w"
            />
            {digital_information.domain_information?.domain && (
              <div className="flex items-center gap-1 text-sm text-gray-500">
                <span>[{digital_information.domain_information.domain}]</span>
                <button 
                  onClick={() => navigator.clipboard.writeText(digital_information.domain_information.domain)}
                  className="text-blue-500 hover:text-blue-700"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                  </svg>
                </button>
              </div>
            )}
          </div>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <DomainInfoCard domainInfo={digital_information.domain_information} />
          <WebsiteMonitoringCard monitoring={digital_information.website_monitoring} />
        </div>
      </motion.div>

      {/* Website Content Section */}
      <motion.div variants={itemVariants} className="space-y-2">
        <SectionHeaderWithRedFlags
          redFlags={flagsList}
          title="Website Content Analysis"
          icon={Globe}
          iconColorClass="text-violet-500"
          redFlag_recepient_id="investigation_digitalfootprint_websitecontent"
        />
        <WebsiteContentCard content={digital_information.website_content} />
      </motion.div>

      {/* Website Content & Reviews Section */}
      <motion.div variants={itemVariants} className="space-y-2">
        <SectionHeaderWithRedFlags
          redFlags={flagsList}
          title="Reviews"
          icon={MessageSquare}
          iconColorClass="text-purple-500"
          redFlag_recepient_id="investigation_digitalfootprint_reviews"
        />
        <ReviewsCard reviews={digital_information.reviews} />
      </motion.div>

      {/* Products & Services Section */}
      <motion.div variants={itemVariants} className="space-y-2">
        <SectionHeaderWithRedFlags
          redFlags={flagsList}
          title="Products & Services"
          icon={Package}
          iconColorClass="text-indigo-500"
          redFlag_recepient_id="digital_footprint_products_services"
        />
        <ProductsServicesCard data={digital_information.products_and_services} />
      </motion.div>

      {/* Risk & News Section */}
      <motion.div variants={itemVariants} className="space-y-2">
        <SectionHeaderWithRedFlags
          redFlags={flagsList}
          title="Risk & News Analysis"
          icon={AlertCircle}
          iconColorClass="text-red-500"
          redFlag_recepient_id="digital_footprint_risk_news"
        />
        <RiskNewsCard data={digital_information.risk_and_news} />
      </motion.div>
    </motion.div>
  );
};