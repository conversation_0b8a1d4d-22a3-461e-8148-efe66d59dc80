import { FC } from 'react';
import { CustomCard } from '@/components/custom/CustomCard';
import { AlertTriangle, Calendar, Clock, User, Tag, Activity } from 'lucide-react';
import { formatTimestamp } from '@/utils/timeFormat';

interface CustomerComplaint {
  id: string;
  date: string;
  customerPhone: string;
  amount: string;
  transactionId: string;
  title: string;
  details: string;
}

interface Investigation {
  id: string;
  title: string;
  status: string;
  priority: string;
  assignee: string;
  lastUpdated: string;
  complaints?: CustomerComplaint[];
}

interface InvestigationArtifactProps {
  investigation: Investigation;
}

export const InvestigationArtifact: FC<InvestigationArtifactProps> = ({ investigation }) => {
  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'high':
        return 'text-red-600';
      case 'medium':
        return 'text-yellow-600';
      default:
        return 'text-green-600';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'in progress':
        return 'text-blue-600';
      case 'pending review':
        return 'text-purple-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <AlertTriangle className={`h-5 w-5 ${getPriorityColor(investigation.priority)}`} />
          <h2 className="text-xl font-semibold">{investigation.title}</h2>
        </div>
        <p className="text-sm text-gray-600">
          Investigation #{investigation.id} • Created {formatTimestamp(investigation.lastUpdated, 'relative')}
        </p>
      </div>

      {/* Meta Information */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <CustomCard className="p-4 space-y-2">
          <div className={`flex items-center gap-2 ${getStatusColor(investigation.status)}`}>
            <Activity className="h-4 w-4" />
            <p className="text-sm font-medium">Status</p>
          </div>
          <p className="text-sm">{investigation.status}</p>
        </CustomCard>

        <CustomCard className="p-4 space-y-2">
          <div className={`flex items-center gap-2 ${getPriorityColor(investigation.priority)}`}>
            <AlertTriangle className="h-4 w-4" />
            <p className="text-sm font-medium">Priority</p>
          </div>
          <p className="text-sm">{investigation.priority}</p>
        </CustomCard>

        <CustomCard className="p-4 space-y-2">
          <div className="flex items-center gap-2 text-blue-600">
            <User className="h-4 w-4" />
            <p className="text-sm font-medium">Assignee</p>
          </div>
          <p className="text-sm">{investigation.assignee}</p>
        </CustomCard>

        <CustomCard className="p-4 space-y-2">
          <div className="flex items-center gap-2 text-purple-600">
            <Clock className="h-4 w-4" />
            <p className="text-sm font-medium">Last Updated</p>
          </div>
          <p className="text-sm">{formatTimestamp(investigation.lastUpdated, 'relative')}</p>
        </CustomCard>
      </div>

      {/* Timeline Placeholder */}
      <CustomCard className="p-4">
        <h3 className="font-medium mb-3">Investigation Timeline</h3>
        <div className="space-y-3">
          <div className="flex gap-3">
            <div className="w-24 text-xs text-gray-500">
              {formatTimestamp(investigation.lastUpdated, 'relative')}
            </div>
            <div className="flex-1">
              <p className="text-sm">Investigation opened</p>
              <p className="text-xs text-gray-500">Assigned to {investigation.assignee}</p>
            </div>
          </div>
        </div>
      </CustomCard>

      {/* Customer Complaints */}
      {/* <CustomCard className="p-4">
        <h3 className="font-medium mb-3">Customer Complaints</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="text-left text-sm text-gray-500 border-b">
                <th className="pb-2 pr-8">Date</th>
                <th className="pb-2 pr-8">Customer</th>
                <th className="pb-2 pr-8">Amount</th>
                <th className="pb-2 pr-8">Transaction ID</th>
                <th className="pb-2">Summary</th>
              </tr>
            </thead>
            <tbody className="divide-y">
              {[
                {
                  id: 'C001',
                  date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
                  customerPhone: '98XX XX1234',
                  amount: '₹7,000',
                  transactionId: 'TXN789012345',
                  title: 'Fraudulent Hotel Booking',
                  details: 'Paid for hotel booking but no confirmation received. Hotel says no booking exists.'
                },
                {
                  id: 'C002',
                  date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
                  customerPhone: '87XX XX5678',
                  amount: '₹9,000',
                  transactionId: 'TXN789012346',
                  title: 'Fake Hotel Reservation',
                  details: 'Hotel booking confirmed but hotel denied existence of booking upon arrival.'
                },
                {
                  id: 'C003',
                  date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
                  customerPhone: '76XX XX9012',
                  amount: '₹7,000',
                  transactionId: 'TXN789012347',
                  title: 'Non-existent Booking',
                  details: 'No response after payment. Hotel has no record of booking.'
                },
                {
                  id: 'C004',
                  date: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
                  customerPhone: '63XX XX4567',
                  amount: '₹9,000',
                  transactionId: 'TXN789012348',
                  title: 'Booking Scam',
                  details: 'Premium hotel booking paid, hotel claims no association with merchant.'
                },
                {
                  id: 'C005',
                  date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
                  customerPhone: '89XX XX8901',
                  amount: '₹7,000',
                  transactionId: 'TXN789012349',
                  title: 'Fraudulent Transaction',
                  details: 'Booked for family vacation, hotel says merchant is not authorized reseller.'
                },
                {
                  id: 'C006',
                  date: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString(),
                  customerPhone: '72XX XX2345',
                  amount: '₹9,000',
                  transactionId: 'TXN789012350',
                  title: 'Fake Travel Package',
                  details: 'Paid for hotel package with activities. Neither hotel nor activities exist.'
                }
              ].map((complaint) => (
                <tr key={complaint.id} className="text-sm">
                  <td className="py-3 pr-8">{formatTimestamp(complaint.date, 'relative')}</td>
                  <td className="py-3 pr-8">{complaint.customerPhone}</td>
                  <td className="py-3 pr-8">{complaint.amount}</td>
                  <td className="py-3 pr-8">{complaint.transactionId}</td>
                  <td className="py-3 font-medium text-red-600">{complaint.title}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CustomCard> */}
    </div>
  );
}; 