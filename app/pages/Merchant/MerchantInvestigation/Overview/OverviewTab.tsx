'use client';

import { FC, useState, useEffect, useMemo } from 'react';
import { AlertTriangle, Bell, FileText, Activity, Loader2 } from 'lucide-react';
import { motion } from 'framer-motion';
import { useActiveContext } from '@/app/layout/ActiveContext/useActiveContext';
import { useCaseManagementStore } from '@/app/store/caseManagement/QueueManagerStore';
import { useMerchantIdStore } from '@/app/store/merchant/merchantIdStore';
import { useInvestigationOverviewStore } from '@/app/store/merchant/investicationOverviewStore';
import { ReportableSection } from '@/app/pages/ReportGeneration/utils/ReportSectionHelpers';
import { useInvestigationDigitalFootprintStore } from '@/app/store/merchant/investigationDigitalFootprintStore';
import { RedFlag } from '@/app/store/merchant/InvestigationRedFlagsStore';
import {
  RiskAssessmentSection,
  InvestigationsSection,
  MerchantHeader,
  MerchantSummary
} from '@/app/pages/Merchant/MerchantInvestigation/Overview/components';
import { KeyMetrics } from '@/components/custom/KeyMetrics';
import { BubbleTag } from '@/components/custom/BubbleTag';
import { formatTimestamp } from '@/utils/timeFormat';
import { BusinessMismatch } from './components/BusinessMismatch';
import { calculateRiskScore, getRiskLevel } from '@/app/data/hardcodeddata/sampleRedFlagsData';
import SectionHeaderWithRedFlags from '@/components/custom/SectionHeaderWithRedFlags';
import { useInvestigationRedFlagsStore } from '@/app/store/merchant/InvestigationRedFlagsStore';

export const OverviewTab: FC = () => {
  const { handleSelect, activeContexts } = useActiveContext();
  const { merchantIdList } = useMerchantIdStore();
  const { riskAssessment, keyMetricList, summary, fetchKeyMetricList, fetchRiskAssessment, fetchSummary } = useInvestigationOverviewStore();
  const { caseInvestigations } = useCaseManagementStore();
  const { digitalInformation, fetchDigitalInformation } = useInvestigationDigitalFootprintStore();
  const { flagsList, fetchFlagsList } = useInvestigationRedFlagsStore();

  const [isMetricsExpanded, setIsMetricsExpanded] = useState(false);
  const [isLoading, setIsLoading] = useState({
    metrics: true,
    risk: true,
    summary: true,
    digital: true,
    flags: true
  });

  const merchantId = useMemo(() => activeContexts?.merchant, [activeContexts?.merchant]);
  const activeMerchant = merchantIdList.find(m => m.id === merchantId);

  // Map and filter investigations
  const investigationItems = caseInvestigations?.investigations.map((investigation) => ({
    id: investigation.investigation_id,
    title: investigation.title,
    content: (
      <ReportableSection 
        type="single-investigation" 
        data={{ investigation, merchantId }}
      >
        <div className="grid grid-cols-[auto_1fr_auto] gap-4 p-3 cursor-pointer" onClick={() => {
          handleSelect('case', investigation.case_number, investigation.investigation_id);
        }}>
          {/* Column 1: Icon */}
          <div className="flex items-center">
            <AlertTriangle className={`h-4 w-4 ${
              investigation.priority === 'High' ? 'text-red-500' : 'text-yellow-500'
            }`} />
          </div>
          {/* Column 2: Case Info */}
          <div className="space-y-1 min-w-0">
            <span className="text-sm font-medium block truncate">
              Case #{investigation.case_number} - {investigation.title}
            </span>
            <p className="text-sm text-gray-600">Investigation details</p>
          </div>
          {/* Column 3: Status & Investigator */}
          <div className="flex flex-col items-end justify-between">
            <div className="flex items-center gap-2">
              <BubbleTag  
                text={investigation.status}
                color="blue"
              />
              <BubbleTag
                text={`${investigation.priority} Priority`}
                color={investigation.priority === 'High' ? 'red' : 'yellow'}
              />
              <span className="text-xs text-gray-500 ml-2">
                {formatTimestamp(investigation.created_at, 'relative')}      
              </span>
            </div>
            <span className="text-xs text-gray-500 mt-2">
              Investigator: {investigation.assignee_Name}
            </span>
          </div>
        </div>
      </ReportableSection>
    ),
    metadata: {
      caseNumber: investigation.case_number,
      description: 'Investigation details',
      title: investigation.title,
      status: investigation.status,
      priority: investigation.priority,
      assignee: investigation.assignee_Name,
      lastUpdated: investigation.created_at,
      channel: 'Direct',
      type: 'Compliance',
      id: investigation.investigation_id
    }
  })) || [];

  const activeInvestigations = investigationItems.filter(
    item => (item.metadata as any).status.toLowerCase() === 'open' || (item.metadata as any).status.toLowerCase() === 'in progress'
  );

  const pastInvestigations = investigationItems.filter(
    item => (item.metadata as any).status.toLowerCase() === 'closed'
  );

  useEffect(() => {
    if (merchantId) {
      // Reset all loading states
      setIsLoading({
        metrics: true,
        risk: true,
        summary: true,
        digital: true,
        flags: true
      });

      // Individual API calls with their own loading states
      fetchKeyMetricList(merchantId)
        .then(() => setIsLoading(prev => ({ ...prev, metrics: false })))
        .catch(() => setIsLoading(prev => ({ ...prev, metrics: false })));

      fetchRiskAssessment(merchantId)
        .then(() => setIsLoading(prev => ({ ...prev, risk: false })))
        .catch(() => setIsLoading(prev => ({ ...prev, risk: false })));

      fetchSummary(merchantId)
        .then(() => setIsLoading(prev => ({ ...prev, summary: false })))
        .catch(() => setIsLoading(prev => ({ ...prev, summary: false })));

      fetchDigitalInformation(merchantId)
        .then(() => setIsLoading(prev => ({ ...prev, digital: false })))
        .catch(() => setIsLoading(prev => ({ ...prev, digital: false })));

      fetchFlagsList(merchantId)
        .then(() => setIsLoading(prev => ({ ...prev, flags: false })))
        .catch(() => setIsLoading(prev => ({ ...prev, flags: false })));
    }
  }, [merchantId, fetchKeyMetricList, fetchRiskAssessment, fetchSummary, fetchDigitalInformation, fetchFlagsList]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  const actualBusiness = useMemo(() => {
    return digitalInformation?.digital_information?.products_and_services?.overview?.[0]?.description ?? '';
  }, [digitalInformation]);
  const textileMCADescription = "Not available";
  const websiteBusinessDescription = "Join the future of wealth creation with our revolutionary crypto-trading platform. Guaranteed 25% returns every month through our AI-powered betting system. Start small with ₹10,000 and watch your investment multiply through smart crypto arbitrage and sports betting strategies. Quick withdrawals, assured profits!";
  

  const riskScore = useMemo(() => {
    const severityWeights = {
      'critical': 4,
      'high': 3,
      'medium': 2,
      'low': 1
    };
    return flagsList.reduce((score, flag) => {
      const weight = severityWeights[flag.severity.toLowerCase() as keyof typeof severityWeights] || 1;
      return score + weight;
    }, 0);
  }, [flagsList]);
  const riskLevel = useMemo(() => getRiskLevel(riskScore), [riskScore]);
  const criticalRedFlags = useMemo(() => flagsList.filter(flag => flag.severity.toLowerCase() === 'critical'), [flagsList]);
  const highRedFlags = useMemo(() => flagsList.filter(flag => flag.severity.toLowerCase() === 'high'), [flagsList]);
  const mediumRedFlags = useMemo(() => flagsList.filter(flag => flag.severity.toLowerCase() === 'medium'), [flagsList]);
  const lowRedFlags = useMemo(() => flagsList.filter(flag => flag.severity.toLowerCase() === 'low'), [flagsList]);

  // Mock merchant summary data for now
  const mockMerchantSummary = {
    industry_type: 'Hospitality',
    onboarding_date: '2020-01-01',
    onboarding_channel: 'Direct',
    product_summary: digitalInformation?.digital_information?.products_and_services?.overview?.[0]?.description ?? 'No product summary available',
    merchant_city: 'Ayodhya',
    complaint_count: 0,
    total_transactions: 0,
    dispute_count: 0,
    chargeback_percentage: 0,
    risk_segmentation: riskLevel,
    red_flags: {
      critical: criticalRedFlags.length,
      high: highRedFlags.length,
      medium: mediumRedFlags.length,
      low: lowRedFlags.length
    }
  };

  const mappedRedFlags: RedFlag[] = useMemo(() => {
    return flagsList.map(flag => ({
      id: flag.id || '',
      merchant_id: flag.merchant_id || '',
      rule_code: flag.rule_code || '',
      rule_type: flag.rule_type || '',
      severity: flag.severity || 'low',
      description: flag.description || '',
      created_at: flag.created_at || new Date().toISOString(),
      updated_at: flag.updated_at || new Date().toISOString(),
      metric_values: flag.metric_values || null,
      rule: flag.rule || null,
      metric_data_timestamp: flag.metric_data_timestamp || new Date().toISOString(),
      notes: flag.notes || null
    }));
  }, [flagsList]);

  const LoadingSpinner = () => (
    <div className="flex justify-center items-center p-8">
      <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
    </div>
  );

  return (
    <motion.div 
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div variants={itemVariants}>
        {activeMerchant && (
          <>
            <MerchantHeader activeMerchant={activeMerchant} />
            <div className="mt-4">
              {isLoading.summary ? (
                <LoadingSpinner />
              ) : (
                <MerchantSummary
                  activeMerchant={activeMerchant}
                  merchantSummary={mockMerchantSummary}
                />
              )}
            </div>
          </>
        )}
      </motion.div>

      <motion.div variants={itemVariants}>
        {isLoading.digital ? (
          <LoadingSpinner />
        ) : (
          <BusinessMismatch
            actualBusiness={actualBusiness ?? ''}
            mcaBusiness={textileMCADescription}
            websiteBusiness={websiteBusinessDescription}
          />
        )}
      </motion.div>
      
      <motion.div variants={itemVariants}>
        <ReportableSection type="risk-score" data={{ riskAssessment, merchantId, keyMetricList }}>
          <SectionHeaderWithRedFlags
            redFlags={mappedRedFlags}
            title="Risk Assessment"
            icon={AlertTriangle}
            iconColorClass="text-red-600"
            redFlag_recepient_id="investigation_overview_risk"
          />
          {isLoading.risk || isLoading.flags ? (
            <LoadingSpinner />
          ) : (
            <RiskAssessmentSection
              riskAssessment={riskAssessment}
              keyMetricList={keyMetricList ?? { key_metrics: [] }}
            />
          )}
        </ReportableSection>
      </motion.div>

      <motion.div variants={itemVariants}>
        <ReportableSection type="key-stats" data={{ keyMetricList, merchantId }}>
          <SectionHeaderWithRedFlags
            redFlags={mappedRedFlags}
            title="Key Metrics"
            icon={Activity}
            iconColorClass="text-blue-600"
            redFlag_recepient_id="investigation_overview_keymetrics"
          />
          {isLoading.metrics ? (
            <LoadingSpinner />
          ) : (
            <KeyMetrics
              keyMetricList={keyMetricList ?? { key_metrics: [] }}
              isMetricsExpanded={isMetricsExpanded}
              setIsMetricsExpanded={setIsMetricsExpanded}
              showHeader={false}
            />
          )}
        </ReportableSection>
      </motion.div>

      <motion.div variants={itemVariants} className="space-y-3">
        <SectionHeaderWithRedFlags
          redFlags={mappedRedFlags}
          title="Active Investigations"
          icon={Bell}
          iconColorClass="text-orange-500"
          redFlag_recepient_id="overview_active_investigations"
        />
        {isLoading.flags ? (
          <LoadingSpinner />
        ) : (
          <>
            <InvestigationsSection
              investigations={activeInvestigations}
              title="Active Investigations"
              icon={Bell}
              iconColorClass="text-orange-500"
            />

            <SectionHeaderWithRedFlags
              redFlags={mappedRedFlags}
              title="Past Investigations"
              icon={FileText}
              iconColorClass="text-teal-600"
              redFlag_recepient_id="overview_past_investigations"
            />
            <InvestigationsSection
              investigations={pastInvestigations}
              title="Past Investigations"
              icon={FileText}
              iconColorClass="text-teal-600"
            />
          </>
        )}
      </motion.div>
    </motion.div>
  );
};