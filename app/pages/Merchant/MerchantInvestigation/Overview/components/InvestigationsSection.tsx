import { FC } from 'react';
import { motion } from 'framer-motion';
import { TabSectionHeading } from '@/components/custom/TabSectionHeading';
import { VirtualList } from '@/components/custom/VirtualList';
import { EmptyState } from './EmptyState';
import { Card } from '@/components/ui/card';

interface InvestigationsSectionProps {
  investigations: any[];
  title: string;
  icon: any;
  iconColorClass: string;
}

export const InvestigationsSection: FC<InvestigationsSectionProps> = ({
  investigations,
  title,
  icon,
  iconColorClass
}) => {

  const hasData = investigations.length > 0;


  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  // Calculate dynamic viewport height based on investigations length
  const calculateViewportHeight = () => {
    const itemHeight = 80;
    const maxItems = 5; // Maximum number of items to show before scrolling
    const minHeight = 160; // Minimum height (2 items)
    
    const calculatedHeight = Math.min(investigations.length * itemHeight, maxItems * itemHeight);
    return Math.max(calculatedHeight, minHeight);
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-2"
    >
      {/* <motion.div variants={itemVariants}>
        <TabSectionHeading icon={icon} iconColorClass={iconColorClass}>
          {title}
        </TabSectionHeading>
      </motion.div> */}
      <motion.div variants={itemVariants}>
        {hasData ? <VirtualList
          items={investigations}
          itemHeight={80}
          viewportHeight={calculateViewportHeight()}
          className="w-full"
        /> : <Card className="p-4"><EmptyState message={`No ${title} available`} /></Card>}
      </motion.div>
    </motion.div>
  );
};