import { FC } from 'react';
import { motion } from 'framer-motion';
import { MerchantItemType } from '@/app/types';
import { Building2 } from 'lucide-react';
import SectionHeaderWithRedFlags from '@/components/custom/SectionHeaderWithRedFlags';
import { useInvestigationRedFlagsStore } from '@/app/store/merchant/InvestigationRedFlagsStore';

interface MerchantSummaryProps {
  activeMerchant: MerchantItemType;
  merchantSummary: {
    industry_type: string;
    onboarding_date: string;
    onboarding_channel: string;
    product_summary: string;
    merchant_city: string;
    complaint_count: number;
    total_transactions: number;
    lea_notification_date?: string;
    lea_email_summary?: string;
    dispute_count: number;
    chargeback_percentage: number;
    risk_segmentation: string;
    red_flags: {
      critical: number;
      high: number;
      medium: number;
      low: number;
    };
  };
}

export const MerchantSummary: FC<MerchantSummaryProps> = ({ activeMerchant, merchantSummary }) => {
  const { flagsList } = useInvestigationRedFlagsStore();

  const formatSummary = () => {
    const sections: {
      intro: string;
      complaints: string[];
      risk: string;
    } = {
      intro: '',
      complaints: [],
      risk: ''
    };
    
    // Intro Section
    sections.intro = `This Merchant is in `
      + `<span class="text-blue-600">${merchantSummary.industry_type}</span> Industry, onboarded on `
      + `<span class="text-blue-600">${merchantSummary.onboarding_date}</span> from the channel `
      + `<span class="text-blue-600">${merchantSummary.onboarding_channel}</span>. `
      + `<span class="text-blue-600">${merchantSummary.product_summary}</span> and is based in `
      + `<span class="text-blue-600">${merchantSummary.merchant_city}</span>.`;
    
    // Complaints/Notification Section
    const complaintPercentage = ((merchantSummary.complaint_count / merchantSummary.total_transactions) * 100).toFixed(2);
    sections.complaints = [
      `A total of <span class="text-blue-600">${merchantSummary.complaint_count}</span> customer complaints (<span class="text-blue-600">${complaintPercentage}%</span> of total txn) have been received against the merchant.`,
      merchantSummary.lea_notification_date && merchantSummary.lea_email_summary ? 
        `We received a notification from LEA against the merchant on <span class="text-blue-600">${merchantSummary.lea_notification_date}</span>, stating <span class="text-blue-600">${merchantSummary.lea_email_summary}</span>` : null,
      `A total of <span class="text-blue-600">${merchantSummary.dispute_count}</span> disputes have been received against the merchant, of which <span class="text-blue-600">${merchantSummary.chargeback_percentage}%</span> have been charged back.`
    ].filter((item): item is string => item !== null);
    
    // Risk Summary Section
    const { critical, high, medium, low } = merchantSummary.red_flags;
    sections.risk = `The merchant has a <span class="text-blue-600">${merchantSummary.risk_segmentation}</span> risk segmentation due to `
      + `<span class="text-blue-600">${critical}</span> critical, `
      + `<span class="text-blue-600">${high}</span> high, `
      + `<span class="text-blue-600">${medium}</span> medium and `
      + `<span class="text-blue-600">${low}</span> low risk red flags.`;
    
    return sections;
  };

  return (
    <>
      <SectionHeaderWithRedFlags
        redFlags={flagsList}
        title="Company"
        icon={Building2}
        iconColorClass="text-blue-600"
        redFlag_recepient_id="investigation_overview_company"
      />
      <motion.div
        className="space-y-2"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        {/* Intro Section */}
        <div>
          <h3 className="font-semibold text-gray-900 mb-2">Intro:</h3>
          <p className="leading-relaxed" dangerouslySetInnerHTML={{ __html: formatSummary().intro }} />
        </div>

        {/* Complaints Section */}
        <div>
          <h3 className="font-semibold text-gray-900 mb-2">Complaints/Notification summary:</h3>
          <div className="space-y-2">
            {formatSummary().complaints.map((complaint, index) => (
              <p key={index} className="leading-relaxed" dangerouslySetInnerHTML={{ __html: complaint }} />
            ))}
          </div>
        </div>

        {/* Risk Summary Section */}
        <div>
          <h3 className="font-semibold text-gray-900 mb-2">Risk Summary:</h3>
          <p className="leading-relaxed" dangerouslySetInnerHTML={{ __html: formatSummary().risk }} />
        </div>
      </motion.div>
    </>
  );
}; 