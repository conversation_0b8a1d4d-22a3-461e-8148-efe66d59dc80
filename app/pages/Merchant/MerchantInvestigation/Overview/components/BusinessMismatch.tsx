import { Info, ChevronsRight } from 'lucide-react';
import { useArtifactStore } from '@/app/store/artifact/artifactStore';
import { BusinessMismatchArtifact } from './BusinessMismatchArtifact';
interface BusinessMismatchProps {
  actualBusiness: string;
  mcaBusiness: string;
  websiteBusiness: string;
}

export const BusinessMismatch = ({ actualBusiness, mcaBusiness, websiteBusiness }: BusinessMismatchProps) => {
  const { addTab, setActiveTabId, setCollapsed } = useArtifactStore();
  const handleMismatchClick = () => {
    addTab({
      id: 'business-mismatch',
      title: 'Business Description Mismatch Analysis',
      renderArtifact: () => (
        <BusinessMismatchArtifact actualBusiness={actualBusiness} mcaBusiness={mcaBusiness} websiteBusiness={websiteBusiness} />
      )
    });
    setActiveTabId('business-mismatch');
    setCollapsed(false);
  };

  return <div 
  className="p-3 rounded-lg border border-gray-100 cursor-pointer hover:border-gray-200 hover:shadow-sm transition-all"
  onClick={handleMismatchClick}
>
  <div className="flex items-center justify-between ">
    <div className="flex items-center gap-1 text-red-600">
      <Info className="h-4 w-4" />
      <span className="text-xs">Business Description Comparison</span>
    </div>
      <ChevronsRight className="h-4 w-4 text-gray-400" />
    </div>
  </div>
}