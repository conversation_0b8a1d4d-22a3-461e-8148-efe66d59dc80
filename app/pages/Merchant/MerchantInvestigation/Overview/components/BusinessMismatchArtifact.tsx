import { FC } from 'react';
import { AlertTriangle, Building2, Globe } from 'lucide-react';
import { formatTimestamp } from '@/utils/timeFormat';

interface BusinessMismatchArtifactProps {
  actualBusiness: string;
  mcaBusiness: string;
  websiteBusiness: string;
}

export const BusinessMismatchArtifact: FC<BusinessMismatchArtifactProps> = ({ 
  actualBusiness, 
  mcaBusiness,
  websiteBusiness
}) => {
  const techHighlights = ['software', 'IT', 'consulting', 'technology', 'digital'];
  const textileHighlights = ['textiles', 'manufacturing', 'trading', 'fabrics', 'garments'];
  const websiteHighlights = ['platform', 'solutions', 'services', 'enterprise', 'cloud'];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            <h2 className="text-xl font-semibold">Business Description Mismatch Analysis</h2>
          </div>
          <p className="text-sm text-gray-500">
            last verified {formatTimestamp(new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), 'relative')}
          </p>
        </div>
        <p className="text-sm text-gray-600">
          Critical discrepancy detected between business descriptions across different sources
        </p>
      </div>

      <div className="space-y-6">
        <div>
          <div className="flex items-center gap-2 mb-3">
            <Building2 className="h-4 w-4 text-blue-600" />
            <p className="font-medium text-gray-700">Actual Business Operations</p>
          </div>
          <div className="p-4 rounded-lg bg-blue-50 border border-blue-100">
            <p>{actualBusiness}</p>
          </div>
        </div>
        <div>
          <div className="flex items-center gap-2 mb-3">
            <Building2 className="h-4 w-4 text-purple-600" />
            <p className="font-medium text-gray-700">MCA Registration</p>
          </div>
          <div className="p-4 rounded-lg bg-purple-50 border border-purple-100">
            <p>{mcaBusiness}</p>
          </div>
        </div>
        <div>
          <div className="flex items-center gap-2 mb-3">
            <Globe className="h-4 w-4 text-green-600" />
            <p className="font-medium text-gray-700">Website Description</p>
          </div>
          <div className="p-4 rounded-lg bg-green-50 border border-green-100">
            <p>{websiteBusiness}</p>
          </div>
        </div>
      </div>
    </div>
  );
}; 