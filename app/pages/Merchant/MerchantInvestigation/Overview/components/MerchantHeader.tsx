import { FC, useCallback } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'lucide-react';
import { motion } from 'framer-motion';
import { MerchantItemType } from '@/app/types';
import { Button } from '@/components/ui/button';
import { useChatStore } from '@/app/store/chat/chatStore';

interface MerchantHeaderProps {
  activeMerchant: MerchantItemType;
}

// We don't need a separate error component anymore as we're using the built-in flow
export const MerchantHeader: FC<MerchantHeaderProps> = ({ activeMerchant }) => {
  const copyMerchantId = () => {
    navigator.clipboard.writeText(activeMerchant.id);
  };

  const { getNewChatId, sendMessage, addMessage, setLoading } = useChatStore();

  // Use useCallback to ensure this doesn't cause re-renders
  const handleCreateAIReport = useCallback(async () => {
    try {
      setLoading(true);
      
      // Creating a new chat - exactly like in InvestigationGPT.tsx
      const newChatId = await getNewChatId(false);
      
      if (!newChatId) {
        console.error("Failed to create new chat");
        setLoading(false);
        return;
      }
      
      // Prepare the initial message
      const initialMessage = "Create a Report with Company details";
      
      // Add user message to the store - This will trigger the useEffect in InvestigationGPT
      // which creates the tab automatically
      addMessage({
        text: initialMessage,
        isUser: true
      });
      
      // Send the message after a delay, just like in handleCreateNewChat
      setTimeout(async () => {
        try {
          console.log("Sending initial message to chat:", initialMessage);
          await sendMessage(initialMessage);
          console.log("Initial message sent successfully");
          setLoading(false);
        } catch (error) {
          console.error("Error sending initial message:", error);
          setLoading(false);
        }
      }, 200);
      
    } catch (error) {
      console.error("Error creating AI report:", error);
      setLoading(false);
    }
  }, [addMessage, getNewChatId, sendMessage, setLoading]);

  return (
    <motion.div
      className="space-y-2"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h2 className="text-xl font-semibold text-blue-600">{activeMerchant.legalName}</h2>
          <div className="flex items-center gap-1 text-sm text-gray-500">
            <span>[{activeMerchant.id}]</span>
            <button 
              onClick={copyMerchantId}
              className="text-blue-500 hover:text-blue-700"
            >
              <Copy className="h-4 w-4" />
            </button>
          </div>
        </div>
        {/* <Button
          onClick={handleCreateAIReport}
          variant="outline"
          size="lg"
          className="gap-2 bg-blue-50 text-blue-600 hover:bg-blue-100 border-blue-200 text-base"
        >
          Create Report with AI
        </Button> */}
      </div>
    </motion.div>
  );
};