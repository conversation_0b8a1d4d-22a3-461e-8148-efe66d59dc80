import { Activity, Shield, Network, MessageSquare, AlertTriangle, Globe, AlertCircle } from "lucide-react";
import { AccordionList } from '@/components/custom/AccordionList';
import { StandardListItemProps } from '@/components/custom/StandardList';
import { FC, useEffect } from 'react';
import { useWorkspace } from '@/app/layout/Workspace/WorkspaceContext';
import { BubbleTag } from '@/components/custom/BubbleTag';
import { formatTimestamp } from '@/utils/timeFormat';
import { useInvestigationRedFlagsStore } from '@/app/store/merchant/InvestigationRedFlagsStore';
import React from 'react';
import { useMerchantIdStore } from "@/app/store/merchant/merchantIdStore";
import { redFlagsData } from "@/app/data/hardcodeddata/sampleRedFlagsData";

interface FlagItemProps {
  flag: {
    severity: string;
    text?: string;
    timestamp?: string;
    flag_type?: string;
    importance?: number;
    description?: string;
    created_at?: string;
  };
}

export const FlagItem = ({ flag }: FlagItemProps) => {
  const getSeverityConfig = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical':
        return {
          icon: <AlertTriangle className="h-4 w-4" />,
          color: 'red' as const,
          text: 'Critical'
        };
      case 'high':
        return {
          icon: <AlertTriangle className="h-4 w-4" />,
          color: 'orange' as const,
          text: 'High'
        };
      case 'medium':
        return {
          icon: <AlertTriangle className="h-4 w-4" />,
          color: 'yellow' as const,
          text: 'Medium'
        };
      default:
        return {
          icon: <AlertTriangle className="h-4 w-4" />,
          color: 'gray' as const,
          text: 'Low'
        };
    }
  };

  const config = getSeverityConfig(flag.severity);
  const isML = flag.flag_type?.endsWith('ML') || false;
  const displayText = flag.text || flag.description || 'No description';
  const timestamp = flag.timestamp || flag.created_at || new Date().toISOString();

  return (
    <div className="flex items-center gap-3 p-2 rounded-md">
      <BubbleTag
        hasOutsideIcon
        icon={config.icon}
        text={config.text}
        color={config.color}
      />
      <div className="flex-grow min-w-0">
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-900 flex items-center gap-2">
            {displayText}
            {isML ? <BubbleTag text="Anomaly" color="blue" /> : <BubbleTag text="Rule" color="blue" />}
          </div>
          <div className="flex items-center gap-2">
            <p className="text-xs text-gray-500">{formatTimestamp(timestamp)}</p>
            {flag.importance && <BubbleTag text={flag.importance.toFixed(2)} color="red" />}
          </div>
        </div>
      </div>
    </div>
  );
};

const FlagArtifact: FC<FlagItemProps> = ({ flag }) => {
  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical':
        return 'text-red-700';
      case 'high':
        return 'text-orange-700';
      case 'medium':
        return 'text-yellow-700';
      default:
        return 'text-gray-700';
    }
  };

  const displayText = flag.text || flag.description || 'No description';

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <h2 className="text-xl font-semibold">Flag Details</h2>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <p className="text-sm text-gray-500">Severity</p>
          <p className={`text-sm capitalize ${getSeverityColor(flag.severity)}`}>
            {flag.severity}
          </p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Description</p>
          <p className="text-sm">{displayText}</p>
        </div>
      </div>
    </div>
  );
};

interface FlagListProps {
  openAccordions: string[];
  setOpenAccordions: (value: string[]) => void;
}

export const FlagList = ({ openAccordions, setOpenAccordions }: FlagListProps) => {
  // const { setArtifact } = useWorkspace();
  const { fetchFlagsList } = useInvestigationRedFlagsStore();
  const { selectedMerchantId } = useMerchantIdStore();

  const flagsList = redFlagsData;
  useEffect(() => {
    // console.log("FlagList mounted");
    // if (selectedMerchantId) {
    //   fetchFlagsList(selectedMerchantId);
    // }
  }, [fetchFlagsList, selectedMerchantId]);

  const groupedFlags = React.useMemo(() => {
    if (!flagsList?.flags) return {};
    
    return flagsList.flags.reduce((acc, flag) => {
      const category = flag.flag_type?.toLowerCase() || 'other';
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(flag);
      return acc;
    }, {} as Record<string, typeof flagsList.flags>);
  }, [flagsList]);

  const handleItemClick = (item: StandardListItemProps) => {
    // setArtifact({
    //   ...item.metadata,
    //   id: item.id,
    //   title: item.title
    // });
  };

    const getSeverityConfig = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical':
        return {
          icon: <AlertTriangle className="h-4 w-4" />,
          color: 'red' as const,
          text: 'Critical'
        };
      case 'high':
        return {
          icon: <AlertTriangle className="h-4 w-4" />,
          color: 'orange' as const,
          text: 'High'
        };
      case 'medium':
        return {
          icon: <AlertTriangle className="h-4 w-4" />,
          color: 'blue' as const,
          text: 'Medium'
        };
      default:
        return {
          icon: <AlertTriangle className="h-4 w-4" />,
          color: 'green' as const,
          text: 'Low'
        };
    }
  };

  const getFlagCounts = (flags: Array<{ severity: string }>) => {
    const counts = flags.reduce((acc, flag) => {
      acc[flag.severity] = (acc[flag.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    return (
      <div className="flex gap-2">
        {Object.entries(counts).map(([severity, count]) => {
          const config = getSeverityConfig(severity);
          return (
            <BubbleTag
              key={severity}
              hasInsideNumber
              number={count}
              text={config.text}
              color={config.color}
            />
          );
        })}
      </div>
    );
  };

  const convertToStandardListItems = (flags: typeof groupedFlags['transactions']): StandardListItemProps[] => {
    return flags.map((flag, index) => ({
      id: `${index}`,
      title: flag.text || flag.description || 'Flag ' + index,
      content: <FlagItem flag={flag} />,
      metadata: {
        ...flag,
        renderArtifact: () => <FlagArtifact flag={flag} />
      }
    }));
  };

  const sections = React.useMemo(() => {
    if (!flagsList?.flags) return [];

    const normalizeType = (type: string | undefined) => {
      if (!type) return 'other';
      return type.replace(/ ML$/, '');
    };

    const uniqueTypes = [...new Set(flagsList.flags.map(flag => normalizeType(flag.flag_type)))];

    const sectionConfigs = {
      "transactions": {
        label: 'Transaction Patterns',
        icon: Activity
      },
      'Legal and Regulatory': {
        label: 'Legal and Regulatory',
        icon: Shield
      },
      "Network": {
        label: 'Network Analysis',
        icon: Network
      },
      "Documentation": {
        label: "Documentation Red-Flag",
        icon: MessageSquare
      },
      'DigitallFootprint': {
        label: 'Digital Footprint',
        icon: Globe
      },
      default: {
        label: 'Other Flags',
        icon: AlertCircle
      }
    };

    return uniqueTypes.map(normalizedType => {
      const config = sectionConfigs[normalizedType as keyof typeof sectionConfigs] || sectionConfigs.default;
      
      const flagsOfType = flagsList.flags.filter(flag => 
        normalizeType(flag.flag_type) === normalizedType
      );

      return {
        id: normalizedType,
        label: config.label,
        icon: config.icon,
        items: convertToStandardListItems(flagsOfType),
        badges: getFlagCounts(flagsOfType),
        onItemClick: handleItemClick
      };
    });
  }, [flagsList]);

  return (
    <AccordionList 
      sections={sections}
      openSections={openAccordions}
      setOpenSections={setOpenAccordions}
    />
  );
};