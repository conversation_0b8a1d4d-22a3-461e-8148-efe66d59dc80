import { useState, FC } from 'react';
import { FlagList } from './FlagList';
import { RedFlagsTimeline } from './RedFlagsTimeline';
import { Button } from '@/components/ui/button';
import { Clock, LayoutList } from 'lucide-react';

export const RedFlagsTab : FC = () => {
  const [openAccordions, setOpenAccordions] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<string>('timeline');

  return (
    <div className="h-full">
      <div className="flex justify-end mb-4">
        <div className="flex space-x-2 items-center">
          <Button
            variant={viewMode === 'timeline' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('timeline')}
            className="h-9 gap-1 text-xs"
          >
            <Clock className="h-4 w-4" />
            <span>Timeline</span>
          </Button>
          <Button
            variant={viewMode === 'grouped' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('grouped')}
            className="h-9 gap-1 text-xs"
          >
            <LayoutList className="h-4 w-4" />
            <span>Grouped</span>
          </Button>
        </div>
      </div>
      
      {viewMode === 'timeline' ? (
        <RedFlagsTimeline />
      ) : (
        <FlagList 
          openAccordions={openAccordions}
          setOpenAccordions={setOpenAccordions}
        />
      )}
    </div>
  );
};
