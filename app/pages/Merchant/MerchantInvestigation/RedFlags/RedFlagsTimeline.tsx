import { FC, useEffect, useMemo, useState } from 'react';
import { VirtualList, VirtualListItemProps } from '@/components/custom/VirtualList';
import { useInvestigationRedFlagsStore, RedFlag } from '@/app/store/merchant/InvestigationRedFlagsStore';
import { useMerchantIdStore } from '@/app/store/merchant/merchantIdStore';
import { Flag, AlertCircle, AlertOctagon, AlertTriangle, FilterX, RefreshCw } from 'lucide-react';
import { format } from 'date-fns';
import { motion } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import { BubbleTag } from '@/components/custom/BubbleTag';
import { Button } from '@/components/ui/button';
import { MultiSelect } from '@/components/ui/multi-select2';
import { useArtifactStore } from '@/app/store/artifact/artifactStore';

// FlagTimelineItem component 
const FlagTimelineItem: FC<{ flag: RedFlag }> = ({ flag }) => {
  const getSeverityConfig = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical':
        return {
          icon: <AlertTriangle className="h-4 w-4" />,
          color: 'red' as const,
          text: 'Critical'
        };
      case 'high':
        return {
          icon: <AlertTriangle className="h-4 w-4" />,
          color: 'orange' as const,
          text: 'High'
        };
      case 'medium':
        return {
          icon: <AlertTriangle className="h-4 w-4" />,
          color: 'yellow' as const,
          text: 'Medium'
        };
      default:
        return {
          icon: <AlertTriangle className="h-4 w-4" />,
          color: 'gray' as const,
          text: 'Low'
        };
    }
  };

  const config = getSeverityConfig(flag.severity);
  const isML = flag.rule_type.endsWith('ML');

  return (
    <motion.div 
      className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-md transition-colors duration-200"
      initial={{ opacity: 0, y: 5 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
    >
      <div className="flex items-center gap-3">
        <div className="flex flex-col">
          <div className="flex items-center gap-2">
            <div className="text-sm font-medium text-gray-900">{flag.description}</div>
            <div className="flex gap-1">
              {isML ? <BubbleTag text="Anomaly" color="blue" /> : <BubbleTag text="Rule" color="blue" />}
              <BubbleTag text={config.text} color={config.color} />
            </div>
          </div>
          <div className="text-xs text-gray-500 mt-1">
            {format(new Date(flag.created_at), 'MMM dd, yyyy')} • {flag.rule_type}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

// Define the detail view for a selected flag
const FlagDetailView: FC<{ flag: RedFlag }> = ({ flag }) => {
  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical':
        return 'text-red-700';
      case 'high':
        return 'text-orange-700';
      case 'medium':
        return 'text-yellow-700';
      default:
        return 'text-gray-700';
    }
  };

  return (
    <div className="space-y-4 p-4">
      <div className="flex items-center gap-2">
        <h2 className="text-xl font-semibold">Flag Details</h2>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <p className="text-sm text-gray-500">Severity</p>
          <p className={`text-sm capitalize ${getSeverityColor(flag.severity)}`}>
            {flag.severity}
          </p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Description</p>
          <p className="text-sm">{flag.description}</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Type</p>
          <p className="text-sm">{flag.rule_type}</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Created At</p>
          <p className="text-sm">{format(new Date(flag.created_at), 'MMM dd, yyyy')}</p>
        </div>
        {flag.metric_values && Object.entries(flag.metric_values).length > 0 && (
          <div className="col-span-2">
            <p className="text-sm text-gray-500">Metric Values</p>
            <div className="grid grid-cols-2 gap-2 mt-1">
              {Object.entries(flag.metric_values).map(([key, value]) => (
                <div key={key}>
                  <p className="text-xs text-gray-500">{key}</p>
                  <p className="text-sm">{value}</p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Define the types of filters available
const filterOptions = [
  { value: 'transactions', label: 'Transactions', icon: Flag },
  { value: 'legal', label: 'Legal & Regulatory', icon: Flag },
  { value: 'network', label: 'Network Analysis', icon: Flag },
  { value: 'documentation', label: 'Documentation', icon: Flag },
  { value: 'digital', label: 'Digital Footprint', icon: Flag },
  { value: 'dispute', label: 'Dispute & Complaints', icon: Flag },
  { value: 'chargeback', label: 'Chargeback', icon: Flag }
];

// Define severity filter options
const severityOptions = [
  { value: 'critical', label: 'Critical', icon: AlertOctagon },
  { value: 'high', label: 'High', icon: AlertTriangle },
  { value: 'medium', label: 'Medium', icon: AlertCircle },
  { value: 'low', label: 'Low', icon: Flag }
];

// Main timeline component
export const RedFlagsTimeline: FC = () => {
  const { 
    flagsList, 
    fetchFlagsList,
    filterTypes,
    filterSeverities,
    setFilterTypes,
    setFilterSeverities,
    clearFilters
  } = useInvestigationRedFlagsStore();
  const { selectedMerchantId } = useMerchantIdStore();
  const { addTab, setActiveTabId, setCollapsed } = useArtifactStore();
  const [isLoading, setIsLoading] = useState(false);

  // Fetch data on initial render and when merchant changes
  useEffect(() => {
    if (selectedMerchantId) {
      fetchFlagsList(selectedMerchantId);
    }
  }, [fetchFlagsList, selectedMerchantId]);

  // Handle refreshing the data
  const handleRefresh = async () => {
    if (selectedMerchantId) {
      setIsLoading(true);
      await fetchFlagsList(selectedMerchantId);
      setIsLoading(false);
    }
  };

  // Process and filter flags for timeline display
  const timelineItems = useMemo(() => {
    if (!flagsList) return [];

    let filteredFlags = [...flagsList];

    // Apply type filters
    if (filterTypes.length > 0) {
      filteredFlags = filteredFlags.filter(flag => {
        const flagType = flag.rule_type.toLowerCase();
        return filterTypes.some(type => flagType.includes(type));
      });
    }

    // Apply severity filters
    if (filterSeverities.length > 0) {
      filteredFlags = filteredFlags.filter(flag => 
        filterSeverities.includes(flag.severity.toLowerCase())
      );
    }

    // Sort by timestamp (newest first)
    filteredFlags.sort((a, b) => 
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );

    // Convert to VirtualList format
    return filteredFlags.map(flag => ({
      id: flag.id,
      title: flag.description,
      content: <FlagTimelineItem flag={flag} />,
      metadata: { flag }
    }));
  }, [flagsList, filterTypes, filterSeverities]);

  // Handle flag item click to show details
  const handleFlagClick = (item: VirtualListItemProps) => {
    const flag = item.metadata.flag as RedFlag;
    addTab({
      id: `flag-${flag.id}`,
      title: `Flag: ${flag.description.slice(0, 30)}...`,
      renderArtifact: () => <FlagDetailView flag={flag} />
    });
    setCollapsed(false);
  };

  return (
    <div className="flex flex-col h-full">
      <div className="mb-4 p-4 bg-gray-50 rounded-lg">
        <div className="flex flex-col gap-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium">Filters</h3>
            <div className="flex items-center gap-2">
              {(filterTypes.length > 0 || filterSeverities.length > 0) && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={clearFilters}
                  className="h-8 gap-1 text-xs"
                >
                  <FilterX className="h-3.5 w-3.5" />
                  Clear All
                </Button>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                className="h-8 gap-1 text-xs"
                disabled={isLoading}
              >
                <RefreshCw className={`h-3.5 w-3.5 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm mb-1">Flag Type</p>
              <MultiSelect
                options={filterOptions}
                onValueChange={setFilterTypes}
                value={filterTypes}
                placeholder="Select flag types..."
                className="w-full"
              />
            </div>
            <div>
              <p className="text-sm mb-1">Severity</p>
              <MultiSelect
                options={severityOptions}
                onValueChange={setFilterSeverities}
                value={filterSeverities}
                placeholder="Select severities..."
                className="w-full"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="flex-1">
        {timelineItems.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64 text-gray-500">
            <Flag className="h-8 w-8 mb-2 opacity-40" />
            <p>No red flags found</p>
            {(filterTypes.length > 0 || filterSeverities.length > 0) && (
              <p className="text-sm mt-1">Try removing some filters</p>
            )}
          </div>
        ) : (
          <VirtualList
            items={timelineItems}
            onItemClick={handleFlagClick}
            itemHeight={70}
            viewportHeight={600}
            className="h-[calc(100vh-300px)]"
          />
        )}
      </div>
    </div>
  );
}; 