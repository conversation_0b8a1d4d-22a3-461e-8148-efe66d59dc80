import { Workspace } from '@/app/layout/Workspace/Workspace';
import { OverviewTab } from './Overview/OverviewTab'
import { RedFlagsTab } from './RedFlags/RedFlagsTab';
import { LinkagesTab } from './Linkages/LinkagesTab';
import { DigitalFootprintTab } from './DigitalFootPrint/DigitalFootprintTab';
import { useActiveContext } from '@/app/layout/ActiveContext/useActiveContext';


export default function InvestigationInsights() {
  
  const tabs = [
    {
      id: 'overview',
      label: 'Overview',
      content: <OverviewTab />
    },
    {
      id: 'flags',
      label: 'Red Flags',
      content: <RedFlagsTab />
    },
    {
      id: 'linkages',
      label: 'Linkages',
      content: <LinkagesTab />
    },
    {
      id: 'digital-information',
      label: 'Digital Information',
      content: <DigitalFootprintTab />
    }
  ];

  console.log('InvestigationInsights - Rendering Workspace with tabs');
  return <Workspace tabs={tabs} />;
} 