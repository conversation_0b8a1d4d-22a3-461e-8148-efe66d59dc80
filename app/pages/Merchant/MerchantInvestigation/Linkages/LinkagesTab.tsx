import { FC, useEffect, useMemo, useState } from 'react';
import { useInvestigationLinkagesStore } from '@/app/store/merchant/investigationLinkagesStore';
import { useMerchantIdStore } from '@/app/store/merchant/merchantIdStore';
import { Network, Share2, Link, Loader2 } from 'lucide-react';
import { motion } from 'framer-motion';
import { NetworkOverviewCard, LinkedEntitiesSection, CommonConnectionsCard } from './components';
import { useActiveContext } from '@/app/layout/ActiveContext/useActiveContext';
import { linkagesData } from '@/app/data/hardcodeddata/sampleLinkagesData';
import SectionHeaderWithRedFlags from '@/components/custom/SectionHeaderWithRedFlags';
import { useInvestigationRedFlagsStore } from '@/app/store/merchant/InvestigationRedFlagsStore';

export const LinkagesTab: FC = () => {
  
  const { networkData, fetchNetworkData } = useInvestigationLinkagesStore();
  const { selectedMerchantId } = useMerchantIdStore();
  const { activeContexts } = useActiveContext();
  const { flagsList, fetchFlagsList } = useInvestigationRedFlagsStore();
  const merchantId = useMemo(() => activeContexts?.merchant, [activeContexts?.merchant]);
  useEffect(() => {
    if (selectedMerchantId) {
      fetchNetworkData(selectedMerchantId);
      fetchFlagsList(selectedMerchantId);
    }
  }, [fetchNetworkData, fetchFlagsList, selectedMerchantId]);

  if (!networkData) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <motion.div
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div variants={itemVariants} className="space-y-2">
        <SectionHeaderWithRedFlags
          redFlags={flagsList}
          title="Connected Entity Network"
          icon={Network}
          iconColorClass="text-blue-500"
          redFlag_recepient_id="linkages_network"
        />
        <NetworkOverviewCard networkOverview={linkagesData.networkOverview} />
      </motion.div>

      <motion.div variants={itemVariants} className="space-y-2">
        <SectionHeaderWithRedFlags
          redFlags={flagsList}
          title="Linked Entities"
          icon={Share2}
          iconColorClass="text-purple-600"
          redFlag_recepient_id="linkages_entities"
        />
        <LinkedEntitiesSection 
          merchantId={selectedMerchantId}
          firstDegreeCommunity={linkagesData.firstDegreeCommunity}
          secondDegreeCommunity={linkagesData.secondDegreeCommunity}
          thirdDegreeCommunity={linkagesData.thirdDegreeCommunity}
        />
      </motion.div>

      <motion.div variants={itemVariants} className="space-y-2">
        <SectionHeaderWithRedFlags
          redFlags={flagsList}
          title="Common Connections"
          icon={Link}
          iconColorClass="text-indigo-600"
          redFlag_recepient_id="linkages_connections"
        />
        <CommonConnectionsCard connections={linkagesData.commonConnections} />
      </motion.div>
    </motion.div>
  );
}