import { FC, useMemo, useState } from 'react';
import { MultiSelect } from '@/components/ui/multi-select2';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { NetworkGraph } from './NetworkGraph';
import { Community, LinkedEntity, Edge } from '@/app/types';
import { motion } from 'framer-motion';
import { Building2, Phone, Mail, MapPin } from 'lucide-react';

interface LinkedEntitiesSectionProps {
  merchantId: string | null;
  firstDegreeCommunity: Community;
  secondDegreeCommunity: Community;
  thirdDegreeCommunity: Community;
}

export const LinkedEntitiesSection: FC<LinkedEntitiesSectionProps> = ({
  merchantId,
  firstDegreeCommunity,
  secondDegreeCommunity,
  thirdDegreeCommunity,
}) => {
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
  const [hoveredNode, setHoveredNode] = useState<LinkedEntity | null>(null);
  const [hoveredEdge, setHoveredEdge] = useState<Edge | null>(null);

  const connectionTypes = [
    { label: 'Same Phone', value: 'SAME_PHONE' },
    { label: 'Same Email', value: 'SAME_EMAIL' },
    { label: 'Same Address', value: 'SAME_ADDRESS' },
  ];

  const filterEntitiesByType = (community: Community) => {
    if (selectedTypes.length === 0) return community;

    // First, find all valid paths from merchant to entities
    const validEdges = new Set<string>();
    const validNodes = new Set([merchantId]);
    let changed = true;

    while (changed) {
      changed = false;
      community.edges.forEach(edge => {
        const edgeKey = `${edge.fromNode}-${edge.toNode}`;
        if (!validEdges.has(edgeKey)) {
          if (
            (validNodes.has(edge.fromNode) || validNodes.has(edge.toNode)) &&
            selectedTypes.includes(edge.relationship_type)
          ) {
            validEdges.add(edgeKey);
            validNodes.add(edge.fromNode);
            validNodes.add(edge.toNode);
            changed = true;
          }
        }
      });
    }

    return {
      ...community,
      entities: community.entities.filter(entity => 
        validNodes.has(entity.related_entity_id)
      ),
      edges: community.edges.filter(edge => 
        validEdges.has(`${edge.fromNode}-${edge.toNode}`)
      )
    };
  };

  const filteredOneDegreeEntities = useMemo(() => {
    return filterEntitiesByType(firstDegreeCommunity);
  }, [firstDegreeCommunity, selectedTypes]);

  const filteredTwoDegreeEntities = useMemo(() => {
    return filterEntitiesByType(secondDegreeCommunity);
  }, [secondDegreeCommunity, selectedTypes]);

  const filteredThreeDegreeEntities = useMemo(() => {
    return filterEntitiesByType(thirdDegreeCommunity);
  }, [thirdDegreeCommunity, selectedTypes]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-4 relative"
    >
      <div className="flex flex-col space-y-4">
        <MultiSelect
          options={connectionTypes}
          onValueChange={setSelectedTypes}
          placeholder="Filter by connection type..."
          className="w-full"
        />

        <Tabs defaultValue="first" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="first">First Degree</TabsTrigger>
            <TabsTrigger value="second">Second Degree</TabsTrigger>
            <TabsTrigger value="third">Third Degree</TabsTrigger>
          </TabsList>

          <TabsContent value="first">
            <NetworkGraph
              merchantId={merchantId}
              entities={filteredOneDegreeEntities.entities}
              edges={filteredOneDegreeEntities.edges}
              onNodeHover={setHoveredNode}
              onEdgeHover={setHoveredEdge}
            />
          </TabsContent>
          <TabsContent value="second">
            <NetworkGraph
              merchantId={merchantId}
              entities={filteredTwoDegreeEntities.entities}
              edges={filteredTwoDegreeEntities.edges}
              onNodeHover={setHoveredNode}
              onEdgeHover={setHoveredEdge}
            />
          </TabsContent>
          <TabsContent value="third">
            <NetworkGraph
              merchantId={merchantId}
              entities={filteredThreeDegreeEntities.entities}
              edges={filteredThreeDegreeEntities.edges}
              onNodeHover={setHoveredNode}
              onEdgeHover={setHoveredEdge}
            />
          </TabsContent>
        </Tabs>
      </div>

      {(hoveredNode || hoveredEdge) && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="absolute bottom-4 right-4 p-4 bg-background/80 backdrop-blur-sm rounded-lg shadow-lg max-w-sm"
        >
          {hoveredNode && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Building2 className="h-4 w-4 text-blue-500" />
                <span className="font-medium">{hoveredNode.related_entity_name}</span>
              </div>
              <div className="text-sm text-gray-500">
                <p>ID: {hoveredNode.related_entity_id}</p>
                <p>Type: {hoveredNode.relationship_type}</p>
                <p>Created: {new Date(hoveredNode.created_at).toLocaleDateString()}</p>
              </div>
            </div>
          )}
          {hoveredEdge && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                {hoveredEdge.relationship_type === 'SAME_PHONE' && <Phone className="h-4 w-4 text-blue-500" />}
                {hoveredEdge.relationship_type === 'SAME_EMAIL' && <Mail className="h-4 w-4 text-green-500" />}
                {hoveredEdge.relationship_type === 'SAME_ADDRESS' && <MapPin className="h-4 w-4 text-purple-500" />}
                <span className="font-medium">Connection Details</span>
              </div>
              <p className="text-sm text-gray-500">Type: {hoveredEdge.relationship_type}</p>
              <p className="text-sm text-gray-500">Connected from: {hoveredEdge.fromNode}</p>
              <p className="text-sm text-gray-500">Connected to: {hoveredEdge.toNode}</p>
            </div>
          )}
        </motion.div>
      )}
    </motion.div>
  );
}; 