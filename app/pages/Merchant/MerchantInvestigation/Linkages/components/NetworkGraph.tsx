import { FC, useEffect, useRef, memo } from 'react';
import { Network } from 'vis-network';
import { DataSet } from 'vis-data';
import { motion } from 'framer-motion';
import { LinkedEntity, Edge } from '@/app/types';
import { Community } from '@/app/types';

interface NetworkGraphProps {
  merchantId: string | null;
  entities: LinkedEntity[];
  edges: Edge[];
  onNodeHover?: (node: LinkedEntity | null) => void;
  onEdgeHover?: (edge: Edge | null) => void;
}

export const NetworkGraph: FC<NetworkGraphProps> = memo(({
  merchantId,
  entities,
  edges,
  onNodeHover,
  onEdgeHover,
}) => {
  const networkRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!networkRef.current) return;

    const getPathLengthToMerchant = (nodeId: string): number => {
      if (nodeId === merchantId) return 0;
      
      let visited = new Set<string>([nodeId]);
      let currentLevel = new Set<string>([nodeId]);
      let distance = 1;

      while (currentLevel.size > 0) {
        const nextLevel = new Set<string>();
        
        for (const current of currentLevel) {
          for (const edge of edges) {
            let nextNode = null;
            if (edge.fromNode === current) nextNode = edge.toNode;
            if (edge.toNode === current) nextNode = edge.fromNode;
            
            if (nextNode === merchantId) return distance;
            
            if (nextNode && !visited.has(nextNode)) {
              visited.add(nextNode);
              nextLevel.add(nextNode);
            }
          }
        }
        
        currentLevel = nextLevel;
        distance++;
      }
      
      return Infinity;
    };

    const getNodeColor = (entity: LinkedEntity) => {
      const pathLength = getPathLengthToMerchant(entity.related_entity_id);
      
      switch(pathLength) {
        case 1: return { background: '#93c5fd', border: '#2563eb' }; // First degree: blue
        case 2: return { background: '#86efac', border: '#16a34a' }; // Second degree: green
        case 3: return { background: '#fda4af', border: '#e11d48' }; // Third degree: red
        default: return { background: '#f8fafc', border: '#e2e8f0' };
      }
    };

    const nodes = new DataSet([
      { 
        id: merchantId || 'merchant', 
        label: 'Merchant', 
        shape: 'star',
        size: 25,
        color: { background: '#fef3c7', border: '#d97706' }
      },
      ...entities.map(entity => ({
        id: entity.related_entity_id,
        label: entity.related_entity_name,
        shape: 'dot',
        size: 15,
        color: getNodeColor(entity)
      }))
    ]);

    const getEdgeColor = (type: string) => {
      switch(type) {
        case 'SAME_PHONE': return '#2563eb';  // blue
        case 'SAME_EMAIL': return '#16a34a';  // green
        case 'SAME_ADDRESS': return '#e11d48'; // red
        default: return '#64748b';
      }
    };

    const graphEdges = new DataSet(
      edges.map((edge, index) => ({
        id: `edge-${index}`,
        from: edge.fromNode,
        to: edge.toNode,
        label: edge.relationship_type,
        color: {
          color: getEdgeColor(edge.relationship_type),
          highlight: getEdgeColor(edge.relationship_type),
          hover: getEdgeColor(edge.relationship_type),
          opacity: 0.8
        },
        width: 1.5
      }))
    );

    const options = {
      nodes: {
        font: {
          size: 12,
          color: '#475569',
          face: 'Inter'
        },
        borderWidth: 1.5,
        shadow: false
      },
      edges: {
        font: {
          size: 10,
          color: '#64748b',
          face: 'Inter',
          background: '#ffffff'
        },
        smooth: {
          enabled: true,
          type: 'continuous',
          roundness: 0.5
        }
      },
      physics: {
        enabled: true,
        stabilization: {
          enabled: true,
          iterations: 100,
          fit: true
        },
        hierarchicalRepulsion: {
          centralGravity: 0.05,
          springLength: 100,
          springConstant: 0.01,
          nodeDistance: 150,
          damping: 0.09
        },
        solver: 'hierarchicalRepulsion'
      },
      interaction: {
        dragNodes: false,
        dragView: false,
        zoomView: false,
        hover: true,
        hoverConnectedEdges: true,
        selectConnectedEdges: false,
      },
      layout: {
        improvedLayout: true,
        hierarchical: {
          enabled: true,
          levelSeparation: 100,
          nodeSpacing: 150,
          direction: 'UD',
          sortMethod: 'directed'
        }
      }
    };

    const network = new Network(networkRef.current, { nodes, edges: graphEdges }, options);

    network.on('hoverNode', params => {
      if (params.node === merchantId) return;
      const entity = entities.find(e => e.related_entity_id === params.node);
      if (entity) {
        onNodeHover?.(entity);
      }
    console.log("hovering node")
    });

    network.on('hoverEdge', params => {
      const edgeData = graphEdges.get(params.edge) as unknown as { from: string; to: string };
      if (edgeData) {
        const edge = edges.find(e => 
          (e.fromNode === edgeData.from && e.toNode === edgeData.to) ||
          (e.fromNode === edgeData.to && e.toNode === edgeData.from)
        );
        if (edge) {
          onEdgeHover?.(edge);
          onNodeHover?.(null);
        }
      }
    });

    return () => {
      network.destroy();
    };
  }, [entities, edges, merchantId, onNodeHover, onEdgeHover]);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      ref={networkRef}
      className="h-[400px] w-full bg-background rounded-lg border border-gray-200 shadow-sm"
    />
  );
}); 