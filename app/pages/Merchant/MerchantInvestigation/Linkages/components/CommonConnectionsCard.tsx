import { FC } from 'react';
import { CustomCard } from '@/components/custom/CustomCard';
import { Phone, Mail, Link } from 'lucide-react';
import { CommonConnection } from '@/app/types';
import { motion } from 'framer-motion';

const CONNECTION_TYPE_ICONS: { [key: string]: JSX.Element } = {
  'phone': <Phone className="h-4 w-4" />,
  'email': <Mail className="h-4 w-4" />,
  'website': <Link className="h-4 w-4" />,
  'default': <Link className="h-4 w-4" />
};

interface CommonConnectionsCardProps {
  connections: CommonConnection[];
}

export const CommonConnectionsCard: FC<CommonConnectionsCardProps> = ({ connections }) => {
  return (
    <CustomCard className="divide-y">
      {connections.map((connection, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
          className="p-4"
        >
          <div className="flex items-center gap-2 mb-2">
            <div className="p-1.5 bg-gray-50 rounded">
              {CONNECTION_TYPE_ICONS[connection.connection_type] || CONNECTION_TYPE_ICONS.default}
            </div>
            <div>
              <p className="text-sm font-medium">
                {connection.connection_type.charAt(0).toUpperCase() + connection.connection_type.slice(1)}
              </p>
              <p className="text-sm text-gray-600">{connection.connection_value}</p>
            </div>
          </div>
          <div className="flex flex-wrap gap-2">
            {connection.shared_with.map((entity, idx) => (
              <span key={idx} className="text-xs bg-gray-100 px-2 py-1 rounded">
                {entity}
              </span>
            ))}
          </div>
        </motion.div>
      ))}
    </CustomCard>
  );
}; 