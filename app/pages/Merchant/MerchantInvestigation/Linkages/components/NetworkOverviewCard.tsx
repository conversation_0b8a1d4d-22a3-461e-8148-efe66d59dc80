import { FC } from 'react';
import { CustomCard } from '@/components/custom/CustomCard';
import { Network } from 'lucide-react';
import { NetworkOverview } from '@/app/types';

interface NetworkOverviewCardProps {
  networkOverview: NetworkOverview;
}

export const NetworkOverviewCard: FC<NetworkOverviewCardProps> = ({ networkOverview }) => {
  return (
    <CustomCard className="p-4">
      {/* <div className="flex items-center gap-2 mb-3">
        <Network className="h-5 w-5 text-blue-500" />
        <span className="font-medium">Connected Entity Network</span>
      </div> */}
      <div className="grid grid-cols-3 gap-4 text-center">
        <div>
          <p className="text-2xl font-semibold text-blue-600">{networkOverview?.total_connections}</p>
          <p className="text-sm text-gray-500">Connected Entities</p>
        </div>
        <div>
          <p className="text-2xl font-semibold text-yellow-600">{networkOverview?.high_risk_connections}</p>
          <p className="text-sm text-gray-500">High Risk Connections</p>
        </div>
        <div>
          <p className="text-2xl font-semibold text-purple-600">{networkOverview?.network_risk_score}%</p>
          <p className="text-sm text-gray-500">Network Risk Score</p>
        </div>
      </div>
    </CustomCard>
  );
}; 