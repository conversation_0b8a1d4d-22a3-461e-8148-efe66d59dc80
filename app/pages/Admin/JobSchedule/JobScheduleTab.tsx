import { FC } from 'react';
import { motion } from 'framer-motion';
import { useJobSchedulingStore } from '@/app/store/admin/jobSchedulingStore';
import { TabSectionHeading } from '@/components/custom/TabSectionHeading';
import { Loader2, RefreshCw, Activity, Shield, Link, FileText, ShieldAlert } from 'lucide-react';

export const JobScheduleTab: FC = () => {
    const { 
        updateDigitalFootprint, 
        updateKeyMetrics, 
        updateRiskAssessment,
        updateLinkages,
        updateSummary,
        updateRedFlags,
        runAllJobs,
        isLoading,
        responses
    } = useJobSchedulingStore();

    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1
            }
        }
    };

    const itemVariants = {
        hidden: { opacity: 0, y: 20 },
        visible: { opacity: 1, y: 0 }
    };

    const JobCard: FC<{
        title: string;
        description: string;
        icon: any;
        isLoading: boolean;
        onUpdate: () => void;
        response: any;
        iconColor: string;
    }> = ({ title, description, icon: Icon, isLoading, onUpdate, response, iconColor }) => (
        <motion.div 
            variants={itemVariants}
            className="p-6 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow"
        >
            <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                    <Icon className={`h-6 w-6 ${iconColor}`} />
                    <div>
                        <h3 className="text-lg font-semibold">{title}</h3>
                        <p className="text-sm text-gray-600">{description}</p>
                    </div>
                </div>
                <button
                    onClick={onUpdate}
                    disabled={isLoading}
                    className="inline-flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium bg-blue-500 text-white hover:bg-blue-600 disabled:opacity-50 transition-colors"
                >
                    {isLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                        'Update'
                    )}
                </button>
            </div>
            {response && (
                <div className="mt-4 p-3 bg-gray-50 rounded-md">
                    <pre className="text-sm overflow-auto">
                        {JSON.stringify(response, null, 2)}
                    </pre>
                </div>
            )}
        </motion.div>
    );

    return (
        <motion.div
            className="space-y-6 p-6"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
        >
            <div className="flex justify-between items-center mb-6">
                <TabSectionHeading icon={RefreshCw} iconColorClass="text-blue-500">
                    Job Scheduling
                </TabSectionHeading>
                <button
                    onClick={runAllJobs}
                    disabled={Object.values(isLoading).some(Boolean)}
                    className="inline-flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium bg-blue-500 text-white hover:bg-blue-600 disabled:opacity-50 transition-colors"
                >
                    {Object.values(isLoading).some(Boolean) ? (
                        <>
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            Running All Jobs...
                        </>
                    ) : (
                        <>
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Run All Jobs
                        </>
                    )}
                </button>
            </div>

            <div className="grid gap-6">
                <JobCard
                    title="Digital Footprint Update"
                    description="Update merchant digital footprint information"
                    icon={Activity}
                    isLoading={isLoading.digitalFootprint}
                    onUpdate={updateDigitalFootprint}
                    response={responses.digitalFootprint}
                    iconColor="text-blue-500"
                />

                <JobCard
                    title="Key Metrics Update"
                    description="Update merchant key metrics and analytics"
                    icon={RefreshCw}
                    isLoading={isLoading.keyMetrics}
                    onUpdate={updateKeyMetrics}
                    response={responses.keyMetrics}
                    iconColor="text-green-500"
                />

                <JobCard
                    title="Risk Assessment Update"
                    description="Update merchant risk assessment scores"
                    icon={Shield}
                    isLoading={isLoading.riskAssessment}
                    onUpdate={updateRiskAssessment}
                    response={responses.riskAssessment}
                    iconColor="text-red-500"
                />

                <JobCard
                    title="Linkages Update"
                    description="Update merchant linkages"
                    icon={Link}
                    isLoading={isLoading.linkages}
                    onUpdate={updateLinkages}
                    response={responses.linkages}
                    iconColor="text-purple-500"
                />

                <JobCard
                    title="Summary Update"
                    description="Update merchant summary"
                    icon={FileText}
                    isLoading={isLoading.summary}
                    onUpdate={updateSummary}
                    response={responses.summary}
                    iconColor="text-orange-500"
                />

                <JobCard
                    title="Red Flags Update"
                    description="Update merchant red flags"
                    icon={ShieldAlert}
                    isLoading={isLoading.redFlags}
                    onUpdate={updateRedFlags}
                    response={responses.redFlags}
                    iconColor="text-red-500"
                />
            </div>
        </motion.div>
    );
};