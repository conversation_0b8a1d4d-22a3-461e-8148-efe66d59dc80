import { ContextMenuContent, ContextMenuItem, ContextMenuTrigger } from "@/components/ui/context-menu";

import { useReportStore } from "@/app/store/report/reportStore";
import { ContextMenu } from "@/components/ui/context-menu";
import { v4 as uuidv4 } from 'uuid';
import { useEffect, useState, useRef, useCallback } from 'react';
import { ContextMenuSub, ContextMenuSubTrigger, ContextMenuSubContent, ContextMenuSeparator } from "@/components/ui/context-menu";
import type { ReportComponent } from "./report";
import { ReportArtifact } from './ReportArtifact';
import { useArtifactStore } from "@/app/store/artifact/artifactStore";
import { useInvestigatorDetailsStore } from "@/app/store/login/InvestigatorDetailsStore";
import { CreateReportDialog } from './CreateReportDialog';

export const ReportableSection = ({ type, children, data, title }: {
    type: ReportComponent['component_type'];
    children: React.ReactNode;
    data: any;
    title?: string;
  }) => {
    const { reports, createNewReport, addComponentToReport, fetchReportDetails } = useReportStore();
    const { addTab, setActiveTabId, setCollapsed } = useArtifactStore();
    const { investigatorEmail } = useInvestigatorDetailsStore();
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [newReportTitle, setNewReportTitle] = useState('');
    const sectionRef = useRef<HTMLDivElement>(null);

    // Memoize the handleAddToReport function to prevent recreation on each render
    const handleAddToReport = useCallback(async (reportId: string) => {
      console.log(`Adding to report: ${reportId}`);

      try {
      // Only fetch report details if components are not present
      const existingReport = reports.find(r => r.id === reportId);
      const reportDetails = existingReport?.components ? 
        existingReport : 
        await fetchReportDetails(reportId);
      
      const updatedReport = await addComponentToReport(reportId, {
        frontend_component_id: uuidv4(),
        component_type: type,
        data: { ...data, title },
      });

      if (updatedReport) {
        addTab({
          id: `report-${reportId}`,
          title: `Report: ${updatedReport.report_title}`,
          renderArtifact: () => <ReportArtifact report={updatedReport} />
        });
        setActiveTabId(`report-${reportId}`);
        setCollapsed(false);
      }
      } catch (error) {
        console.error("Error adding component to report:", error);
      }
    }, [reports, fetchReportDetails, addComponentToReport, type, data, title, addTab, setActiveTabId, setCollapsed]);

    const handleNewReport = async () => {
      try {
      const newReport = await createNewReport(newReportTitle, investigatorEmail);
      // Get the newly created report
      if (newReport) {
        // First create the artifact tab for the new report
        addTab({
          id: `report-${newReport.id}`,
          title: `Report: ${newReport.report_title}`,
          renderArtifact: () => <ReportArtifact report={newReport} />
        });
        setActiveTabId(`report-${newReport.id}`);
        setCollapsed(false);  
        
        // Then add the component to the report
        handleAddToReport(newReport.id);
      }
      } catch (error) {
        console.error("Error creating new report:", error);
      } finally {
      setIsDialogOpen(false);
      }
    };

    // Handle the generate-report event
    useEffect(() => {
      const currentSectionRef = sectionRef.current;
      
      const handleGenerateReport = (event: CustomEvent) => {
        const { type: eventType, data: eventData } = event.detail;
        
        if (eventType === type) {
          // Open the dialog to create a new report
          setIsDialogOpen(true);
          // Optionally pre-populate the report title based on the section
          setNewReportTitle(`${title || type.charAt(0).toUpperCase() + type.slice(1).replace(/-/g, ' ')} Report`);
        }
      };
      
      // Handle add-to-report event (direct addition to existing report)
      const handleAddToReportEvent = (event: CustomEvent) => {
        event.stopPropagation();
        const { type: eventType, data: eventData, reportId } = event.detail;
        
        console.log(`add-to-report event received for ${eventType} to report ${reportId}`);
        
        if (eventType === type && reportId) {
          // Directly add to the specified report
          handleAddToReport(reportId);
        }
      };
      
      if (currentSectionRef) {
        // Remove any existing event listeners to prevent duplicates
        currentSectionRef.removeEventListener('generate-report', handleGenerateReport as EventListener);
        currentSectionRef.removeEventListener('add-to-report', handleAddToReportEvent as EventListener);
        
        // Add fresh event listeners
        currentSectionRef.addEventListener('generate-report', handleGenerateReport as EventListener);
        currentSectionRef.addEventListener('add-to-report', handleAddToReportEvent as EventListener);
        
        // Add data attributes for DOM querying
        currentSectionRef.setAttribute('data-report-section', type);
        currentSectionRef.classList.add('reportable-section');
        currentSectionRef.setAttribute('data-type', type);
      }
      
      return () => {
        if (currentSectionRef) {
          currentSectionRef.removeEventListener('generate-report', handleGenerateReport as EventListener);
          currentSectionRef.removeEventListener('add-to-report', handleAddToReportEvent as EventListener);
        }
      };
    }, [type, title, handleAddToReport]);

    return (
      <div ref={sectionRef} className="reportable-section" data-report-section={type} data-type={type}>
        <ContextMenu>
          <ContextMenuTrigger>{children}</ContextMenuTrigger>
          <ContextMenuContent>
            <ContextMenuSub>
              <ContextMenuSubTrigger>Add to Report</ContextMenuSubTrigger>
              <ContextMenuSubContent>
                {reports.map(report => (
                  <ContextMenuItem key={report.id} onClick={() => handleAddToReport(report.id)}>
                    {report.report_title}
                  </ContextMenuItem>
                ))}
                {reports.length > 0 && <ContextMenuSeparator />}
                <ContextMenuItem onClick={() => setIsDialogOpen(true)}>
                  Create New Report...
                </ContextMenuItem>
              </ContextMenuSubContent>
            </ContextMenuSub>
          </ContextMenuContent>
        </ContextMenu>

        <CreateReportDialog
          isOpen={isDialogOpen}
          onClose={() => setIsDialogOpen(false)}
          onCreateReport={handleNewReport}
          reportTitle={newReportTitle}
          onReportTitleChange={(value) => setNewReportTitle(value)}
        />
      </div>
    );
  };

export const RemoveFromReport = ({ id, reportId, children }: { id: string, reportId: string, children: React.ReactNode }) => {
  const { removeComponentFromReport } = useReportStore();
  const { addTab, setActiveTabId, setCollapsed, updateTab } = useArtifactStore();
  
  const handleRemoveFromReport = async () => {
    const updatedReport = await removeComponentFromReport(reportId, id);
    
    if (updatedReport) {
      // Update the existing tab instead of creating a new one
      updateTab(`report-${reportId}`, {
        title: `Report: ${updatedReport.report_title}`,
        renderArtifact: () => <ReportArtifact report={updatedReport} />
      });
      
      setActiveTabId(`report-${reportId}`);
      setCollapsed(false);
    }
  };

  return (
    <ContextMenu>
      <ContextMenuTrigger>{children}</ContextMenuTrigger>
      <ContextMenuContent>
        <ContextMenuItem onClick={handleRemoveFromReport}>Remove</ContextMenuItem>
      </ContextMenuContent>
    </ContextMenu>
  );
};