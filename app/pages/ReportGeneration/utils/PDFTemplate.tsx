import { Document, Page, Text, View, StyleSheet, Font, Image } from '@react-pdf/renderer';
import { Report, ReportComponent } from './report';
import { KeyMetricListType, RiskAssessment } from '@/app/types';

Font.register({
  family: 'Roboto',
  fonts: [
    {
      src: 'https://fonts.gstatic.com/s/roboto/v20/KFOmCnqEu92Fr1Mu4mxP.ttf',
      fontWeight: 'normal',
    },
    {
      src: 'https://fonts.gstatic.com/s/roboto/v20/KFOlCnqEu92Fr1MmWUlfBBc9.ttf',
      fontWeight: 'bold',
    },
  ],
});

const colors = {
  primary: '#2563EB',
  lightBlue: '#DBEAFE',
  border: '#93C5FD',
  background: '#F9FAFB',
  danger: '#FECACA',
  dangerText: '#B91C1C',
};

const styles = StyleSheet.create({
  page: {
    padding: 40,
    fontFamily: 'Roboto',
    fontSize: 10,
    backgroundColor: colors.background,
    color: '#111827',
  },
  header: {
    textAlign: 'center',
    marginBottom: 20,
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
    paddingBottom: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: 5,
    textTransform: 'uppercase',
  },
  subtitle: {
    fontSize: 10,
    marginBottom: 3,
    color: '#4B5563',
  },
  section: {
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 10,
    color: colors.primary,
    textTransform: 'uppercase',
    borderBottom: 1,
    borderBottomColor: colors.border,
    paddingBottom: 4,
  },
  table: {
    display: 'flex',
    flexDirection: 'column',
    width: 'auto',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 4,
    overflow: 'hidden',
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  tableHeader: {
    backgroundColor: colors.primary,
    color: '#FFFFFF',
    padding: 8,
    flex: 1,
    fontWeight: 'bold',
  },
  tableCell: {
    padding: 8,
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  tableCellAlt: {
    backgroundColor: colors.lightBlue,
  },
  referenceInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  referenceItem: {
    fontSize: 10,
    color: '#374151',
  },
  infoBox: {
    backgroundColor: colors.danger,
    padding: 6,
    borderRadius: 4,
    marginTop: 6,
  },
  infoText: {
    fontSize: 9,
    color: colors.dangerText,
  },
});

export const PDFDocument = ({ report, components }: { report: Report; components: ReportComponent[] }) => {
  // Sort components by their natural order for reporting
  const sortedComponents = [...components].sort((a, b) => {
    // Define the order of component types for the report
    const orderMap: Record<string, number> = {
      'risk-score': 1,
      'key-stats': 2,
      // Other components follow in natural order
    };
    
    const orderA = orderMap[a.component_type] || 99;
    const orderB = orderMap[b.component_type] || 99;
    
    return orderA - orderB;
  });

  let sectionIndex = 1;

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Modus Fraud Detection Report</Text>
          <Text style={styles.subtitle}>Merchant Risk Assessment and Investigation Report</Text>
          <Text style={styles.subtitle}>Fraud Detection and Prevention Division</Text>
          <Text style={styles.subtitle}>Generated via Modus Intelligence Platform</Text>
        </View>

        {/* Reference Info */}
        <View style={styles.referenceInfo}>
          <View>
            <Text style={styles.referenceItem}>Reference #: {report.id}</Text>
            <Text style={styles.referenceItem}>Jurisdiction: Cyber Crime Cell</Text>
          </View>
          <View>
            <Text style={styles.referenceItem}>Date: {new Date(report.last_updated).toLocaleDateString()}</Text>
            <Text style={styles.referenceItem}>Time: {new Date(report.last_updated).toLocaleTimeString()}</Text>
          </View>
        </View>

        {/* All Components */}
        {sortedComponents.map((component, idx) => {
          const { component_type, data } = component;
          // Ensure we have a valid string for section title
          const componentTitle = data?.title || component_type.replace(/-/g, ' ').replace(/\b\w/g, c => c.toUpperCase());
          const sectionTitle = `${sectionIndex++}. ${componentTitle}`;

          // Risk Score
          if (component_type === 'risk-score') {
            return (
              <View key={idx} style={styles.section}>
                <Text style={styles.sectionTitle}>{sectionTitle}</Text>
                {data?.riskAssessment && (
                  <View style={styles.table}>
                    <View style={styles.tableRow}>
                      <Text style={styles.tableHeader}>Overall Risk Score</Text>
                      <Text style={styles.tableCell}>{data.riskAssessment.overall?.percentile || 'N/A'}</Text>
                    </View>
                    <View style={styles.tableRow}>
                      <Text style={styles.tableHeader}>Risk Level</Text>
                      <Text style={styles.tableCell}>{data.riskAssessment.risk_level || 'N/A'}</Text>
                    </View>
                  </View>
                )}
              </View>
            );
          }

          // Key Stats
          if (component_type === 'key-stats') {
            // Get metrics from either keyMetricList or companyMetrics
            const metrics = data?.keyMetricList?.key_metrics || data?.companyMetrics || [];
            const hasMetrics = metrics && metrics.length > 0;
            
            return (
              <View key={idx} style={styles.section}>
                <Text style={styles.sectionTitle}>{sectionTitle}</Text>
                {!hasMetrics && (
                  <Text style={styles.referenceItem}>No company metrics available</Text>
                )}
                {hasMetrics && (
                  <View style={[styles.table, { marginBottom: 10 }]}>
                    {metrics.map((metric: any, midx: number) => {
                      // Skip if missing required properties
                      if (!metric || (typeof metric !== 'object')) return null;
                      
                      // Get the label and value
                      const label = metric.label;
                      const value = metric.value;
                      
                      // Skip if missing label or value
                      if (!label || value === undefined || value === null) return null;
                      
                      return (
                        <View key={midx} style={styles.tableRow}>
                          <Text style={[styles.tableCell, styles.tableCellAlt, { fontWeight: 'bold' }]}>{label}</Text>
                          <Text style={styles.tableCell}>{String(value)}</Text>
                        </View>
                      );
                    })}
                  </View>
                )}
              </View>
            );
          }

          // Chart Image
          if (data?.chartImage) {
            return (
              <View key={idx} style={styles.section}>
                <Text style={styles.sectionTitle}>{sectionTitle}</Text>
                <Image src={data.chartImage} style={{ width: 500, height: 200, marginBottom: 10 }} />
              </View>
            );
          }

          // Tables for known types
          if (component_type === 'single-investigation') {
            const inv = data?.investigation;
            return inv ? (
              <View key={idx} style={styles.section}>
                <Text style={styles.sectionTitle}>{sectionTitle}</Text>
                <View style={styles.table}>
                  {[
                    ['Case Number', inv.case_number],
                    ['Title', inv.title],
                    ['Priority', inv.priority],
                    ['Status', inv.status],
                    ['Assigned To', inv.assignee_Name],
                  ].map(([label, value], i) => (
                    <View key={i} style={styles.tableRow}>
                      <Text style={[styles.tableCell, styles.tableCellAlt, { fontWeight: 'bold' }]}>{label}</Text>
                      <Text style={styles.tableCell}>{value || 'N/A'}</Text>
                    </View>
                  ))}
                </View>
              </View>
            ) : null;
          }

          if (component_type === 'transaction') {
            return (
              <View key={idx} style={styles.section}>
                <Text style={styles.sectionTitle}>{sectionTitle}</Text>
                <View style={styles.table}>
                  {[
                    ['Transaction ID', data?.transaction_id],
                    ['Amount', `₹${data?.amount || '0'}`],
                    ['Status', data?.status],
                    ['Channel', data?.payment_channel],
                  ].map(([label, value], i) => (
                    <View key={i} style={styles.tableRow}>
                      <Text style={[styles.tableCell, styles.tableCellAlt, { fontWeight: 'bold' }]}>{label}</Text>
                      <Text style={styles.tableCell}>{value || 'N/A'}</Text>
                    </View>
                  ))}
                </View>
              </View>
            );
          }

          if (component_type === 'payout') {
            return (
              <View key={idx} style={styles.section}>
                <Text style={styles.sectionTitle}>{sectionTitle}</Text>
                <View style={styles.table}>
                  {[
                    ['Amount', `₹${data?.amount || '0'}`],
                    ['Status', data?.status],
                    ['Bank Account', data?.bank_account],
                  ].map(([label, value], i) => (
                    <View key={i} style={styles.tableRow}>
                      <Text style={[styles.tableCell, styles.tableCellAlt, { fontWeight: 'bold' }]}>{label}</Text>
                      <Text style={styles.tableCell}>{value || 'N/A'}</Text>
                    </View>
                  ))}
                </View>
              </View>
            );
          }

          if (component_type === 'default-probability') {
            return (
              <View key={idx} style={styles.section}>
                <Text style={styles.sectionTitle}>{sectionTitle}</Text>
                <Text style={styles.referenceItem}>Analysis for merchant ID: {data?.merchantId || 'N/A'}</Text>
              </View>
            );
          }

          if (component_type === 'company-overview') {
            return (
              <View key={idx} style={styles.section}>
                <Text style={styles.sectionTitle}>{sectionTitle}</Text>
                <Text style={styles.referenceItem}>{data?.companyData?.about_the_company || 'N/A'}</Text>
              </View>
            );
          }

          if (component_type === 'industry-overview') {
            return (
              <View key={idx} style={styles.section}>
                <Text style={styles.sectionTitle}>{sectionTitle}</Text>
                <Text style={styles.referenceItem}>{data?.industryData?.about_the_industry || 'N/A'}</Text>
                {data?.industryData?.is_industry_risky === 'yes' && (
                  <View style={styles.infoBox}>
                    <Text style={styles.infoText}>
                      Industry Risk: {data?.industryData?.justification || 'Risk factors detected'}
                    </Text>
                  </View>
                )}
              </View>
            );
          }

          if (component_type === 'transaction-metrics') {
            const metrics = data?.transactionMetrics || [];
            const hasMetrics = metrics && metrics.length > 0;
            
            return (
              <View key={idx} style={styles.section}>
                <Text style={styles.sectionTitle}>{sectionTitle}</Text>
                {!hasMetrics && (
                  <Text style={styles.referenceItem}>No transaction metrics available</Text>
                )}
                {hasMetrics && (
                  <View style={[styles.table, { marginBottom: 10 }]}>
                    {metrics.map((metric: any, i: number) => {
                      // Skip if missing required properties
                      if (!metric || (typeof metric !== 'object')) return null;
                      
                      // Get the label and value
                      const label = metric.label;
                      const value = metric.value;
                      
                      // Skip if missing label or value
                      if (!label || value === undefined || value === null) return null;
                      
                      return (
                        <View key={i} style={styles.tableRow}>
                          <Text style={[styles.tableCell, styles.tableCellAlt, { fontWeight: 'bold' }]}>{label}</Text>
                          <Text style={styles.tableCell}>{String(value)}</Text>
                        </View>
                      );
                    })}
                  </View>
                )}
              </View>
            );
          }

          if (component_type === 'transactions-visualizations') {
            return (
              <View key={idx} style={styles.section}>
                <Text style={styles.sectionTitle}>{sectionTitle}</Text>
                <Text style={styles.referenceItem}>Transaction visualization data not available</Text>
              </View>
            );
          }

          // Default fallback
          return (
            <View key={idx} style={styles.section}>
              <Text style={styles.sectionTitle}>{sectionTitle}</Text>
              <Text style={{ fontSize: 10 }}>Component data not available for rendering</Text>
            </View>
          );
        })}
      </Page>
    </Document>
  );
}; 