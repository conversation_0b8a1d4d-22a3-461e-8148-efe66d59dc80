import { useReportStore } from '@/app/store/report/reportStore';
import { Button } from '@/components/ui/button';
import { RemoveFromReport } from './ReportSectionHelpers';
import { RiskAssessmentSection } from '@/app/pages/Merchant/MerchantInvestigation/Overview/components';
import { KeyMetrics } from '@/components/custom/KeyMetrics';
import { useEffect, useState } from 'react';
import type { ReportArtifactProps, ReportComponent } from './report';
import { AlertTriangle, Printer, CreditCard, Banknote, FileText } from 'lucide-react';
import { BubbleTag } from '@/components/custom/BubbleTag';
import { formatTimestamp } from '@/utils/timeFormat';
import './reportPrint.css';
import { PDFDownloadLink } from '@react-pdf/renderer';
import { PDFDocument } from './PDFTemplate';

export function ReportArtifact({ report }: ReportArtifactProps) {
  const components = report.components || [];
  const { updateReportComponents } = useReportStore();
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});

  // Helper functions to get and set expansion state for a specific section
  const getMetricsExpanded = (sectionId: string) => {
    return expandedSections[sectionId] || false;
  };
  
  const setMetricsExpanded = (sectionId: string, isExpanded: boolean) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionId]: isExpanded
    }));
  };

  // Add debug logging for component data
  useEffect(() => {
    console.log("Report components for rendering:", components);
    components.forEach(component => {
      console.log(`Component type: ${component.component_type}`, {
        dataKeys: Object.keys(component.data || {}),
        dataPreview: JSON.stringify(component.data).substring(0, 200) + '...'
      });
    });
  }, [components]);

  const renderComponentContent = (component: ReportComponent) => {
    switch (component.component_type) {
      case 'risk-score':
        return <RiskAssessmentSection riskAssessment={component.data.riskAssessment} keyMetricList={component.data.keyMetricList} />;
      case 'key-stats':
        console.log('Rendering key-stats component:', {
          data: component.data,
          hasKeyMetricList: !!component.data.keyMetricList,
          hasKeyMetricsArray: !!component.data.keyMetricList?.key_metrics,
          keyMetricsLength: component.data.keyMetricList?.key_metrics?.length,
          hasCompanyMetrics: !!component.data.companyMetrics,
          companyMetricsLength: component.data.companyMetrics?.length
        });
        return <div className="p-4">
          <h3 className="text-md font-medium mb-2">Company Metrics</h3>
          {component.data.keyMetricList?.key_metrics ? (
            <KeyMetrics
              keyMetricList={component.data.keyMetricList}
              isMetricsExpanded={getMetricsExpanded(component.frontend_component_id)}
              setIsMetricsExpanded={(isExpanded) => setMetricsExpanded(component.frontend_component_id, isExpanded)}
              showHeader={false}
            />
          ) : component.data.companyMetrics ? (
            <KeyMetrics
              keyMetricList={{ key_metrics: component.data.companyMetrics }}
              isMetricsExpanded={getMetricsExpanded(component.frontend_component_id)}
              setIsMetricsExpanded={(isExpanded) => setMetricsExpanded(component.frontend_component_id, isExpanded)}
              showHeader={false}
            />
          ) : (
            <div className="text-sm text-gray-500">
              <pre className="overflow-auto p-2 bg-gray-50 rounded">
                {JSON.stringify(component.data, null, 2)}
              </pre>
            </div>
          )}
        </div>;
      case 'company-overview':
        return <div className="p-4">
          <h3 className="text-md font-medium mb-2">Company Overview</h3>
          <div className="text-sm">{component.data.companyData?.about_the_company}</div>
          {component.data.companyMetrics &&
            <KeyMetrics
              keyMetricList={{ key_metrics: component.data.companyMetrics }}
              isMetricsExpanded={getMetricsExpanded(component.frontend_component_id)}
              setIsMetricsExpanded={(isExpanded) => setMetricsExpanded(component.frontend_component_id, isExpanded)}
              showHeader={false}
            />
          }
        </div>;
      case 'industry-overview':
        return <div className="p-4">
          <h3 className="text-md font-medium mb-2">Industry Overview</h3>
          <div className="text-sm">{component.data.industryData?.about_the_industry}</div>
          {component.data.industryData?.is_industry_risky === 'yes' && (
            <div className="mt-4 p-4 bg-red-50 rounded-lg">
              <h4 className="font-medium text-red-800 mb-2">Industry Risk Assessment</h4>
              <p className="text-sm text-red-700">{component.data.industryData.justification}</p>
            </div>
          )}
        </div>;
      case 'default-probability':
        return <div className="p-4">
          <h3 className="text-md font-medium mb-2">Probability of Default Analysis</h3>
          <div className="text-sm">Analysis for merchant ID: {component.data.merchantId}</div>
        </div>;
      case 'transaction-metrics':
        return <div className="p-4">
          <h3 className="text-md font-medium mb-2">Transaction Metrics</h3>
          {component.data.transactionMetrics &&
            <KeyMetrics
              keyMetricList={{ key_metrics: component.data.transactionMetrics }}
              isMetricsExpanded={getMetricsExpanded(component.frontend_component_id)}
              setIsMetricsExpanded={(isExpanded) => setMetricsExpanded(component.frontend_component_id, isExpanded)}
              showHeader={false}
            />
          }
        </div>;
      case 'transaction':
        return (
          <div className="grid grid-cols-[auto_1fr_auto] gap-4 p-4 bg-gray-50 border border-gray-200 rounded-lg hover:shadow-md transition-all duration-200 hover:bg-white">
            <div className="flex items-center">
              <CreditCard className="h-4 w-4 text-blue-500" />
            </div>
            <div className="space-y-1 min-w-0">
              <span className="text-sm font-medium block truncate">
                Transaction #{component.data?.transaction_id || 'Unknown'}
              </span>
              <p className="text-sm text-gray-600">
                Amount: ₹{component.data?.amount || '0'}
              </p>
            </div>
            <div className="flex flex-col items-end justify-between">
              <div className="flex items-center gap-2">
                {component.data?.status && (
                  <BubbleTag
                    text={component.data.status}
                    color={(component.data.status?.toLowerCase?.() === 'completed') ? 'green' : 'yellow'}
                  />
                )}
                {component.data?.payment_channel && (
                  <BubbleTag
                    text={component.data.payment_channel.toUpperCase?.() || component.data.payment_channel}
                    color="blue"
                  />
                )}
                {component.data?.timestamp && (
                  <span className="text-xs text-gray-500 ml-2">
                    {formatTimestamp(component.data.timestamp, 'relative')}
                  </span>
                )}
              </div>
              <span className="text-xs text-gray-500 mt-2">
                Customer: {component.data?.customer_name || 'Anonymous'}
              </span>
            </div>
          </div>
        );
      case 'payout':
        return (
          <div className="grid grid-cols-[auto_1fr_auto] gap-4 p-4 bg-gray-50 border border-gray-200 rounded-lg hover:shadow-md transition-all duration-200 hover:bg-white">
            <div className="flex items-center">
              <Banknote className="h-4 w-4 text-green-500" />
            </div>
            <div className="space-y-1 min-w-0">
              <span className="text-sm font-medium block truncate">
                Payout to {component.data?.bank_account || 'Bank'}
              </span>
              <p className="text-sm text-gray-600">
                Amount: ₹{component.data?.amount || '0'}
              </p>
            </div>
            <div className="flex flex-col items-end justify-between">
              <div className="flex items-center gap-2">
                {component.data?.status && (
                  <BubbleTag
                    text={component.data.status}
                    color={
                      component.data.status === 'processed' ? 'green' :
                        component.data.status === 'pending' ? 'yellow' :
                          'red'
                    }
                  />
                )}
                {component.data?.timestamp && (
                  <span className="text-xs text-gray-500 ml-2">
                    {formatTimestamp(component.data.timestamp, 'relative')}
                  </span>
                )}
              </div>
              <span className="text-xs text-gray-500 mt-2">
                Reference: {component.data?.reference_id || 'N/A'}
              </span>
            </div>
          </div>
        );
      case 'single-investigation':
        return (
          <div className="grid grid-cols-[auto_1fr_auto] gap-4 p-4 bg-gray-50 border border-gray-200 rounded-lg hover:shadow-md transition-all duration-200 hover:bg-white">
            <div className="flex items-center">
              <AlertTriangle className={`h-4 w-4 ${component.data?.investigation?.priority === 'High' ? 'text-red-500' : 'text-yellow-500'
                }`} />
            </div>
            <div className="space-y-1 min-w-0">
              <span className="text-sm font-medium block truncate">
                {component.data?.investigation?.case_number ? (
                  <>Case #{component.data.investigation.case_number} - {component.data.investigation.title}</>
                ) : (
                  <>Investigation</>
                )}
              </span>
              <p className="text-sm text-gray-600">Investigation details</p>
            </div>
            <div className="flex flex-col items-end justify-between">
              <div className="flex items-center gap-2">
                {component.data?.investigation?.status && (
                  <BubbleTag
                    text={component.data.investigation.status}
                    color="blue"
                  />
                )}
                {component.data?.investigation?.priority && (
                  <BubbleTag
                    text={`${component.data.investigation.priority} Priority`}
                    color={component.data.investigation.priority === 'High' ? 'red' : 'yellow'}
                  />
                )}
                {component.data?.investigation?.created_at && (
                  <span className="text-xs text-gray-500 ml-2">
                    {formatTimestamp(component.data.investigation.created_at, 'relative')}
                  </span>
                )}
              </div>
              {component.data?.investigation?.assignee_Name && (
                <span className="text-xs text-gray-500 mt-2">
                  Investigator: {component.data.investigation.assignee_Name}
                </span>
              )}
            </div>
          </div>
        );
      default:
        if (component.data?.chartImage) {
          return (
            <div>
              <h3 className="text-lg font-semibold mb-2">{component.data?.title}</h3>
              <img src={component.data.chartImage} alt={component.data?.title || 'Visualization'} style={{ width: '100%', maxHeight: 250, objectFit: 'contain', marginBottom: 8 }} />
            </div>
          );
        }
        return (
          <div className="p-4">
            <h3 className="text-md font-medium mb-2">{component.component_type.replace(/-/g, ' ').replace(/\b\w/g, c => c.toUpperCase())}</h3>
            <div className="text-sm">Component data not available for rendering</div>
          </div>
        );
    }
  };

  // Order components with risk-score and key-stats first
  const orderedComponents = [...components].sort((a, b) => {
    // Define the order of component types for the report
    const orderMap: Record<string, number> = {
      'risk-score': 1,
      'key-stats': 2,
      // Other components follow in original order
    };

    const orderA = orderMap[a.component_type] || 99;
    const orderB = orderMap[b.component_type] || 99;

    // If both are not risk-score or key-stats, maintain original order
    if (orderA === 99 && orderB === 99) {
      return 0;
    }

    return orderA - orderB;
  });

  return (
    <div className="space-y-6 p-4">
      <div id="report-content">
        <h2 className="text-xl font-semibold mb-6">{report.report_title}</h2>
        <div className="space-y-6">
          {/* Render all components in order */}
          {orderedComponents.map(component => (
            <div className="gap-6">
              <RemoveFromReport
                key={component.frontend_component_id}
                id={component.frontend_component_id}
                reportId={report.id}
              >
                <div className="border rounded-lg shadow-sm bg-white overflow-hidden">
                  <div className="p-2 bg-blue-50 border-b border-blue-100">
                    <div className="flex items-center">
                      <FileText className="h-4 w-4 text-blue-500 mr-2" />
                      <span className="text-sm font-medium">
                        {component.data?.title || component.component_type.replace(/-/g, ' ').replace(/\b\w/g, c => c.toUpperCase())}
                      </span>
                    </div>
                  </div>
                  <div>
                    {renderComponentContent(component)}
                  </div>
                </div>
              </RemoveFromReport>
            </div>
          ))}
        </div>
      </div>

      <div className="flex gap-2 no-print">
        <Button
          className="mt-4"
          onClick={() => updateReportComponents(report)}
        >
          Save Report
        </Button>
        <PDFDownloadLink
          document={<PDFDocument report={report} components={components} />}
          fileName={`Modus_Fraud_Report_${report.id}_${new Date().toISOString().split('T')[0]}.pdf`}
        >
          {((params: any) => (
            <Button
              className="mt-4"
              variant="outline"
              disabled={params.loading}
            >
              <Printer className="h-4 w-4 mr-2" />
              {params.loading ? 'Generating PDF...' : 'Print Report'}
            </Button>
          )) as any}
        </PDFDownloadLink>
      </div>
    </div>
  );
} 