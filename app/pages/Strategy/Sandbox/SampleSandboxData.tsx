import { z } from 'zod';

// API Response Types
export interface TableField {
  label: string;
  matching_fields: string[];
}

export interface TableDetails {
  id: string;
  name: string;
  description: string;
  schema: Record<string, string>;
  default_field_mapping: Record<string, TableField>;
  fields: Record<string, string>;
}

export interface BacktestingConfigResponse {
  data_tables: {
    table_details: TableDetails;
  }[];
}

// Schema definitions based on API response
export const transactionSchema = z.object({
  id: z.string().describe("UUID"),
  txn_id: z.string().describe("Transaction ID"),
  mer_id: z.string().describe("Merchant ID"),
  txn_amt: z.number().describe("Transaction amount"),
  fraud_label: z.boolean().describe("Fraud label"),
  txn_datetime: z.string().describe("Transaction datetime"),
  transaction_datetime: z.string().describe("Transaction datetime"),
  status: z.string().describe("Transaction status"),
  channel: z.string().describe("Transaction channel"),
  city: z.string().describe("City"),
  chargeback_amt_30d: z.number().describe("30-day chargeback amount"),
  txn_amt_30d: z.number().describe("30-day transaction amount"),
  created_at: z.string().describe("Created datetime"),
  updated_at: z.string().describe("Updated datetime")
});

export type Transaction = z.infer<typeof transactionSchema>;

// Updated available tables to use API response structure
export const availableTables = [];

// Types for backtesting configuration
export interface ColumnConfig {
  rowUniqueId: string;
  datetime: string;
  fraudLabel: string;
  amount: string;
  applicationId?: string;
  accountId?: string;
  transactionId?: string;
}

export const columnFieldDescriptions: Record<keyof ColumnConfig, string> = {
  rowUniqueId: "Unique identifier for each row in the dataset",
  datetime: "Timestamp of the event",
  fraudLabel: "Boolean flag indicating if the event is fraudulent",
  amount: "Monetary value of the transaction",
  applicationId: "Application ID",
  accountId: "Account ID",
  transactionId: "Transaction ID"
};

export interface TimeframeFilter {
  startDate: string;
  endDate: string;
}

export interface PopulationFilter {
  column: string;
  operator: '>=' | '<=' | 'is in';
  value: string | number | string[];
}

export interface BacktestingConfig {
  selectedTable: string;
  columnConfig: ColumnConfig;
  timeframeFilter: TimeframeFilter;
  populationFilters: PopulationFilter[];
}
