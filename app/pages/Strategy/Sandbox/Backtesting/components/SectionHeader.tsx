import React from 'react';
import { ChevronDown, ChevronRight, LucideIcon, Check, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { CustomCard } from '@/components/custom/CustomCard';
import { motion, AnimatePresence } from 'framer-motion';

const ANIMATION_DURATION = 200; // ms
const EXPAND_DELAY = 300; // ms

export type SectionButtonType = 'save' | 'run' | 'autodetect-columns' | 'autodetect-timeframe' | 'create-custom-metric';

export type SectionButtonConfig = {
  icon: LucideIcon;
  text: string;
  onClick: (e: React.MouseEvent) => void;
  disabled?: boolean;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  type?: SectionButtonType;
};

interface SectionProps {
  title: string;
  icon: React.ReactNode;
  isExpanded: boolean;
  onToggle: () => void;
  buttonConfigs?: SectionButtonConfig[];
  children: React.ReactNode;
  warnings?: string[];
  onExpandComplete?: () => void;
}

const buttonTypeOrder: Record<SectionButtonType, number> = {
  'autodetect-timeframe': 0,
  'autodetect-columns': 1,
  'create-custom-metric': 2,
  'run': 3,
  'save': 4
};

export const Section: React.FC<SectionProps> = ({
  title,
  icon,
  isExpanded,
  onToggle,
  buttonConfigs = [],
  children,
  warnings = [],
  onExpandComplete
}) => {
  const hasWarnings = warnings.length > 0;

  // Sort buttons by type for consistent ordering
  const sortedButtonConfigs = [...buttonConfigs].sort((a, b) => {
    const aOrder = a.type ? buttonTypeOrder[a.type] : 999;
    const bOrder = b.type ? buttonTypeOrder[b.type] : 999;
    return aOrder - bOrder;
  });

  const handleButtonClick = (e: React.MouseEvent, config: SectionButtonConfig) => {
    e.stopPropagation();
    if (config.disabled) return;

    // For autodetect buttons, we want to run the function after expansion
    if (config.type?.startsWith('autodetect-')) {
      if (!isExpanded) {
        onToggle();
        // Execute click handler after expansion animation
        setTimeout(() => config.onClick(e), EXPAND_DELAY);
      } else {
        config.onClick(e);
      }
    } else {
      // For other buttons (save/run), just execute immediately
      config.onClick(e);
    }
  };

  return (
    <CustomCard className="overflow-hidden">
      <div className="flex items-center justify-between p-4">
        <div 
          className="flex items-center space-x-2 cursor-pointer flex-1 min-w-0"
          onClick={onToggle}
        >
          {isExpanded ? (
            <ChevronDown className="h-5 w-5 text-muted-foreground flex-shrink-0" />
          ) : (
            <ChevronRight className="h-5 w-5 text-muted-foreground flex-shrink-0" />
          )}
          <div className="flex items-center space-x-2 min-w-0">
            {icon}
            <h3 className="font-medium truncate">{title}</h3>
            {hasWarnings ? (
              <AlertCircle className="h-5 w-5 text-destructive flex-shrink-0" />
            ) : (
              <Check className="h-5 w-5 text-green-500 flex-shrink-0" />
            )}
          </div>
        </div>
        
        <div className="flex items-center space-x-2 ml-4">
          {sortedButtonConfigs.map((config, index) => {
            const Icon = config.icon;
            return (
              <Button
                key={index}
                variant={config.variant || "outline"}
                size="sm"
                onClick={(e) => handleButtonClick(e, config)}
                disabled={config.disabled}
                className={`${config.disabled ? "opacity-50" : ""} px-2.5 py-1 h-8 text-sm whitespace-nowrap flex items-center min-w-fit`}
              >
                {Icon && <Icon className="h-3.5 w-3.5 mr-1.5 flex-shrink-0" />}
                <span>{config.text}</span>
              </Button>
            );
          })}
        </div>
      </div>
      
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: ANIMATION_DURATION / 1000, ease: "easeInOut" }}
            onAnimationComplete={onExpandComplete}
          >
            <div className="p-4 pt-0">
              {children}
              {hasWarnings && (
                <div className="mt-4 pt-4 border-t border-border">
                  {warnings.map((warning, index) => (
                    <div key={index} className="flex items-center gap-2 text-destructive text-sm">
                      <AlertCircle className="h-4 w-4 flex-shrink-0" />
                      <span>{warning}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </CustomCard>
  );
}; 