import React, { useState } from 'react';
import { MetricEquation, ColumnStatistics } from '@/app/types';
import { TimeframeFilter, PopulationFilter, ColumnConfig } from '../../SampleSandboxData';
import { BacktestingAnalysisSettings } from '../types';

export interface RuleAnalysisData {
  rule: {
    metric_equation: MetricEquation;
    rule_code: string;
    created_at: string;
    created_by: string;
    rule_status: boolean;
    rule_severity: 'Low' | 'Medium' | 'High' | 'Critical';
    version: number;
  };
}

export interface MetricAnalysisData {
  columns: Array<{
    column: string;
    description?: string;
  }>;
  timeframe: TimeframeFilter;
  populationFilters: PopulationFilter[];
  analysisId: string;
}

export type AnalysisData = {
  type: 'rule' | 'metric';
  data: RuleAnalysisData | MetricAnalysisData;
  settings: BacktestingAnalysisSettings;
};

interface AnalysisArtifactProps {
  analysis: AnalysisData;
}

const Collapsible: React.FC<{ title: string; children: React.ReactNode; defaultOpen?: boolean }> = ({ title, children, defaultOpen = true }) => {
  const [open, setOpen] = useState(defaultOpen);
  return (
    <div className="border rounded bg-white">
      <button
        className="w-full flex items-center justify-between px-3 py-2 text-sm font-semibold bg-gray-50 hover:bg-gray-100 focus:outline-none"
        onClick={() => setOpen((v) => !v)}
        aria-expanded={open}
      >
        <span>{title}</span>
        <span className="ml-2 text-xs">{open ? '▲' : '▼'}</span>
      </button>
      {open && <div className="px-3 py-2 border-t text-xs text-gray-800 space-y-2">{children}</div>}
    </div>
  );
};

const CompactSettings: React.FC<{ settings: BacktestingAnalysisSettings; type: 'rule' | 'metric' }> = ({ settings, type }) => (
  <div className="space-y-4">
    <div className="flex flex-wrap gap-x-8 gap-y-2">
      <div>
        <span className="font-medium">Data Table:</span> <span>{settings.dataTable.selectedTable}</span>
        {settings.dataTable.tableDescription && <span className="ml-1 text-gray-500">({settings.dataTable.tableDescription})</span>}
      </div>
      <div>
        <span className="font-medium">Timestamp:</span> <span>{new Date(settings.timestamp).toLocaleString()}</span>
      </div>
      <div>
        <span className="font-medium">Type:</span> <span className="capitalize">{settings.analysisType} analysis</span>
      </div>
    </div>
    <div className="flex flex-wrap gap-x-8 gap-y-2">
      <div>
        <span className="font-medium">Timeframe:</span> <span>{settings.filters.timeframeFilter.startDate} to {settings.filters.timeframeFilter.endDate}</span>
        {settings.filters.timeframeAutoDetected && <span className="ml-1 text-blue-600">(Auto-detected)</span>}
      </div>
      <div>
        <span className="font-medium">Population Filters:</span>
        {settings.filters.populationFilters.length > 0 ? (
          <ul className="ml-2 mt-1 list-disc">
            {settings.filters.populationFilters.map((filter, idx) => (
              <li key={idx} className="text-gray-700">
                <span className="font-semibold">{filter.column}</span> {filter.operator} {String(filter.value)}
              </li>
            ))}
          </ul>
        ) : (
          <span className="ml-1 text-gray-500">None</span>
        )}
      </div>
    </div>
    <div>
      <span className="font-medium">Column Mappings:</span>
      <ul className="ml-2 mt-1 list-disc space-y-1">
        {Object.entries(settings.configuration.columnMappings).map(([key, mapping]) => (
          <li key={key} className="ml-2">
            <span className="font-semibold">{key}</span>: <span>{mapping.mappedTo || 'Not mapped'}</span>
            <span className="ml-1 text-gray-400">({mapping.description})</span>
          </li>
        ))}
      </ul>
    </div>
    {type === 'rule' && settings.analysis.rule && (
      <div>
        <span className="font-medium">Rule Operator:</span> <span>{settings.analysis.rule.operator}</span>
        <div className="mt-1">
          <span className="font-medium">Conditions:</span>
          <ul className="ml-2 mt-1 list-disc space-y-1">
            {settings.analysis.rule.conditions.map((condition, idx) => (
              <li key={idx}>
                <span className="font-semibold">{condition.metric_name}</span> {condition.operation} {String(condition.value)}
                <span className="ml-1 text-gray-400">({condition.type})</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    )}
    {type === 'metric' && settings.analysis.metric && (
      <div>
        <span className="font-medium">Metric Columns:</span>
        <ul className="ml-2 mt-1 list-disc space-y-2">
          {settings.analysis.metric.selectedColumns.map(({ column, description }) => (
            <li key={column}>
              <div className="flex items-start justify-between">
                <div>
                  <span className="font-semibold">{column}</span>
                  {description && <span className="ml-1 text-gray-400">({description})</span>}
                </div>
              </div>
            </li>
          ))}
        </ul>
      </div>
    )}
  </div>
);

export const AnalysisArtifact: React.FC<AnalysisArtifactProps> = ({ analysis }) => {
  const { type, data, settings } = analysis;
  const analysisId = type === 'rule'
    ? (data as RuleAnalysisData).rule.rule_code
    : (data as MetricAnalysisData).analysisId;
  const analysisTitle = type === 'rule' ? 'Rule Analysis' : 'Metric Analysis';

  return (
    <div className="max-w-full h-[70vh] overflow-auto bg-white border rounded shadow-sm">
      <div className="p-5 max-w-3xl mx-auto space-y-3">
        <div className="flex items-center justify-between border-b pb-2 mb-2">
          <div>
            <h2 className="text-lg font-semibold tracking-tight text-gray-900">{analysisTitle} Results</h2>
            <div className="text-xs text-gray-500 mt-0.5">Analysis ID: <span className="text-blue-700 font-mono">{analysisId}</span></div>
          </div>
        </div>
        <Collapsible title="Analysis Settings" defaultOpen>
          <CompactSettings settings={settings} type={type} />
        </Collapsible>
      </div>
    </div>
  );
}; 