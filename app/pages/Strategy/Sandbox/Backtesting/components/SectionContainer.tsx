import React from 'react';

interface SectionContainerProps {
  title: string;
  children: React.ReactNode;
  rightElement?: React.ReactNode;
}

export const SectionContainer: React.FC<SectionContainerProps> = ({
  title,
  children,
  rightElement
}) => {
  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium text-muted-foreground">{title}</h4>
        {rightElement}
      </div>
      {children}
    </div>
  );
}; 