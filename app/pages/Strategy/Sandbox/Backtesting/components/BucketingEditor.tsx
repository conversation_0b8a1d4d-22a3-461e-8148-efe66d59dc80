import React, { useState, useCallback, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MultiSelect } from '@/components/ui/multi-select2';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Trash2, RotateCcw } from 'lucide-react';
import { BucketConfig, ColumnBucketing, ColumnStatistics } from '@/app/types';

interface BucketingEditorProps {
  isOpen: boolean;
  onClose: () => void;
  column: string;
  columnType: 'numeric' | 'categorical' | 'datetime';
  statistics?: ColumnStatistics;
  currentBucketing?: ColumnBucketing;
  onSave: (bucketing: ColumnBucketing) => void;
}

export const BucketingEditor: React.FC<BucketingEditorProps> = ({
  isOpen,
  onClose,
  column,
  columnType,
  statistics,
  currentBucketing,
  onSave
}) => {
  const [bucketingType, setBucketingType] = useState<'auto' | 'custom'>(
    currentBucketing?.bucketingType || 'auto'
  );
  const [buckets, setBuckets] = useState<BucketConfig[]>(
    currentBucketing?.buckets || []
  );

  // Reset state when dialog opens
  useEffect(() => {
    if (isOpen) {
      setBucketingType(currentBucketing?.bucketingType || 'auto');
      setBuckets(currentBucketing?.buckets || []);
    }
  }, [isOpen, currentBucketing]);

  // Generate auto buckets based on actual data
  const generateAutoBuckets = useCallback((): BucketConfig[] => {
    if (!statistics || !statistics.allValues) return [];

    const allValues = statistics.allValues;
    const totalValues = allValues.length;

    if (columnType === 'numeric' && allValues.length > 0) {
      // Convert string values to numbers and filter out invalid ones
      const numericValues = allValues
        .map(val => parseFloat(val))
        .filter(val => !isNaN(val))
        .sort((a, b) => a - b);

      if (numericValues.length === 0) return [];

      const min = numericValues[0];
      const max = numericValues[numericValues.length - 1];
      const numBuckets = Math.min(5, Math.max(3, Math.ceil(Math.sqrt(numericValues.length))));
      const range = max - min;

      if (range === 0) {
        // All values are the same
        return [{
          id: 'auto-0',
          label: `${min}`,
          type: 'auto',
          range: { min, max },
          count: numericValues.length,
          percentage: 100
        }];
      }

      // Determine rounding unit based on range
      let roundingUnit: number;
      if (range >= 10000) roundingUnit = 1000;
      else if (range >= 1000) roundingUnit = 100;
      else if (range >= 100) roundingUnit = 10;
      else if (range >= 10) roundingUnit = 5;
      else if (range >= 1) roundingUnit = 1;
      else roundingUnit = 0.1;

      const roundedMin = Math.floor(min / roundingUnit) * roundingUnit;
      const roundedMax = Math.ceil(max / roundingUnit) * roundingUnit;
      const bucketSize = (roundedMax - roundedMin) / numBuckets;

      // Create buckets and count actual values in each bucket
      const buckets: BucketConfig[] = [];
      for (let i = 0; i < numBuckets; i++) {
        const bucketMin = roundedMin + i * bucketSize;
        const bucketMax = i === numBuckets - 1 ? roundedMax : roundedMin + (i + 1) * bucketSize;

        // Count values in this bucket
        const valuesInBucket = numericValues.filter(val =>
          val >= bucketMin && (i === numBuckets - 1 ? val <= bucketMax : val < bucketMax)
        );

        buckets.push({
          id: `auto-${i}`,
          label: `${bucketMin.toFixed(roundingUnit < 1 ? 1 : 0)} - ${bucketMax.toFixed(roundingUnit < 1 ? 1 : 0)}`,
          type: 'auto',
          range: { min: bucketMin, max: bucketMax },
          count: valuesInBucket.length,
          percentage: totalValues > 0 ? (valuesInBucket.length / totalValues) * 100 : 0
        });
      }
      return buckets;
    }

    if (columnType === 'categorical' && allValues.length > 0) {
      // Count occurrences of each value
      const valueCounts = allValues.reduce((acc, value) => {
        acc[value] = (acc[value] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Sort by count (descending) and create buckets
      return Object.entries(valueCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 20) // Limit to top 20 values
        .map(([value, count], i) => ({
          id: `auto-${i}`,
          label: value,
          type: 'auto' as const,
          values: [value],
          count,
          percentage: totalValues > 0 ? (count / totalValues) * 100 : 0
        }));
    }

    if (columnType === 'datetime' && allValues.length > 0) {
      // Parse datetime values and create time-based buckets
      const dateValues = allValues
        .map(val => new Date(val))
        .filter(date => !isNaN(date.getTime()))
        .sort((a, b) => a.getTime() - b.getTime());

      if (dateValues.length === 0) return [];

      const minDate = dateValues[0];
      const maxDate = dateValues[dateValues.length - 1];
      const timeDiff = maxDate.getTime() - minDate.getTime();
      const daysDiff = timeDiff / (1000 * 60 * 60 * 24);

      // Determine appropriate time bucketing based on data range
      let bucketType: 'hour' | 'day' | 'week' | 'month' | 'year';
      if (daysDiff <= 1) bucketType = 'hour';
      else if (daysDiff <= 31) bucketType = 'day';
      else if (daysDiff <= 90) bucketType = 'week';
      else if (daysDiff <= 730) bucketType = 'month';
      else bucketType = 'year';

      // Group dates by the determined bucket type
      const dateBuckets = dateValues.reduce((acc, date) => {
        let key: string;
        switch (bucketType) {
          case 'hour':
            key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:00`;
            break;
          case 'day':
            key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
            break;
          case 'week':
            const weekStart = new Date(date);
            weekStart.setDate(date.getDate() - date.getDay());
            key = `Week of ${weekStart.getFullYear()}-${String(weekStart.getMonth() + 1).padStart(2, '0')}-${String(weekStart.getDate()).padStart(2, '0')}`;
            break;
          case 'month':
            key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            break;
          case 'year':
            key = `${date.getFullYear()}`;
            break;
        }
        acc[key] = (acc[key] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Create buckets from grouped dates
      return Object.entries(dateBuckets)
        .sort(([a], [b]) => a.localeCompare(b))
        .map(([period, count], i) => ({
          id: `auto-${i}`,
          label: period,
          type: 'auto' as const,
          count,
          percentage: totalValues > 0 ? (count / totalValues) * 100 : 0
        }));
    }

    return [];
  }, [columnType, statistics]);

  // Initialize auto buckets when switching to auto mode
  useEffect(() => {
    if (bucketingType === 'auto' && buckets.length === 0) {
      setBuckets(generateAutoBuckets());
    }
  }, [bucketingType, buckets.length, generateAutoBuckets]);

  const handleSave = () => {
    const totalCount = buckets.reduce((sum, bucket) => sum + bucket.count, 0);
    const bucketing: ColumnBucketing = {
      buckets,
      bucketingType,
      totalCount
    };
    onSave(bucketing);
    onClose();
  };

  const handleReset = () => {
    if (bucketingType === 'auto') {
      setBuckets(generateAutoBuckets());
    } else {
      setBuckets([]);
    }
  };

  const addCustomBucket = () => {
    const newBucket: BucketConfig = {
      id: `custom-${Date.now()}`,
      label: `Custom Bucket ${buckets.length + 1}`,
      type: 'custom',
      values: [],
      count: 0,
      percentage: 0
    };
    setBuckets([...buckets, newBucket]);
  };

  const removeBucket = (bucketId: string) => {
    setBuckets(buckets.filter(b => b.id !== bucketId));
  };

  const updateBucket = (bucketId: string, updates: Partial<BucketConfig>) => {
    setBuckets(buckets.map(b => b.id === bucketId ? { ...b, ...updates } : b));
  };

  // Get available values for custom bucketing
  const availableValues = statistics?.allValues ||
    (statistics?.categoricalStats?.valueCounts.map(v => v.value)) || [];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Configure Bucketing for {column}</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Label>Bucketing Type:</Label>
            <Select value={bucketingType} onValueChange={(value: 'auto' | 'custom') => setBucketingType(value)}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="auto">Auto Bucketing</SelectItem>
                <SelectItem value="custom">Custom Bucketing</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm" onClick={handleReset}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
          </div>

          <Tabs defaultValue="buckets" className="w-full">
            <TabsList>
              <TabsTrigger value="buckets">Buckets ({buckets.length})</TabsTrigger>
              {columnType === 'categorical' && (
                <TabsTrigger value="values">Available Values ({availableValues.length})</TabsTrigger>
              )}
            </TabsList>

            <TabsContent value="buckets" className="space-y-4">
              {bucketingType === 'custom' && (
                <Button onClick={addCustomBucket} className="w-full">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Custom Bucket
                </Button>
              )}

              <div className="space-y-2 max-h-96 overflow-y-auto">
                {buckets.map((bucket) => (
                  <BucketConfigItem
                    key={bucket.id}
                    bucket={bucket}
                    columnType={columnType}
                    availableValues={availableValues}
                    onUpdate={(updates) => updateBucket(bucket.id, updates)}
                    onRemove={() => removeBucket(bucket.id)}
                    canRemove={bucketingType === 'custom'}
                  />
                ))}
              </div>
            </TabsContent>

            {columnType === 'categorical' && (
              <TabsContent value="values" className="space-y-2">
                <div className="text-sm text-gray-600 mb-2">
                  Available values in this column:
                </div>
                <div className="max-h-64 overflow-y-auto space-y-1">
                  {availableValues.map((value, i) => (
                    <div key={i} className="p-2 bg-gray-50 rounded text-sm">
                      {value}
                    </div>
                  ))}
                </div>
              </TabsContent>
            )}
          </Tabs>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Bucketing
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

interface BucketConfigItemProps {
  bucket: BucketConfig;
  columnType: 'numeric' | 'categorical' | 'datetime';
  availableValues: string[];
  onUpdate: (updates: Partial<BucketConfig>) => void;
  onRemove: () => void;
  canRemove: boolean;
}

const BucketConfigItem: React.FC<BucketConfigItemProps> = ({
  bucket,
  columnType,
  availableValues,
  onUpdate,
  onRemove,
  canRemove
}) => {
  return (
    <div className="border rounded-lg p-3 space-y-2">
      <div className="flex items-center justify-between">
        <Input
          value={bucket.label}
          onChange={(e) => onUpdate({ label: e.target.value })}
          placeholder="Bucket label"
          className="flex-1 mr-2"
        />
        {canRemove && (
          <Button variant="ghost" size="sm" onClick={onRemove}>
            <Trash2 className="h-4 w-4" />
          </Button>
        )}
      </div>

      {columnType === 'categorical' && bucket.type === 'custom' && (
        <div>
          <Label className="text-xs">Select Values:</Label>
          <MultiSelect
            options={availableValues.map(value => ({ label: value, value }))}
            onValueChange={(values) => onUpdate({ values })}
            defaultValue={bucket.values || []}
            placeholder="Select values for this bucket"
            className="mt-1"
          />
        </div>
      )}

      {columnType === 'numeric' && bucket.type === 'custom' && (
        <div className="grid grid-cols-2 gap-2">
          <div>
            <Label className="text-xs">Min Value:</Label>
            <Input
              type="number"
              value={bucket.range?.min || ''}
              onChange={(e) => onUpdate({
                range: {
                  min: parseFloat(e.target.value) || 0,
                  max: bucket.range?.max || 0
                }
              })}
              placeholder="Min"
            />
          </div>
          <div>
            <Label className="text-xs">Max Value:</Label>
            <Input
              type="number"
              value={bucket.range?.max || ''}
              onChange={(e) => onUpdate({
                range: {
                  min: bucket.range?.min || 0,
                  max: parseFloat(e.target.value) || 0
                }
              })}
              placeholder="Max"
            />
          </div>
        </div>
      )}

      <div className="text-xs text-gray-500">
        Count: {bucket.count} ({bucket.percentage.toFixed(1)}%)
      </div>
    </div>
  );
};
