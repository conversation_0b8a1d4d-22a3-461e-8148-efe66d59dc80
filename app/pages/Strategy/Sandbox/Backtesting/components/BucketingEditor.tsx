import React, { useState, useCallback, useEffect } from 'react';
import { Di<PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MultiSelect } from '@/components/ui/multi-select2';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Trash2, Edit3, RotateCcw } from 'lucide-react';
import { BucketConfig, ColumnBucketing, ColumnStatistics } from '@/app/types';

interface BucketingEditorProps {
  isOpen: boolean;
  onClose: () => void;
  column: string;
  columnType: 'numeric' | 'categorical' | 'datetime';
  statistics?: ColumnStatistics;
  currentBucketing?: ColumnBucketing;
  onSave: (bucketing: ColumnBucketing) => void;
}

export const BucketingEditor: React.FC<BucketingEditorProps> = ({
  isOpen,
  onClose,
  column,
  columnType,
  statistics,
  currentBucketing,
  onSave
}) => {
  const [bucketingType, setBucketingType] = useState<'auto' | 'custom'>(
    currentBucketing?.bucketingType || 'auto'
  );
  const [buckets, setBuckets] = useState<BucketConfig[]>(
    currentBucketing?.buckets || []
  );

  // Reset state when dialog opens
  useEffect(() => {
    if (isOpen) {
      setBucketingType(currentBucketing?.bucketingType || 'auto');
      setBuckets(currentBucketing?.buckets || []);
    }
  }, [isOpen, currentBucketing]);

  // Generate auto buckets based on column type
  const generateAutoBuckets = useCallback((): BucketConfig[] => {
    if (!statistics) return [];

    if (columnType === 'numeric' && statistics.numericStats) {
      const { min, max } = statistics.numericStats;
      const numBuckets = 5;
      const range = max - min;
      
      // Determine rounding unit
      let roundingUnit: number;
      if (range >= 10000) roundingUnit = 1000;
      else if (range >= 1000) roundingUnit = 100;
      else if (range >= 100) roundingUnit = 10;
      else if (range >= 10) roundingUnit = 5;
      else roundingUnit = 1;

      const roundedMin = Math.floor(min / roundingUnit) * roundingUnit;
      const roundedMax = Math.ceil(max / roundingUnit) * roundingUnit;
      const bucketSize = (roundedMax - roundedMin) / numBuckets;

      return Array(numBuckets).fill(0).map((_, i) => ({
        id: `auto-${i}`,
        label: `${(roundedMin + i * bucketSize).toFixed(2)} - ${(roundedMin + (i + 1) * bucketSize).toFixed(2)}`,
        type: 'auto' as const,
        range: {
          min: roundedMin + i * bucketSize,
          max: roundedMin + (i + 1) * bucketSize
        },
        count: 0, // Will be populated with real data
        percentage: 0
      }));
    }

    if (columnType === 'categorical' && statistics.categoricalStats) {
      return statistics.categoricalStats.valueCounts.map((item, i) => ({
        id: `auto-${i}`,
        label: item.value,
        type: 'auto' as const,
        values: [item.value],
        count: item.count,
        percentage: item.percentage * 100
      }));
    }

    if (columnType === 'datetime' && statistics.datetimeStats) {
      // Create time-based buckets (daily, weekly, monthly)
      return [
        {
          id: 'auto-daily',
          label: 'Daily',
          type: 'auto' as const,
          count: 0,
          percentage: 0
        },
        {
          id: 'auto-weekly',
          label: 'Weekly',
          type: 'auto' as const,
          count: 0,
          percentage: 0
        },
        {
          id: 'auto-monthly',
          label: 'Monthly',
          type: 'auto' as const,
          count: 0,
          percentage: 0
        }
      ];
    }

    return [];
  }, [columnType, statistics]);

  // Initialize auto buckets when switching to auto mode
  useEffect(() => {
    if (bucketingType === 'auto' && buckets.length === 0) {
      setBuckets(generateAutoBuckets());
    }
  }, [bucketingType, buckets.length, generateAutoBuckets]);

  const handleSave = () => {
    const totalCount = buckets.reduce((sum, bucket) => sum + bucket.count, 0);
    const bucketing: ColumnBucketing = {
      buckets,
      bucketingType,
      totalCount
    };
    onSave(bucketing);
    onClose();
  };

  const handleReset = () => {
    if (bucketingType === 'auto') {
      setBuckets(generateAutoBuckets());
    } else {
      setBuckets([]);
    }
  };

  const addCustomBucket = () => {
    const newBucket: BucketConfig = {
      id: `custom-${Date.now()}`,
      label: `Custom Bucket ${buckets.length + 1}`,
      type: 'custom',
      values: [],
      count: 0,
      percentage: 0
    };
    setBuckets([...buckets, newBucket]);
  };

  const removeBucket = (bucketId: string) => {
    setBuckets(buckets.filter(b => b.id !== bucketId));
  };

  const updateBucket = (bucketId: string, updates: Partial<BucketConfig>) => {
    setBuckets(buckets.map(b => b.id === bucketId ? { ...b, ...updates } : b));
  };

  // Get available values for custom bucketing
  const availableValues = statistics?.allValues || 
    (statistics?.categoricalStats?.valueCounts.map(v => v.value)) || [];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Configure Bucketing for {column}</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Label>Bucketing Type:</Label>
            <Select value={bucketingType} onValueChange={(value: 'auto' | 'custom') => setBucketingType(value)}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="auto">Auto Bucketing</SelectItem>
                <SelectItem value="custom">Custom Bucketing</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm" onClick={handleReset}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
          </div>

          <Tabs defaultValue="buckets" className="w-full">
            <TabsList>
              <TabsTrigger value="buckets">Buckets ({buckets.length})</TabsTrigger>
              {columnType === 'categorical' && (
                <TabsTrigger value="values">Available Values ({availableValues.length})</TabsTrigger>
              )}
            </TabsList>

            <TabsContent value="buckets" className="space-y-4">
              {bucketingType === 'custom' && (
                <Button onClick={addCustomBucket} className="w-full">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Custom Bucket
                </Button>
              )}

              <div className="space-y-2 max-h-96 overflow-y-auto">
                {buckets.map((bucket) => (
                  <BucketConfigItem
                    key={bucket.id}
                    bucket={bucket}
                    columnType={columnType}
                    availableValues={availableValues}
                    onUpdate={(updates) => updateBucket(bucket.id, updates)}
                    onRemove={() => removeBucket(bucket.id)}
                    canRemove={bucketingType === 'custom'}
                  />
                ))}
              </div>
            </TabsContent>

            {columnType === 'categorical' && (
              <TabsContent value="values" className="space-y-2">
                <div className="text-sm text-gray-600 mb-2">
                  Available values in this column:
                </div>
                <div className="max-h-64 overflow-y-auto space-y-1">
                  {availableValues.map((value, i) => (
                    <div key={i} className="p-2 bg-gray-50 rounded text-sm">
                      {value}
                    </div>
                  ))}
                </div>
              </TabsContent>
            )}
          </Tabs>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Bucketing
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

interface BucketConfigItemProps {
  bucket: BucketConfig;
  columnType: 'numeric' | 'categorical' | 'datetime';
  availableValues: string[];
  onUpdate: (updates: Partial<BucketConfig>) => void;
  onRemove: () => void;
  canRemove: boolean;
}

const BucketConfigItem: React.FC<BucketConfigItemProps> = ({
  bucket,
  columnType,
  availableValues,
  onUpdate,
  onRemove,
  canRemove
}) => {
  return (
    <div className="border rounded-lg p-3 space-y-2">
      <div className="flex items-center justify-between">
        <Input
          value={bucket.label}
          onChange={(e) => onUpdate({ label: e.target.value })}
          placeholder="Bucket label"
          className="flex-1 mr-2"
        />
        {canRemove && (
          <Button variant="ghost" size="sm" onClick={onRemove}>
            <Trash2 className="h-4 w-4" />
          </Button>
        )}
      </div>

      {columnType === 'categorical' && bucket.type === 'custom' && (
        <div>
          <Label className="text-xs">Select Values:</Label>
          <MultiSelect
            options={availableValues.map(value => ({ label: value, value }))}
            onValueChange={(values) => onUpdate({ values })}
            defaultValue={bucket.values || []}
            placeholder="Select values for this bucket"
            className="mt-1"
          />
        </div>
      )}

      {columnType === 'numeric' && bucket.type === 'custom' && (
        <div className="grid grid-cols-2 gap-2">
          <div>
            <Label className="text-xs">Min Value:</Label>
            <Input
              type="number"
              value={bucket.range?.min || ''}
              onChange={(e) => onUpdate({ 
                range: { ...bucket.range, min: parseFloat(e.target.value) || 0 } 
              })}
              placeholder="Min"
            />
          </div>
          <div>
            <Label className="text-xs">Max Value:</Label>
            <Input
              type="number"
              value={bucket.range?.max || ''}
              onChange={(e) => onUpdate({ 
                range: { ...bucket.range, max: parseFloat(e.target.value) || 0 } 
              })}
              placeholder="Max"
            />
          </div>
        </div>
      )}

      <div className="text-xs text-gray-500">
        Count: {bucket.count} ({bucket.percentage.toFixed(1)}%)
      </div>
    </div>
  );
};
