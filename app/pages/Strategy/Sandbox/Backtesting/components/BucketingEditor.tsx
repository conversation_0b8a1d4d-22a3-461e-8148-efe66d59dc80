import React, { useState, useCallback, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MultiSelect } from '@/components/ui/multi-select2';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Trash2, RotateCcw } from 'lucide-react';
import { BucketConfig, ColumnBucketing, ColumnStatistics } from '@/app/types';

interface BucketingEditorProps {
  isOpen: boolean;
  onClose: () => void;
  column: string;
  columnType: 'numeric' | 'categorical' | 'datetime';
  statistics?: ColumnStatistics;
  currentBucketing?: ColumnBucketing;
  onSave: (bucketing: ColumnBucketing) => void;
}

export const BucketingEditor: React.FC<BucketingEditorProps> = ({
  isOpen,
  onClose,
  column,
  columnType,
  statistics,
  currentBucketing,
  onSave
}) => {
  const [bucketingType, setBucketingType] = useState<'auto' | 'custom'>(
    currentBucketing?.bucketingType || 'auto'
  );
  const [buckets, setBuckets] = useState<BucketConfig[]>(
    currentBucketing?.buckets || []
  );

  // Reset state when dialog opens
  useEffect(() => {
    if (isOpen) {
      setBucketingType(currentBucketing?.bucketingType || 'auto');
      setBuckets(currentBucketing?.buckets || []);
    }
  }, [isOpen, currentBucketing]);

  // Generate auto buckets based on actual data
  const generateAutoBuckets = useCallback((): BucketConfig[] => {
    if (!statistics || !statistics.allValues) return [];

    const allValues = statistics.allValues;
    const totalValues = allValues.length;

    if (columnType === 'numeric' && allValues.length > 0) {
      // Convert string values to numbers and filter out invalid ones
      const numericValues = allValues
        .map(val => parseFloat(val))
        .filter(val => !isNaN(val))
        .sort((a, b) => a - b);

      if (numericValues.length === 0) return [];

      const min = numericValues[0];
      const max = numericValues[numericValues.length - 1];
      const numBuckets = Math.min(5, Math.max(3, Math.ceil(Math.sqrt(numericValues.length))));
      const range = max - min;

      if (range === 0) {
        // All values are the same
        return [{
          id: 'auto-0',
          label: `${min}`,
          type: 'auto',
          range: { min, max },
          count: numericValues.length,
          percentage: 100
        }];
      }

      // Determine rounding unit based on range
      let roundingUnit: number;
      if (range >= 10000) roundingUnit = 1000;
      else if (range >= 1000) roundingUnit = 100;
      else if (range >= 100) roundingUnit = 10;
      else if (range >= 10) roundingUnit = 5;
      else if (range >= 1) roundingUnit = 1;
      else roundingUnit = 0.1;

      const roundedMin = Math.floor(min / roundingUnit) * roundingUnit;
      const roundedMax = Math.ceil(max / roundingUnit) * roundingUnit;
      const bucketSize = (roundedMax - roundedMin) / numBuckets;

      // Create buckets and count actual values in each bucket
      const buckets: BucketConfig[] = [];
      for (let i = 0; i < numBuckets; i++) {
        const bucketMin = roundedMin + i * bucketSize;
        const bucketMax = i === numBuckets - 1 ? roundedMax : roundedMin + (i + 1) * bucketSize;

        // Count values in this bucket
        const valuesInBucket = numericValues.filter(val =>
          val >= bucketMin && (i === numBuckets - 1 ? val <= bucketMax : val < bucketMax)
        );

        buckets.push({
          id: `auto-${i}`,
          label: `${bucketMin.toFixed(roundingUnit < 1 ? 1 : 0)} - ${bucketMax.toFixed(roundingUnit < 1 ? 1 : 0)}`,
          type: 'auto',
          range: { min: bucketMin, max: bucketMax },
          count: valuesInBucket.length,
          percentage: totalValues > 0 ? (valuesInBucket.length / totalValues) * 100 : 0
        });
      }
      return buckets;
    }

    if (columnType === 'categorical' && allValues.length > 0) {
      // Count occurrences of each value
      const valueCounts = allValues.reduce((acc, value) => {
        acc[value] = (acc[value] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Sort by count (descending) and create buckets
      return Object.entries(valueCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 20) // Limit to top 20 values
        .map(([value, count], i) => ({
          id: `auto-${i}`,
          label: value,
          type: 'auto' as const,
          values: [value],
          count,
          percentage: totalValues > 0 ? (count / totalValues) * 100 : 0
        }));
    }

    if (columnType === 'datetime' && allValues.length > 0) {
      // Parse datetime values and create time-based buckets
      const dateValues = allValues
        .map(val => new Date(val))
        .filter(date => !isNaN(date.getTime()))
        .sort((a, b) => a.getTime() - b.getTime());

      if (dateValues.length === 0) return [];

      const minDate = dateValues[0];
      const maxDate = dateValues[dateValues.length - 1];
      const timeDiff = maxDate.getTime() - minDate.getTime();
      const daysDiff = timeDiff / (1000 * 60 * 60 * 24);

      // Determine appropriate time bucketing based on data range
      let bucketType: 'hour' | 'day' | 'week' | 'month' | 'year';
      if (daysDiff <= 1) bucketType = 'hour';
      else if (daysDiff <= 31) bucketType = 'day';
      else if (daysDiff <= 90) bucketType = 'week';
      else if (daysDiff <= 730) bucketType = 'month';
      else bucketType = 'year';

      // Group dates by the determined bucket type
      const dateBuckets = dateValues.reduce((acc, date) => {
        let key: string;
        switch (bucketType) {
          case 'hour':
            key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:00`;
            break;
          case 'day':
            key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
            break;
          case 'week':
            const weekStart = new Date(date);
            weekStart.setDate(date.getDate() - date.getDay());
            key = `Week of ${weekStart.getFullYear()}-${String(weekStart.getMonth() + 1).padStart(2, '0')}-${String(weekStart.getDate()).padStart(2, '0')}`;
            break;
          case 'month':
            key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            break;
          case 'year':
            key = `${date.getFullYear()}`;
            break;
        }
        acc[key] = (acc[key] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Create buckets from grouped dates
      return Object.entries(dateBuckets)
        .sort(([a], [b]) => a.localeCompare(b))
        .map(([period, count], i) => ({
          id: `auto-${i}`,
          label: period,
          type: 'auto' as const,
          count,
          percentage: totalValues > 0 ? (count / totalValues) * 100 : 0
        }));
    }

    return [];
  }, [columnType, statistics]);

  // Initialize auto buckets when switching to auto mode
  useEffect(() => {
    if (bucketingType === 'auto' && buckets.length === 0) {
      setBuckets(generateAutoBuckets());
    }
  }, [bucketingType, buckets.length, generateAutoBuckets]);

  const handleSave = () => {
    const totalCount = buckets.reduce((sum, bucket) => sum + bucket.count, 0);
    const bucketing: ColumnBucketing = {
      buckets,
      bucketingType,
      totalCount
    };
    onSave(bucketing);
    onClose();
  };

  const handleReset = () => {
    if (bucketingType === 'auto') {
      setBuckets(generateAutoBuckets());
    } else {
      setBuckets([]);
    }
  };

  const addCustomBucket = () => {
    let newBucket: BucketConfig;

    if (columnType === 'numeric') {
      newBucket = {
        id: `custom-${Date.now()}`,
        label: `Custom Range ${buckets.length + 1}`,
        type: 'custom',
        range: { min: 0, max: 100 },
        count: 0,
        percentage: 0
      };
    } else if (columnType === 'datetime') {
      newBucket = {
        id: `custom-${Date.now()}`,
        label: `Custom Period ${buckets.length + 1}`,
        type: 'custom',
        count: 0,
        percentage: 0
      };
    } else {
      newBucket = {
        id: `custom-${Date.now()}`,
        label: `Custom Group ${buckets.length + 1}`,
        type: 'custom',
        values: [],
        count: 0,
        percentage: 0
      };
    }

    setBuckets([...buckets, newBucket]);
  };

  const removeBucket = (bucketId: string) => {
    setBuckets(buckets.filter(b => b.id !== bucketId));
  };

  const updateBucket = (bucketId: string, updates: Partial<BucketConfig>) => {
    setBuckets(buckets.map(b => b.id === bucketId ? { ...b, ...updates } : b));
  };

  // Get available values for custom bucketing
  const availableValues = statistics?.allValues ||
    (statistics?.categoricalStats?.valueCounts.map(v => v.value)) || [];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[85vh] overflow-hidden flex flex-col">
        <DialogHeader className="pb-4 border-b">
          <DialogTitle className="text-xl font-semibold">
            Configure Bucketing for "{column}"
          </DialogTitle>
          <div className="text-sm text-gray-600 mt-1">
            Column Type: <span className="font-medium capitalize">{columnType}</span>
            {statistics?.allValues && (
              <span className="ml-4">
                Total Values: <span className="font-medium">{statistics.allValues.length.toLocaleString()}</span>
              </span>
            )}
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-6 py-4">
          {/* Bucketing Type Selection */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="text-base font-medium">Bucketing Strategy</Label>
                <p className="text-sm text-gray-600">
                  {bucketingType === 'auto'
                    ? 'Automatically generate buckets based on data distribution'
                    : 'Create custom buckets with your own criteria'
                  }
                </p>
              </div>
              <div className="flex items-center gap-3">
                <Select value={bucketingType} onValueChange={(value: 'auto' | 'custom') => setBucketingType(value)}>
                  <SelectTrigger className="w-48">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="auto">
                      <div className="flex items-center gap-2">
                        <span>🤖</span>
                        <span>Auto Bucketing</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="custom">
                      <div className="flex items-center gap-2">
                        <span>⚙️</span>
                        <span>Custom Bucketing</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                <Button variant="outline" size="sm" onClick={handleReset}>
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Reset
                </Button>
              </div>
            </div>
          </div>

          {/* Bucket Configuration */}
          <Tabs defaultValue="buckets" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="buckets" className="flex items-center gap-2">
                <span>📊</span>
                <span>Buckets ({buckets.length})</span>
              </TabsTrigger>
              {columnType === 'categorical' && (
                <TabsTrigger value="values" className="flex items-center gap-2">
                  <span>📋</span>
                  <span>Available Values ({availableValues.length})</span>
                </TabsTrigger>
              )}
              {columnType !== 'categorical' && (
                <TabsTrigger value="preview" className="flex items-center gap-2">
                  <span>👁️</span>
                  <span>Preview</span>
                </TabsTrigger>
              )}
            </TabsList>

            <TabsContent value="buckets" className="space-y-4 mt-4">
              {/* Add Custom Bucket Button */}
              {bucketingType === 'custom' && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-blue-900">Add New Bucket</h4>
                      <p className="text-sm text-blue-700 mt-1">
                        {columnType === 'numeric' && 'Define a custom numeric range for grouping values'}
                        {columnType === 'categorical' && 'Create a custom group by selecting multiple values'}
                        {columnType === 'datetime' && 'Set a custom time period for grouping dates'}
                      </p>
                    </div>
                    <Button onClick={addCustomBucket} className="bg-blue-600 hover:bg-blue-700">
                      <Plus className="h-4 w-4 mr-2" />
                      Add {columnType === 'numeric' ? 'Range' : columnType === 'datetime' ? 'Period' : 'Group'}
                    </Button>
                  </div>
                </div>
              )}

              {/* Buckets List */}
              <div className="space-y-3">
                {buckets.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <div className="text-4xl mb-2">📊</div>
                    <p>No buckets configured yet</p>
                    <p className="text-sm">
                      {bucketingType === 'auto'
                        ? 'Switch to auto bucketing to generate buckets automatically'
                        : 'Click "Add Custom Bucket" to create your first bucket'
                      }
                    </p>
                  </div>
                ) : (
                  <div className="max-h-96 overflow-y-auto space-y-3">
                    {buckets.map((bucket, index) => (
                      <div key={bucket.id} className="relative">
                        <div className="absolute -left-3 top-4 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium">
                          {index + 1}
                        </div>
                        <BucketConfigItem
                          bucket={bucket}
                          columnType={columnType}
                          availableValues={availableValues}
                          onUpdate={(updates) => updateBucket(bucket.id, updates)}
                          onRemove={() => removeBucket(bucket.id)}
                          canRemove={bucketingType === 'custom'}
                        />
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </TabsContent>

            {columnType === 'categorical' && (
              <TabsContent value="values" className="space-y-4 mt-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">Available Values in Column</h4>
                  <p className="text-sm text-gray-600 mb-3">
                    These are all the unique values found in the "{column}" column.
                    You can group multiple values together into custom buckets.
                  </p>
                  <div className="max-h-64 overflow-y-auto">
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                      {availableValues.map((value, i) => (
                        <div key={i} className="p-2 bg-white border rounded text-sm truncate" title={value}>
                          {value}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </TabsContent>
            )}

            {columnType !== 'categorical' && (
              <TabsContent value="preview" className="space-y-4 mt-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">Bucket Preview</h4>
                  <p className="text-sm text-gray-600 mb-3">
                    Preview of how your data will be distributed across the configured buckets.
                  </p>
                  <div className="space-y-2">
                    {buckets.map((bucket, index) => (
                      <div key={bucket.id} className="flex items-center justify-between p-2 bg-white rounded border">
                        <div className="flex items-center gap-2">
                          <span className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs">
                            {index + 1}
                          </span>
                          <span className="font-medium">{bucket.label}</span>
                        </div>
                        <div className="text-sm text-gray-600">
                          {bucket.count.toLocaleString()} values ({bucket.percentage.toFixed(1)}%)
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </TabsContent>
            )}
          </Tabs>
        </div>

        <DialogFooter className="border-t pt-4 bg-gray-50">
          <div className="flex items-center justify-between w-full">
            <div className="text-sm text-gray-600">
              <span className="font-medium">{buckets.length}</span> bucket{buckets.length !== 1 ? 's' : ''} configured
              {buckets.length > 0 && (
                <span className="ml-2">
                  • Total: {buckets.reduce((sum, bucket) => sum + bucket.count, 0).toLocaleString()} values
                </span>
              )}
            </div>
            <div className="flex gap-3">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                disabled={buckets.length === 0}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Save Bucketing Configuration
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

interface BucketConfigItemProps {
  bucket: BucketConfig;
  columnType: 'numeric' | 'categorical' | 'datetime';
  availableValues: string[];
  onUpdate: (updates: Partial<BucketConfig>) => void;
  onRemove: () => void;
  canRemove: boolean;
}

const BucketConfigItem: React.FC<BucketConfigItemProps> = ({
  bucket,
  columnType,
  availableValues,
  onUpdate,
  onRemove,
  canRemove
}) => {
  return (
    <div className="border rounded-lg p-4 space-y-3 bg-gray-50">
      {/* Header with bucket name and remove button */}
      <div className="flex items-center justify-between">
        <div className="flex-1 mr-2">
          <Label className="text-sm font-medium text-gray-700 mb-1 block">
            Bucket Name
          </Label>
          <Input
            value={bucket.label}
            onChange={(e) => onUpdate({ label: e.target.value })}
            placeholder={
              columnType === 'numeric' ? 'e.g., Low Range' :
              columnType === 'datetime' ? 'e.g., Q1 2024' :
              'e.g., High Priority'
            }
            className="w-full"
          />
        </div>
        {canRemove && (
          <Button variant="ghost" size="sm" onClick={onRemove} className="mt-6">
            <Trash2 className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Configuration based on column type */}
      {columnType === 'categorical' && bucket.type === 'custom' && (
        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-700">
            Select Values for this Bucket
          </Label>
          <MultiSelect
            options={availableValues.map(value => ({ label: value, value }))}
            onValueChange={(values) => onUpdate({ values })}
            defaultValue={bucket.values || []}
            placeholder="Choose values to group together..."
            className="w-full"
          />
          <div className="text-xs text-gray-500">
            Selected: {bucket.values?.length || 0} values
          </div>
        </div>
      )}

      {columnType === 'numeric' && bucket.type === 'custom' && (
        <div className="space-y-3">
          <Label className="text-sm font-medium text-gray-700">
            Define Numeric Range
          </Label>
          <div className="grid grid-cols-2 gap-3">
            <div>
              <Label className="text-xs text-gray-600 mb-1 block">
                Minimum Value
              </Label>
              <Input
                type="number"
                step="any"
                value={bucket.range?.min ?? ''}
                onChange={(e) => onUpdate({
                  range: {
                    min: parseFloat(e.target.value) || 0,
                    max: bucket.range?.max || 0
                  }
                })}
                placeholder="0"
                className="w-full"
              />
            </div>
            <div>
              <Label className="text-xs text-gray-600 mb-1 block">
                Maximum Value
              </Label>
              <Input
                type="number"
                step="any"
                value={bucket.range?.max ?? ''}
                onChange={(e) => onUpdate({
                  range: {
                    min: bucket.range?.min || 0,
                    max: parseFloat(e.target.value) || 0
                  }
                })}
                placeholder="100"
                className="w-full"
              />
            </div>
          </div>
          <div className="text-xs text-gray-500">
            Range: {bucket.range?.min ?? 'N/A'} to {bucket.range?.max ?? 'N/A'}
          </div>
        </div>
      )}

      {columnType === 'datetime' && bucket.type === 'custom' && (
        <div className="space-y-3">
          <Label className="text-sm font-medium text-gray-700">
            Define Time Period
          </Label>
          <div className="grid grid-cols-2 gap-3">
            <div>
              <Label className="text-xs text-gray-600 mb-1 block">
                Start Date
              </Label>
              <Input
                type="datetime-local"
                value={bucket.dateRange?.start || ''}
                onChange={(e) => onUpdate({
                  dateRange: {
                    start: e.target.value,
                    end: bucket.dateRange?.end || ''
                  }
                })}
                className="w-full"
              />
            </div>
            <div>
              <Label className="text-xs text-gray-600 mb-1 block">
                End Date
              </Label>
              <Input
                type="datetime-local"
                value={bucket.dateRange?.end || ''}
                onChange={(e) => onUpdate({
                  dateRange: {
                    start: bucket.dateRange?.start || '',
                    end: e.target.value
                  }
                })}
                className="w-full"
              />
            </div>
          </div>
          <div className="text-xs text-gray-500">
            Period: {bucket.dateRange?.start ? new Date(bucket.dateRange.start).toLocaleDateString() : 'Not set'} to {bucket.dateRange?.end ? new Date(bucket.dateRange.end).toLocaleDateString() : 'Not set'}
          </div>
        </div>
      )}

      {/* Statistics display */}
      <div className="pt-2 border-t border-gray-200">
        <div className="flex justify-between items-center text-sm">
          <span className="text-gray-600">Data Count:</span>
          <span className="font-medium text-gray-800">
            {bucket.count.toLocaleString()} ({bucket.percentage.toFixed(1)}%)
          </span>
        </div>
      </div>
    </div>
  );
};
