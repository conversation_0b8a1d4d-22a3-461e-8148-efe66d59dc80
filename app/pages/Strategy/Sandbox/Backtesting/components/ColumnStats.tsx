import React, { useState } from 'react';
import { ColumnStatistics, ColumnBucketing } from '@/app/types';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { X, ChevronDown, Loader2, Edit3, Settings } from 'lucide-react';
import { BucketingEditor } from './BucketingEditor';

interface ColumnStatsProps {
  tableId: string;
  column: string;
  description?: string;
  stats?: ColumnStatistics;
  isLoading?: boolean;
  onRemove?: () => void;
  onBucketingUpdate?: (bucketing: ColumnBucketing) => void;
  className?: string;
}

interface NumericBucket {
  start: number | null;
  end: number | null;
  count: number;
  formattedStart?: string;
  formattedEnd?: string;
}

export const ColumnStats: React.FC<ColumnStatsProps> = ({
  tableId,
  column,
  description,
  stats,
  isLoading,
  onRemove,
  onBucketingUpdate,
  className
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isBucketingEditorOpen, setIsBucketingEditorOpen] = useState(false);

  // Helper function to format bucket boundary values
  const formatBucketValue = (value: number, roundingUnit: number): string => {
    // For very large numbers (≥1000), don't show decimals
    if (roundingUnit >= 100) {
      return value.toLocaleString();
    }

    // For medium numbers (≥10), show decimals only if they're not zero
    if (roundingUnit >= 5) {
      return value % 1 === 0 ? value.toString() : value.toFixed(1);
    }

    // For small numbers, show up to 2 decimal places if they exist
    return value % 1 === 0 ? value.toString() : value.toFixed(2);
  };

  // Helper function to create buckets for numeric data
  const createNumericBuckets = (min: number, max: number): NumericBucket[] => {
    const numBuckets = 5;
    const range = max - min;

    // Determine the appropriate rounding unit based on the range
    let roundingUnit: number;
    if (range >= 10000) {
      roundingUnit = 1000;
    } else if (range >= 1000) {
      roundingUnit = 100;
    } else if (range >= 100) {
      roundingUnit = 10;
    } else if (range >= 10) {
      roundingUnit = 5;
    } else {
      roundingUnit = 1;
    }

    // Round min and max to the nearest unit
    const roundedMin = Math.floor(min / roundingUnit) * roundingUnit;
    const roundedMax = Math.ceil(max / roundingUnit) * roundingUnit;

    // Calculate bucket size based on rounded values
    const bucketSize = (roundedMax - roundedMin) / numBuckets;

    // Create buckets
    const buckets: NumericBucket[] = Array(numBuckets).fill(0).map((_, i) => ({
      start: roundedMin + (i * bucketSize),
      end: roundedMin + ((i + 1) * bucketSize),
      count: 0,
      // Add formatted values for display
      formattedStart: formatBucketValue(roundedMin + (i * bucketSize), roundingUnit),
      formattedEnd: formatBucketValue(roundedMin + ((i + 1) * bucketSize), roundingUnit)
    }));

    return buckets;
  };

  // Calculate total count for categorical data
  const getTotalCount = (stats: ColumnStatistics): number => {
    if (stats.type === 'categorical' && stats.categoricalStats) {
      return stats.categoricalStats.valueCounts.reduce((sum, { count }) => sum + count, 0);
    }
    return 0;
  };

  return (
    <div className={cn("border rounded-lg bg-white", className)}>
      {/* Header - Always visible */}
      <div
        className="flex items-center justify-between p-2 cursor-pointer hover:bg-gray-50"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-2">
          <ChevronDown
            className={cn(
              "h-4 w-4 text-gray-500 transition-transform duration-200",
              isExpanded ? "transform rotate-0" : "transform -rotate-90"
            )}
          />
          <div className="text-blue-700 font-mono text-sm">{column}</div>
          {stats && (
            <span className="px-1.5 py-0.5 bg-gray-100 rounded text-xs font-medium">
              {stats.type}
            </span>
          )}
          {stats?.bucketing && (
            <span className="px-1.5 py-0.5 bg-blue-100 text-blue-700 rounded text-xs font-medium">
              {stats.bucketing.buckets.length} buckets
            </span>
          )}
        </div>
        <div className="flex items-center gap-1">
          {stats && (
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 shrink-0"
              onClick={(e) => {
                e.stopPropagation();
                setIsBucketingEditorOpen(true);
              }}
              title="Edit bucketing"
            >
              <Settings className="h-4 w-4" />
            </Button>
          )}
          {onRemove && (
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 shrink-0 -mr-1"
              onClick={(e) => {
                e.stopPropagation();
                onRemove();
              }}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Collapsible Content */}
      <div
        className={cn(
          "transition-all duration-200 ease-in-out overflow-hidden",
          isExpanded ? "max-h-[500px]" : "max-h-0"
        )}
      >
        <div className="p-2 pt-0">
          {description && (
            <div className="text-sm text-gray-500 mb-1.5">{description}</div>
          )}

          {/* Loading State or Stats */}
          {isLoading ? (
            <div className="flex items-center text-sm text-gray-500">
              <Loader2 className="h-3 w-3 animate-spin mr-1.5" />
              Loading statistics...
            </div>
          ) : stats && (
            <div className="space-y-1.5">
              {/* Bucketing Display */}
              <div className="space-y-0.5">
                <div className="flex items-center justify-between mb-2">
                  <div className="text-sm font-medium text-gray-700">
                    {stats.bucketing ?
                      `${stats.bucketing.bucketingType === 'auto' ? 'Auto' : 'Custom'} Bucketing` :
                      'Distribution'
                    }
                  </div>
                  {stats.bucketing && (
                    <div className="text-xs text-gray-500">
                      Total: {stats.bucketing.totalCount}
                    </div>
                  )}
                </div>

                <div className="max-h-32 overflow-y-auto space-y-0.5 pr-2">
                  {stats.bucketing ? (
                    // Display bucketing data
                    <>
                      {/* Null entry first if there's null percentage */}
                      {stats.nullPercentage > 0 && (
                        <div className="flex items-center gap-1 text-sm">
                          <div className="flex-1 truncate">null</div>
                          <div className="text-gray-500 whitespace-nowrap text-right min-w-[80px] pr-1">
                            {Math.round(stats.nullPercentage * (stats.bucketing.totalCount || 1))}
                            <span className="text-gray-400 ml-1">
                              ({(stats.nullPercentage * 100).toFixed(1)}%)
                            </span>
                          </div>
                        </div>
                      )}

                      {stats.bucketing.buckets.map((bucket) => (
                        <div key={bucket.id} className="flex items-center gap-1 text-sm">
                          <div className="flex-1 truncate">{bucket.label}</div>
                          <div className="text-gray-500 whitespace-nowrap text-right min-w-[80px] pr-1">
                            {bucket.count}
                            <span className="text-gray-400 ml-1">
                              ({bucket.percentage.toFixed(1)}%)
                            </span>
                          </div>
                        </div>
                      ))}

                      {/* Total row */}
                      <div className="flex items-center gap-1 text-sm border-t pt-0.5 mt-0.5">
                        <div className="flex-1 truncate font-medium">Total</div>
                        <div className="text-gray-700 whitespace-nowrap text-right min-w-[80px] pr-1 font-medium">
                          {stats.bucketing.totalCount}
                          <span className="text-gray-500 ml-1">(100%)</span>
                        </div>
                      </div>
                    </>
                  ) : (
                    // Fallback to old display if no bucketing
                    <>
                      {/* For categorical data */}
                      {stats.type === 'categorical' && stats.categoricalStats && (
                        <>
                          {stats.categoricalStats.valueCounts.map(({ value, count, percentage }) => (
                            <div key={value} className="flex items-center gap-1 text-sm">
                              <div className="flex-1 truncate">{value}</div>
                              <div className="text-gray-500 whitespace-nowrap text-right min-w-[80px] pr-1">
                                {count}
                                <span className="text-gray-400 ml-1">
                                  ({(percentage * 100).toFixed(1)}%)
                                </span>
                              </div>
                            </div>
                          ))}
                        </>
                      )}

                      {/* For numeric data */}
                      {stats.type === 'numeric' && stats.numericStats && (
                        <>
                          {createNumericBuckets(
                            stats.numericStats.min,
                            stats.numericStats.max
                          ).map((bucket, i) => (
                            <div key={i} className="flex items-center gap-1 text-sm">
                              <div className="flex-1 truncate">
                                {bucket.start === null ? 'null' :
                                  `${bucket.formattedStart} - ${bucket.formattedEnd}`}
                              </div>
                              <div className="text-gray-500 whitespace-nowrap text-right min-w-[80px] pr-1">
                                {bucket.count}
                                <span className="text-gray-400 ml-1">
                                  ({((bucket.count / (getTotalCount(stats) || 1)) * 100).toFixed(1)}%)
                                </span>
                              </div>
                            </div>
                          ))}
                        </>
                      )}
                    </>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Bucketing Editor */}
      {stats && (
        <BucketingEditor
          isOpen={isBucketingEditorOpen}
          onClose={() => setIsBucketingEditorOpen(false)}
          tableId={tableId}
          column={column}
          columnType={stats.type}
          statistics={stats}
          currentBucketing={stats.bucketing}
          onSave={(bucketing) => {
            if (onBucketingUpdate) {
              onBucketingUpdate(bucketing);
            }
            setIsBucketingEditorOpen(false);
          }}
        />
      )}
    </div>
  );
};