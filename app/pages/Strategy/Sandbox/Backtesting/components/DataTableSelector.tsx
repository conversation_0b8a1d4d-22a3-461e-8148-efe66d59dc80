import React, { useState, useCallback } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertCircle } from 'lucide-react';
import { TableConfig } from '@/app/store/backtesting/backtestingStore';

export interface DataTableSelectorProps {
  tables: TableConfig[];
  selectedTable: string;
  onTableSelect: (tableId: string) => void;
}

export const DataTableSelector: React.FC<DataTableSelectorProps> = ({
  tables,
  selectedTable,
  onTableSelect
}) => {
  const selectedTableData = tables.find(t => t.id === selectedTable);
  const [internalValue, setInternalValue] = useState(selectedTable);

  // Update internal value when prop changes
  React.useEffect(() => {
    setInternalValue(selectedTable);
  }, [selectedTable]);

  const handleValueChange = useCallback((value: string) => {
    // If the new value is the same as current, unselect it
    const newValue = value === internalValue ? "" : value;
    setInternalValue(newValue);
    onTableSelect(newValue);
  }, [internalValue, onTableSelect]);

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 gap-4">
        {tables.map((table) => (
          <div
            key={table.id}
            className={`p-4 border rounded-lg cursor-pointer ${
              selectedTable === table.id ? 'border-primary' : 'border-border'
            }`}
            onClick={() => onTableSelect(table.id)}
          >
            <h3 className="font-medium">{table.name}</h3>
            <p className="text-sm text-muted-foreground">{table.description}</p>
          </div>
        ))}
      </div>
    </div>
  );
}; 