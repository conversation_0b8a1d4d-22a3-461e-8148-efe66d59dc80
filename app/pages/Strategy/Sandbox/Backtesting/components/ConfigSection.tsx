import React, { useState, useCallback } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ColumnConfig } from '../../SampleSandboxData';
import { AlertCircle, Wand2 } from 'lucide-react';
import { z } from 'zod';
import { SectionContainer } from './SectionContainer';
import { Button } from '@/components/ui/button';
import { TableConfig } from '@/app/store/backtesting/backtestingStore';
import { useBacktestingStore } from '@/app/store/backtesting/backtestingStore';

interface ConfigSectionProps {
  selectedTable: string;
  columnConfig: ColumnConfig;
  onConfigUpdate: (config: ColumnConfig) => void;
  onAutoDetect?: (fn: () => void) => void;
}

export const ConfigSection: React.FC<ConfigSectionProps> = ({
  selectedTable,
  columnConfig,
  onConfigUpdate,
  onAutoDetect
}) => {
  const { tables, getColumnConfig } = useBacktestingStore();
  const selectedTableData = tables.find(t => t.id === selectedTable);
  const columns = selectedTableData ? Object.keys(selectedTableData.schema.shape) : [];
  const [internalValues, setInternalValues] = useState<ColumnConfig>(columnConfig);

  // Update internal values when props change
  React.useEffect(() => {
    setInternalValues(columnConfig);
  }, [columnConfig]);

  const requiredFields = ['rowUniqueId', 'datetime', 'fraudLabel', 'amount'] as const;
  const optionalFields = ['applicationId', 'accountId', 'transactionId'] as const;

  const handleAutoDetect = useCallback(() => {
    if (!selectedTable) return;

    // Get column config for the selected table
    const newConfig = getColumnConfig(selectedTable);
    setInternalValues(newConfig);
    onConfigUpdate(newConfig);
  }, [selectedTable, getColumnConfig, onConfigUpdate]);

  // Make handleAutoDetect available to parent
  React.useEffect(() => {
    if (onAutoDetect) {
      onAutoDetect(handleAutoDetect);
    }
  }, [onAutoDetect, handleAutoDetect]);

  const getColumnType = (column: string) => {
    if (!selectedTableData) return '';
    const colDef = (selectedTableData.schema.shape as any)[column];
    if (!colDef) return '';
    return (colDef as z.ZodTypeAny)._def.typeName.replace('Zod', '');
  };

  const getExpectedColumnType = (field: keyof ColumnConfig) => {
    switch (field) {
      case 'datetime': return 'ZodDate';
      case 'amount': return 'ZodNumber';
      case 'fraudLabel': return 'ZodBoolean';
      default: return 'ZodString';
    }
  };

  const renderColumnSelector = (
    field: keyof ColumnConfig,
    label: string,
    isRequired: boolean
  ) => {
    const handleValueChange = useCallback((value: string) => {
      const newValue = value === internalValues[field] ? "" : value;
      const newConfig = { ...internalValues, [field]: newValue };
      setInternalValues(newConfig);
      onConfigUpdate(newConfig);
    }, [field, internalValues, onConfigUpdate]);

    const expectedType = getExpectedColumnType(field);
    const filteredColumns = columns.filter(column => {
      const columnType = getColumnType(column);
      return columnType === expectedType.replace('Zod', '');
    });

    return (
      <div className="space-y-2">
        <label className="text-sm font-medium">
          {label}
          {isRequired && <span className="text-destructive ml-1">*</span>}
        </label>
        <Select 
          value={internalValues[field] || ""}
          onValueChange={handleValueChange}
        >
          <SelectTrigger className="w-full">
            <SelectValue>
              <span className="text-left">
                {internalValues[field] ? (
                  <div className="flex items-center justify-between">
                    <span>{internalValues[field]}</span>
                    <span className="text-sm text-muted-foreground ml-2">
                      ({getColumnType(internalValues[field])})
                    </span>
                  </div>
                ) : (
                  `Select ${label.toLowerCase()} column`
                )}
              </span>
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            {filteredColumns.map(column => (
              <SelectItem key={column} value={column}>
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span>{column}</span>
                    <span className="text-sm text-muted-foreground ml-2">
                      ({getColumnType(column)})
                    </span>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {(selectedTableData?.schema.shape as any)[column]?._def.description || ''}
                  </div>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-8 relative">
        {/* Vertical Separator */}
        <div className="absolute left-1/2 top-0 bottom-0 w-px bg-border" />

        {/* Required Fields Column */}
        <div className="pr-8">
          <SectionContainer title="Required Fields">
            <div className="space-y-4">
              {requiredFields.map(field => (
                <div key={field}>
                  {renderColumnSelector(
                    field,
                    field.charAt(0).toUpperCase() + field.slice(1).replace(/([A-Z])/g, ' $1'),
                    true
                  )}
                </div>
              ))}
            </div>
          </SectionContainer>
        </div>

        {/* Optional Fields Column */}
        <div className="pl-8">
          <SectionContainer title="Optional Fields">
            <div className="space-y-4">
              {optionalFields.map(field => (
                <div key={field}>
                  {renderColumnSelector(
                    field,
                    field.charAt(0).toUpperCase() + field.slice(1).replace(/([A-Z])/g, ' $1'),
                    false
                  )}
                </div>
              ))}
            </div>
          </SectionContainer>
        </div>
      </div>
    </div>
  );
}; 