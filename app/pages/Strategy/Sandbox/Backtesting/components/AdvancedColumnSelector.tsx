import React, { useState, useCallback } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { ColumnStatistics, BucketConfig, ColumnBucketing } from '@/app/types';
import { ColumnStats } from './ColumnStats';
import { useBacktestingStore } from '@/app/store/backtesting/backtestingStore';

interface ColumnInfo {
  column: string;
  description?: string;
  statistics?: ColumnStatistics;
}

interface AdvancedColumnSelectorProps {
  selectedTable: string;
  selectedColumns: ColumnInfo[];
  onColumnsChange: (columns: ColumnInfo[]) => void;
  onColumnStatsRequest?: (column: string) => Promise<ColumnStatistics>;
}

// Helper function to generate default bucketing
const generateDefaultBucketing = (
  columnType: 'numeric' | 'categorical' | 'datetime',
  stats?: ColumnStatistics
): ColumnBucketing => {
  const buckets: BucketConfig[] = [];

  if (columnType === 'numeric' && stats?.numericStats) {
    const { min, max } = stats.numericStats;
    const numBuckets = 5;
    const range = max - min;

    // Determine rounding unit
    let roundingUnit: number;
    if (range >= 10000) roundingUnit = 1000;
    else if (range >= 1000) roundingUnit = 100;
    else if (range >= 100) roundingUnit = 10;
    else if (range >= 10) roundingUnit = 5;
    else roundingUnit = 1;

    const roundedMin = Math.floor(min / roundingUnit) * roundingUnit;
    const roundedMax = Math.ceil(max / roundingUnit) * roundingUnit;
    const bucketSize = (roundedMax - roundedMin) / numBuckets;

    for (let i = 0; i < numBuckets; i++) {
      buckets.push({
        id: `auto-${i}`,
        label: `${(roundedMin + i * bucketSize).toFixed(2)} - ${(roundedMin + (i + 1) * bucketSize).toFixed(2)}`,
        type: 'auto',
        range: {
          min: roundedMin + i * bucketSize,
          max: roundedMin + (i + 1) * bucketSize
        },
        count: 0,
        percentage: 0
      });
    }
  } else if (columnType === 'categorical' && stats?.categoricalStats) {
    stats.categoricalStats.valueCounts.forEach((item, i) => {
      buckets.push({
        id: `auto-${i}`,
        label: item.value,
        type: 'auto',
        values: [item.value],
        count: item.count,
        percentage: item.percentage * 100
      });
    });
  } else if (columnType === 'datetime') {
    // Default time-based buckets
    ['Daily', 'Weekly', 'Monthly'].forEach((period) => {
      buckets.push({
        id: `auto-${period.toLowerCase()}`,
        label: period,
        type: 'auto',
        count: 0,
        percentage: 0
      });
    });
  }

  return {
    buckets,
    bucketingType: 'auto',
    totalCount: buckets.reduce((sum, bucket) => sum + bucket.count, 0)
  };
};

export const AdvancedColumnSelector: React.FC<AdvancedColumnSelectorProps> = ({
  selectedTable,
  selectedColumns,
  onColumnsChange,
  onColumnStatsRequest
}) => {
  const [loadingStats, setLoadingStats] = useState<Record<string, boolean>>({});
  const [selectedColumn, setSelectedColumn] = useState<string>('');
  const { tables } = useBacktestingStore();
  const selectedTableData = tables.find(t => t.id === selectedTable);
  const availableColumns = selectedTableData
    ? Object.keys(selectedTableData.schema.shape).filter(col =>
        !selectedColumns.some(selected => selected.column === col)
      )
    : [];

  const handleColumnSelect = useCallback((column: string) => {
    setSelectedColumn(column);
  }, []);

  const handleAddColumn = useCallback(async () => {
    if (!selectedColumn || !selectedTableData) return;

    const description = (selectedTableData.schema.shape as any)[selectedColumn]?._def.description;
    const columnType = (selectedTableData.schema.shape as any)[selectedColumn]?._def.typeName;

    setLoadingStats(prev => ({ ...prev, [selectedColumn]: true }));

    try {
      let stats: ColumnStatistics | undefined;

      // Fetch column statistics if available
      if (onColumnStatsRequest) {
        stats = await onColumnStatsRequest(selectedColumn);
      }

      // Determine column type for bucketing
      let detectedType: 'numeric' | 'categorical' | 'datetime' = 'categorical';
      if (columnType === 'ZodNumber') {
        detectedType = 'numeric';
      } else if (columnType === 'ZodDate') {
        detectedType = 'datetime';
      }

      // Fetch all values for bucketing
      const { fetchFieldValues } = useBacktestingStore.getState();
      let allValues: string[] = [];

      try {
        if (detectedType === 'categorical') {
          const response = await fetchFieldValues(selectedTable, selectedColumn, 'all');
          if ('distinct_values' in response) {
            allValues = response.distinct_values;
          }
        } else if (detectedType === 'numeric' || detectedType === 'datetime') {
          const response = await fetchFieldValues(selectedTable, selectedColumn, 'min_max');
          if ('min_value' in response && 'max_value' in response) {
            // For numeric/datetime, we'll use min/max for auto-bucketing
            allValues = [response.min_value, response.max_value];
          }
        }
      } catch (error) {
        console.error('Failed to fetch field values:', error);
      }

      // Create enhanced statistics with bucketing data
      const enhancedStats: ColumnStatistics = {
        ...stats,
        type: detectedType,
        allValues,
        bucketing: generateDefaultBucketing(detectedType, stats)
      } as ColumnStatistics;

      const newColumn: ColumnInfo = {
        column: selectedColumn,
        description,
        statistics: enhancedStats
      };

      const updatedColumns = [...selectedColumns, newColumn];
      onColumnsChange(updatedColumns);
      setSelectedColumn(''); // Reset selection after adding

    } catch (error) {
      console.error('Failed to add column:', error);
    } finally {
      setLoadingStats(prev => ({ ...prev, [selectedColumn]: false }));
    }
  }, [selectedColumn, selectedColumns, onColumnsChange, onColumnStatsRequest, selectedTableData, selectedTable]);

  const handleRemoveColumn = useCallback((column: string) => {
    onColumnsChange(selectedColumns.filter(col => col.column !== column));
  }, [selectedColumns, onColumnsChange]);

  const handleBucketingUpdate = useCallback((column: string, bucketing: ColumnBucketing) => {
    const updatedColumns = selectedColumns.map(col =>
      col.column === column
        ? {
            ...col,
            statistics: col.statistics ? { ...col.statistics, bucketing } : undefined
          }
        : col
    );
    onColumnsChange(updatedColumns);
  }, [selectedColumns, onColumnsChange]);

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Select
          value={selectedColumn}
          onValueChange={handleColumnSelect}
          disabled={availableColumns.length === 0}
        >
          <SelectTrigger className="w-full">
            <SelectValue>
              {selectedColumn ? (
                <div className="flex items-center">
                  <span className="truncate">{selectedColumn}</span>
                  {selectedTableData?.schema.shape &&
                   selectedColumn in selectedTableData.schema.shape && (
                    <span className="ml-2 text-sm text-muted-foreground truncate">
                      ({(selectedTableData.schema.shape as any)[selectedColumn]?._def.description || ''})
                    </span>
                  )}
                </div>
              ) : (
                "Select a column to analyze"
              )}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            {availableColumns.map(column => (
              <SelectItem key={column} value={column}>
                <div className="space-y-1">
                  <div className="font-medium">{column}</div>
                  <div className="text-sm text-muted-foreground">
                    {(selectedTableData?.schema.shape as any)[column]?._def.description || ''}
                  </div>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Button
          variant="outline"
          size="icon"
          onClick={handleAddColumn}
          disabled={!selectedColumn}
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>

      <div className="space-y-2">
        {selectedColumns.map(({ column, description, statistics }) => (
          <ColumnStats
            key={column}
            column={column}
            description={description}
            stats={statistics}
            isLoading={loadingStats[column]}
            onRemove={() => handleRemoveColumn(column)}
            onBucketingUpdate={(bucketing) => handleBucketingUpdate(column, bucketing)}
          />
        ))}
      </div>
    </div>
  );
};