import { MetricEquation, BaseMetricCondition, ColumnStatistics } from '@/app/types';
import { TimeframeFilter, PopulationFilter, ColumnConfig } from '../SampleSandboxData';

export interface BacktestingAnalysisSettings {
  analysisId: string;
  analysisType: 'rule' | 'metric';
  timestamp: string;
  dataTable: {
    selectedTable: string;
    columnConfig: ColumnConfig;
    tableDescription?: string;
  };
  configuration: {
    columnMappings: {
      rowUniqueId: { mappedTo: string; description: string };
      datetime: { mappedTo: string; description: string };
      fraudLabel: { mappedTo: string; description: string };
      amount: { mappedTo: string; description: string };
      applicationId?: { mappedTo: string; description: string };
      accountId?: { mappedTo: string; description: string };
      transactionId?: { mappedTo: string; description: string };
    };
    autoDetected: boolean;
  };
  filters: {
    timeframeFilter: TimeframeFilter;
    populationFilters: PopulationFilter[];
    timeframeAutoDetected: boolean;
  };
  analysis: {
    type: 'rule' | 'metric';
    rule?: {
      equation: MetricEquation;
      operator: 'AND' | 'OR';
      conditions: Array<{
        metric_name: string;
        operation: string;
        value: string | number | boolean;
        type: 'string' | 'boolean' | 'numeric';
      }>;
    };
    metric?: {
      selectedColumns: Array<{
        column: string;
        description?: string;
      }>;
    };
  };
}

export type TableRow = {
  [key: string]: string | number | boolean;
}; 