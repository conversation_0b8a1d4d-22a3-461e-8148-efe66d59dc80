import { FC, useEffect } from 'react';
import { useRulesRepoStore } from '@/app/store/ruleRepo/RulesRepoStore';
import { motion } from 'framer-motion';
import { Activity } from 'lucide-react';
import { TabSectionHeading } from '@/components/custom/TabSectionHeading';
import { MetricsList } from './components';

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 }
};

export const MetricsTab: FC = () => {
  const { metrics, getMetrics } = useRulesRepoStore();

  useEffect(() => {
    getMetrics();
  }, [getMetrics]);

  return (
    <motion.div
      className="space-y-6 p-4"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div variants={itemVariants}>
        <TabSectionHeading icon={Activity} iconColorClass="text-blue-500">
          Standard Metrics
        </TabSectionHeading>
        <MetricsList metrics={metrics.data} />
      </motion.div>
    </motion.div>
  );
};

export default MetricsTab;