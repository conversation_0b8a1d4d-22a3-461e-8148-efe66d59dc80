import { FC, useState } from 'react';
import { LlmMetric } from '@/app/types';
import { CustomCard } from '@/components/custom/CustomCard';
import { motion } from 'framer-motion';
import { Brain, Edit2 } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useRulesRepoStore } from '@/app/store/ruleRepo/RulesRepoStore';

interface LlmMetricCardProps {
  metric: LlmMetric;
}

export const LlmMetricCard: FC<LlmMetricCardProps> = ({ metric }) => {
  const { updateLlmMetric, getLlmMetrics } = useRulesRepoStore();
  const [isEditing, setIsEditing] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  const handleSeverityChange = async (severity: "Low" | "Medium" | "High" | "Critical") => {
    try {
      setIsUpdating(true);
      await updateLlmMetric(
        "<EMAIL>", // TODO: Get actual user email
        metric.metric_name,
        severity,
        metric.active_status
      );
      await getLlmMetrics(); // Refresh the metrics after update
    } catch (error) {
      console.error('Failed to update metric severity:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleStatusChange = async (status: boolean) => {
    try {
      setIsUpdating(true);
      await updateLlmMetric(
        "<EMAIL>", // TODO: Get actual user email
        metric.metric_name,
        metric.severity,
        status
      );
      await getLlmMetrics(); // Refresh the metrics after update
    } catch (error) {
      console.error('Failed to update metric status:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <CustomCard className={`p-4 ${isUpdating ? 'opacity-70' : ''}`}>
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3">
            <Brain className="h-5 w-5 text-purple-500 mt-1" />
            <div>
              <h3 className="font-medium">{metric.metric_name}</h3>
              <p className="text-sm text-gray-600 mt-1">
                {metric.metric_description}
              </p>
              <div className="flex items-center gap-4 mt-2">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-500">Status:</span>
                  <Switch
                    checked={metric.active_status}
                    onCheckedChange={handleStatusChange}
                    disabled={isUpdating}
                  />
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-500">Severity:</span>
                  <Select
                    value={metric.severity}
                    onValueChange={(value) => handleSeverityChange(value as any)}
                    disabled={isUpdating}
                  >
                    <SelectTrigger className="w-[100px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Low">Low</SelectItem>
                      <SelectItem value="Medium">Medium</SelectItem>
                      <SelectItem value="High">High</SelectItem>
                      <SelectItem value="Critical">Critical</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </div>
          <button
            onClick={() => setIsEditing(!isEditing)}
            className="text-gray-500 hover:text-gray-700"
            disabled={isUpdating}
          >
            <Edit2 className="h-4 w-4" />
          </button>
        </div>
      </CustomCard>
    </motion.div>
  );
}; 