import { FC } from 'react';
import { Metric } from '@/app/types';
import { CustomCard } from '@/components/custom/CustomCard';
import { motion } from 'framer-motion';
import { Activity } from 'lucide-react';

interface MetricCardProps {
  metric: Metric;
}

export const MetricCard: FC<MetricCardProps> = ({ metric }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <CustomCard className="p-4">
        <div className="flex items-start gap-3">
          <Activity className="h-5 w-5 text-blue-500 mt-1" />
          <div>
            <h3 className="font-medium">{metric.metric_name}</h3>
            <p className="text-sm text-gray-600 mt-1">
              {metric.metric_description}
            </p>
          </div>
        </div>
      </CustomCard>
    </motion.div>
  );
}; 