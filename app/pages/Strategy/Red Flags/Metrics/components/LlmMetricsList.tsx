import { FC } from 'react';
import { LlmMetric } from '@/app/types';
import { motion } from 'framer-motion';
import { LlmMetricCard } from './LlmMetricCard';

interface LlmMetricsListProps {
  metrics: LlmMetric[];
}

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.05
    }
  }
};

export const LlmMetricsList: FC<LlmMetricsListProps> = ({ metrics }) => {
  return (
    <motion.div
      className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {metrics.map((metric) => (
        <LlmMetricCard key={metric.metric_name} metric={metric} />
      ))}
    </motion.div>
  );
}; 