import { FC } from 'react';
import { Metric } from '@/app/types';
import { motion } from 'framer-motion';
import { MetricCard } from './MetricCard';

interface MetricsListProps {
  metrics: Metric[];
}

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.05
    }
  }
};

export const MetricsList: FC<MetricsListProps> = ({ metrics }) => {
  return (
    <motion.div
      className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {metrics.map((metric) => (
        <MetricCard key={metric.metric_name} metric={metric} />
      ))}
    </motion.div>
  );
}; 