import { Workspace } from '@/app/layout/Workspace/Workspace';
import { RulesRepositoryTab } from './Rules/RulesRepositoryTab';
import MetricsTab from './Metrics/MetricsTab';
import AnomalyDetectionTab from './Anomaly/anomalyDetectionTab';
export interface Rule {
  id: string;
  name: string;
  description: string;
  dateCreated: string;
  dateModified: string;
  riskLevel: 'low' | 'medium' | 'high' | 'severe';
  category: string;
  conditions: string[];
  queue: string;
  triggerCount: number;
  author: string;
}

export interface RulePerformance {
  ruleId: string;
  ruleName: string;
  category: string;
  metrics: {
    [period: string]: {
      triggers: number;
      fraudRate: number;
    };
  };
}

export interface SimulationResult {
  totalAccounts: number;
  fraudAccounts: number;
  precision: number;
  recall: number;
  monthlyStats: {
    month: string;
    triggers: number;
    fraudCaught: number;
    precision: number;
  }[];
}

export interface FeaturePerformance {
  bucket: string;
  accountCount: number;
  fraudCount: number;
  fraudRate: number;
}

export default function RedFlagsPage() {
  const tabs = [
    {
      id: 'conditions',
      label: 'Rules Repository', 
      content: <RulesRepositoryTab />
    },
    {
      id: 'metrics',
      label: 'Metrics',
      content: <MetricsTab />
    },
    {
      id: 'anomaly',
      label: 'Anomaly Detection',
      content: <AnomalyDetectionTab />
    }
  ];

  return <Workspace tabs={tabs} />;
} 