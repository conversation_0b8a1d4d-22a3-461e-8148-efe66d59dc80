import { FC } from 'react';
import { Rule } from '@/app/types';
import { motion } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, FileText } from 'lucide-react';

interface RuleCardProps {
  rule: Rule;
  onClick?: () => void;
}

export const RuleCard: FC<RuleCardProps> = ({ rule, onClick }) => {
  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical': return 'bg-purple-100 text-purple-800 border-purple-300';
      case 'high': return 'bg-red-100 text-red-800 border-red-300';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      case 'low': return 'bg-green-100 text-green-800 border-green-300';
      default: return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'transaction': return 'bg-blue-50 text-blue-700 border-blue-200';
      case 'account': return 'bg-emerald-50 text-emerald-700 border-emerald-200';
      case 'device': return 'bg-indigo-50 text-indigo-700 border-indigo-200';
      case 'location': return 'bg-amber-50 text-amber-700 border-amber-200';
      case 'merchant': return 'bg-violet-50 text-violet-700 border-violet-200';
      case 'velocity': return 'bg-pink-50 text-pink-700 border-pink-200';
      default: return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };

  return (
    <div 
      className="p-5 cursor-pointer hover:shadow-md transition-all duration-200 hover:bg-accent/5 border-l-4 border border-gray-300 rounded-lg bg-white"
      style={{ 
        borderLeftColor: rule.rule_severity?.toLowerCase() === 'high' 
          ? '#f87171' 
          : rule.rule_severity?.toLowerCase() === 'critical' 
            ? '#8b5cf6' 
            : rule.rule_severity?.toLowerCase() === 'medium' 
              ? '#facc15' 
              : '#4ade80',
        margin: '2px 0'
      }}
      onClick={onClick}
    >
      <div className="grid grid-cols-2 gap-4">
        {/* Column 1 - Left aligned content */}
        <div className="space-y-2">
          {/* Row 1 - Heading */}
          <div className="flex items-center gap-2">
            <FileText className="h-4 w-4 text-blue-500 flex-shrink-0" />
            <h3 className="font-medium truncate">{rule.rule_name}</h3>
          </div>
          {/* Row 2 - Description */}
          <p className="text-sm text-gray-600 truncate">{rule.rule_description}</p>
        </div>

        {/* Column 2 - Right aligned content */}
        <div className="space-y-2 flex flex-col items-end">
          {/* Row 1 - Tags */}
          <div className="flex gap-2 flex-wrap justify-end">
            <Badge variant="outline" className={`${getSeverityColor(rule.rule_severity || '')} truncate max-w-[120px]`}>
              {rule.rule_severity}
            </Badge>
            <Badge variant="outline" className={`${getTypeColor(rule.rule_type || '')} truncate max-w-[120px]`}>
              {rule.rule_type}
            </Badge>
          </div>
          {/* Row 2 - Rule Code */}
          <span className="text-xs text-gray-500 truncate max-w-[200px]">Rule Code: {rule.rule_code}</span>
        </div>
      </div>

      {/* Status indicator - spans full width */}
      {!rule.rule_status && (
        <div className="mt-3 flex items-center gap-1 text-red-500">
          <AlertCircle className="h-3 w-3" />
          <span className="text-xs truncate">Inactive</span>
        </div>
      )}
    </div>
  );
}; 