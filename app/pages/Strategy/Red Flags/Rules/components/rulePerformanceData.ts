export interface RulePerformanceData {
  name: string;
  triggers: number;
  hitrate: number;
  [key: string]: string | number;
}

export interface RuleStatisticsData {
  totalTriggers: number;
  averageHitrate: number;
  fraudCases: number;
  highRiskCases: number;
  efficiency: number;
}

// 12 months of performance data
export const getPerformanceData = (ruleId: string): RulePerformanceData[] => {
  // In a real application, this would fetch data from an API based on the ruleId
  return [
    { name: 'Jan', triggers: 124, hitrate: 3.2 },
    { name: 'Feb', triggers: 165, hitrate: 4.1 },
    { name: 'Mar', triggers: 142, hitrate: 3.7 },
    { name: 'Apr', triggers: 189, hitrate: 4.8 },
    { name: 'May', triggers: 213, hitrate: 5.2 },
    { name: 'Jun', triggers: 256, hitrate: 6.1 },
    { name: 'Jul', triggers: 278, hitrate: 6.5 },
    { name: 'Aug', triggers: 245, hitrate: 5.9 },
    { name: 'Sep', triggers: 198, hitrate: 4.9 },
    { name: 'Oct', triggers: 187, hitrate: 4.5 },
    { name: 'Nov', triggers: 225, hitrate: 5.3 },
    { name: 'Dec', triggers: 268, hitrate: 6.2 }
  ];
};

// Statistics data for the rule
export const getRuleStatistics = (ruleId: string): RuleStatisticsData => {
  // In a real application, this would calculate or fetch statistics based on the ruleId
  return {
    totalTriggers: 2490,
    averageHitrate: 5.03,
    fraudCases: 125,
    highRiskCases: 342,
    efficiency: 78.6
  };
}; 