import { FC, ReactNode } from 'react';
import { Rule } from '@/app/types';
import { Badge } from '@/components/ui/badge';
import { Tag, Power, AlertCircle, FileDigit } from 'lucide-react';
import { motion } from 'framer-motion';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';

interface RuleHeaderProps {
  rule: Rule;
  isEditing?: boolean;
  onStatusChange?: (status: boolean) => void;
  onSeverityChange?: (severity: "Low" | "Medium" | "High" | "Critical") => void;
  editButton?: ReactNode;
}

export const RuleHeader: FC<RuleHeaderProps> = ({ 
  rule, 
  isEditing = false,
  onStatusChange,
  onSeverityChange,
  editButton
}) => {
  const getSeverityConfig = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical':
        return { color: 'text-purple-600', bg: 'bg-purple-50', border: 'border-purple-200' };
      case 'high':
        return { color: 'text-red-600', bg: 'bg-red-50', border: 'border-red-200' };
      case 'medium':
        return { color: 'text-yellow-600', bg: 'bg-yellow-50', border: 'border-yellow-200' };
      case 'low':
        return { color: 'text-green-600', bg: 'bg-green-50', border: 'border-green-200' };
      default:
        return { color: 'text-gray-600', bg: 'bg-gray-50', border: 'border-gray-200' };
    }
  };

  const severityConfig = getSeverityConfig(rule.rule_severity);

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="border border-gray-200 rounded-lg p-4 shadow-sm bg-white space-y-4 hover:shadow-md transition-shadow">
        {/* Rule Title and Controls */}
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <h3 className="text-xl font-semibold text-gray-800 tracking-tight">{rule.rule_name}</h3>
          </div>
          
          <div className="flex items-center gap-3">
            {/* Status Badge */}
            {isEditing ? (
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-500">Status:</span>
                <Switch
                  checked={rule.rule_status}
                  onCheckedChange={onStatusChange}
                />
              </div>
            ) : (
              rule.rule_status ? (
                <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200 py-1 px-3 rounded-full text-xs font-medium">
                  Active
                </Badge>
              ) : (
                <Badge variant="outline" className="bg-red-50 text-red-600 border-red-200 py-1 px-3 rounded-full text-xs font-medium">
                  Inactive
                </Badge>
              )
            )}
            
            {/* Severity Badge */}
            {isEditing ? (
              <Select
                value={rule.rule_severity}
                onValueChange={(value: "Low" | "Medium" | "High" | "Critical") => 
                  onSeverityChange?.(value)
                }
              >
                <SelectTrigger className="w-[120px] h-8 text-xs">
                  <SelectValue placeholder="Select severity" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Low">Low</SelectItem>
                  <SelectItem value="Medium">Medium</SelectItem>
                  <SelectItem value="High">High</SelectItem>
                  <SelectItem value="Critical">Critical</SelectItem>
                </SelectContent>
              </Select>
            ) : (
              <Badge 
                variant="outline" 
                className={`${severityConfig.bg} ${severityConfig.color} ${severityConfig.border} py-1 px-3 rounded-full text-xs font-medium`}
              >
                {rule.rule_severity}
              </Badge>
            )}
            
            {/* Edit Button */}
            <div className="edit-button-container">
              {editButton}
            </div>
          </div>
        </div>
        
        {/* Rule Description */}
        <p className="text-gray-600 text-sm leading-relaxed">{rule.rule_description}</p>

        {/* Rule Metadata */}
        <div className="grid grid-cols-2 gap-4 mt-2 border-t border-gray-100 pt-4">
          <div className="flex items-start gap-2">
            <div className="bg-blue-50 p-1.5 rounded-full mt-0.5 shrink-0">
              <Tag className="h-3.5 w-3.5 text-blue-500" />
            </div>
            <div className="flex flex-col min-w-0">
              <span className="text-xs text-gray-500">Rule Code</span>
              <span className="text-sm font-medium text-gray-700 break-all">{rule.rule_code}</span>
            </div>
          </div>
          <div className="flex items-start gap-2">
            <div className="bg-purple-50 p-1.5 rounded-full mt-0.5 shrink-0">
              <Power className="h-3.5 w-3.5 text-purple-500" />
            </div>
            <div className="flex flex-col min-w-0">
              <span className="text-xs text-gray-500">Rule Type</span>
              <span className="text-sm font-medium text-gray-700 break-all">{rule.rule_type}</span>
            </div>
          </div>
          <div className="flex items-start gap-2">
            <div className="bg-amber-50 p-1.5 rounded-full mt-0.5 shrink-0">
              <AlertCircle className="h-3.5 w-3.5 text-amber-500" />
            </div>
            <div className="flex flex-col min-w-0">
              <span className="text-xs text-gray-500">Fraud Type</span>
              <span className="text-sm font-medium text-gray-700 break-all">{rule.fraud_type}</span>
            </div>
          </div>
          <div className="flex items-start gap-2">
            <div className="bg-teal-50 p-1.5 rounded-full mt-0.5 shrink-0">
              <FileDigit className="h-3.5 w-3.5 text-teal-500" />
            </div>
            <div className="flex flex-col min-w-0">
              <span className="text-xs text-gray-500">ID</span>
              <span className="text-sm font-medium text-gray-700 break-all" title={rule.id}>{rule.id}</span>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}; 