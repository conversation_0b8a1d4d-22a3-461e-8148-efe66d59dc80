import { FC } from 'react';
import { MetricEquation } from '@/app/types';
import { CustomCard } from '@/components/custom/CustomCard';
import { motion } from 'framer-motion';
import { FileText } from 'lucide-react';
import { RuleMetricEquation } from './RuleMetricEquation';

interface RuleConditionsProps {
  equation: MetricEquation;
}

export const RuleConditions: FC<RuleConditionsProps> = ({ equation }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.2 }}
    >
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <FileText className="h-5 w-5 text-blue-500" />
          <h3 className="text-lg font-semibold">Rule Conditions</h3>
        </div>
        <CustomCard className="p-6">
          <RuleMetricEquation equation={equation} />
        </CustomCard>
      </div>
    </motion.div>
  );
}; 