import { FC, useState, useEffect } from 'react';
import { Rule, BaseMetricCondition, CompositeMetricCondition } from '@/app/types';
import { RuleHeader } from './RuleHeader';
import { RuleMetricEquation } from './RuleMetricEquation';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { useRulesRepoStore } from '@/app/store/ruleRepo/RulesRepoStore';
import { Edit2, Save, FileText, Calendar, User, Clock, Code, Calendar as CalendarIcon } from 'lucide-react';
import { RulePerformanceDefault } from './RulePerformanceDefault';
import { format } from 'date-fns';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface RuleDetailArtifactProps {
  rule: Rule;
}

export const RuleDetailArtifact: FC<RuleDetailArtifactProps> = ({ rule }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedRule, setEditedRule] = useState(rule);
  const [pendingMetricUpdates, setPendingMetricUpdates] = useState<BaseMetricCondition[]>([]);
  const [activeTab, setActiveTab] = useState('performance');
  const [selectedFrequency, setSelectedFrequency] = useState('1y');

  const {
    updateRule,
    updateNumericMetric,
    updateStringMetric,
    updateBooleanMetric,
    getRules
  } = useRulesRepoStore();

  // Update local state when rule prop changes
  useEffect(() => {
    setEditedRule(rule);
  }, [rule]);

  const handleStatusChange = (status: boolean) => {
    setEditedRule(prev => ({ ...prev, rule_status: status }));
  };

  const handleSeverityChange = (severity: "Low" | "Medium" | "High" | "Critical") => {
    setEditedRule(prev => ({ ...prev, rule_severity: severity }));
  };

  const handleMetricUpdate = (metric: BaseMetricCondition, oldMetricName?: string) => {
    // Update pending changes for saving to backend
    setPendingMetricUpdates(prev => {
      // If this is a field change, find by old name
      const searchName = oldMetricName || metric.metric_name;
      const existing = prev.findIndex(m => m.metric_name === searchName);
      if (existing >= 0) {
        const updated = [...prev];
        updated[existing] = metric;
        return updated;
      }
      return [...prev, metric];
    });

    // Update the metric in the equation for immediate UI update
    setEditedRule(prev => ({
      ...prev,
      metric_equation: {
        ...prev.metric_equation,
        conditions: prev.metric_equation.conditions.map(condition =>
          updateMetricInEquation(condition, metric, oldMetricName)
        )
      }
    }));
  };

  // Helper function to update metric in nested conditions
  const updateMetricInEquation = (
    condition: BaseMetricCondition | CompositeMetricCondition,
    updatedMetric: BaseMetricCondition,
    oldMetricName?: string
  ): BaseMetricCondition | CompositeMetricCondition => {
    if ('metric_name' in condition) {
      // If oldMetricName is provided, this is a field change - find by old name
      if (oldMetricName) {
        return condition.metric_name === oldMetricName ? updatedMetric : condition;
      } else {
        // Regular update - find by current metric name
        return condition.metric_name === updatedMetric.metric_name ? updatedMetric : condition;
      }
    }
    return {
      ...condition,
      conditions: condition.conditions.map(cond => updateMetricInEquation(cond, updatedMetric, oldMetricName))
    };
  };

  const handleSave = async () => {
    try {
      // Update rule status and severity
      if (editedRule.rule_status !== rule.rule_status ||
          editedRule.rule_severity !== rule.rule_severity) {
        await updateRule(
          editedRule.created_by || "<EMAIL>",
          rule.rule_code,
          editedRule.rule_status,
          editedRule.rule_severity
        );
      }

      // Update metrics (Note: This may need to be updated for the new API)
      for (const metric of pendingMetricUpdates) {
        switch (metric.type) {
          case 'numeric':
            await updateNumericMetric(
              editedRule.created_by || "<EMAIL>",
              metric.metric_name,
              rule.rule_code,
              metric.value as number,
              metric.operation
            );
            break;
          case 'string':
            await updateStringMetric(
              editedRule.created_by || "<EMAIL>",
              metric.metric_name,
              rule.rule_code,
              metric.value as string,
              metric.operation
            );
            break;
          case 'boolean':
            await updateBooleanMetric(
              editedRule.created_by || "<EMAIL>",
              metric.metric_name,
              rule.rule_code,
              metric.value as boolean,
              metric.operation
            );
            break;
        }
      }

      // Refresh the rules after saving
      await getRules(0, 100, true);
      setIsEditing(false);
      setPendingMetricUpdates([]);
    } catch (error) {
      console.error('Error saving updates:', error);
      // TODO: Add error handling UI
    }
  };

  // Format dates for display
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'N/A';
    try {
      return format(new Date(dateString), 'MMM d, yyyy h:mm a');
    } catch (e) {
      return dateString;
    }
  };

  // Ensure metric equation exists
  const ensuredMetricEquation = editedRule.metric_equation || {
    operator: "AND",
    conditions: []
  };

  return (
    <motion.div
      className="space-y-6 max-w-4xl p-4"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <RuleHeader
        rule={editedRule}
        isEditing={isEditing}
        onStatusChange={handleStatusChange}
        onSeverityChange={handleSeverityChange}
        editButton={
          <Button
            variant="outline"
            onClick={isEditing ? handleSave : () => setIsEditing(true)}
            className="flex items-center gap-1.5 h-8 px-3 text-xs font-medium"
            size="sm"
          >
            {isEditing ? (
              <>
                <Save className="h-3.5 w-3.5" />
                Save
              </>
            ) : (
              <>
                <Edit2 className="h-3.5 w-3.5" />
                Edit Rule
              </>
            )}
          </Button>
        }
      />

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-4">
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="conditions">Conditions</TabsTrigger>
          <TabsTrigger value="raw">Raw Definition</TabsTrigger>
          <TabsTrigger value="metadata">Metadata</TabsTrigger>
        </TabsList>

        <TabsContent value="conditions" className="mt-2">
          <RuleMetricEquation
            equation={ensuredMetricEquation}
            isEditing={isEditing}
            onMetricUpdate={handleMetricUpdate}
          />
        </TabsContent>

        <TabsContent value="performance" className="mt-2">
          <RulePerformanceDefault ruleId={rule.rule_code} timePeriod={selectedFrequency} />
        </TabsContent>

        <TabsContent value="raw" className="mt-2">
          <div className="bg-slate-50 p-4 rounded-md overflow-auto max-h-[400px]">
            <pre className="text-xs font-mono whitespace-pre-wrap text-gray-700">
              {JSON.stringify(rule.raw_rule || {}, null, 2)}
            </pre>
          </div>
        </TabsContent>

        <TabsContent value="metadata" className="mt-2">
          <Card className="bg-gray-50 border-gray-200">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-700">Rule Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-xs text-gray-500">Created By</p>
                    <p className="text-sm font-medium">{rule.created_by || 'Unknown'}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-xs text-gray-500">Created At</p>
                    <p className="text-sm font-medium">{formatDate(rule.created_at)}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-xs text-gray-500">Last Updated By</p>
                    <p className="text-sm font-medium">{rule.updated_by || 'Unknown'}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-xs text-gray-500">Last Updated</p>
                    <p className="text-sm font-medium">{formatDate(rule.updated_at)}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2 col-span-2">
                  <div>
                    <p className="text-xs text-gray-500">Version</p>
                    <p className="text-sm font-medium">{rule.version || 1}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

    </motion.div>
  );
};