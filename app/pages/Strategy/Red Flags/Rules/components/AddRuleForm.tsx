import { FC, useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useRulesRepoStore } from '@/app/store/ruleRepo/RulesRepoStore';
import { X, Plus, Save, AlertTriangle, CheckCircle2, Info } from 'lucide-react';
import { motion } from 'framer-motion';

// Define condition item type matching the API expectation
interface ConditionItem {
  table: string;
  condition: string;
  operator: string;
  value: string | number;  // Boolean is not needed in our UI, keep it simpler
}

interface AddRuleFormProps {
  onClose: () => void;
}

export const AddRuleForm: FC<AddRuleFormProps> = ({ onClose }) => {
  const { getRules, createRule } = useRulesRepoStore();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [createdRule, setCreatedRule] = useState<any>(null);

  const [formData, setFormData] = useState<{
    code: string;
    name: string;
    description: string;
    type: string;
    severity: string;
    fraud_type: string;
    andConditions: ConditionItem[];
    orConditions: ConditionItem[];
  }>({
    code: '',
    name: '',
    description: '',
    type: '',
    severity: 'Medium',
    fraud_type: '',
    andConditions: [{ table: '', condition: '', operator: '==', value: '' }],
    orConditions: [{ table: '', condition: '', operator: '==', value: '' }]
  });

  // Auto-close after successful creation
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => {
        onClose();
      }, 2000);
      
      return () => clearTimeout(timer);
    }
  }, [success, onClose]);

  const handleInputChange = (field: string, value: string) => {
    // Clear any error messages when user makes changes
    if (error) setError(null);
    setFormData({ ...formData, [field]: value });
  };

  // Helper function to convert string value to appropriate type
  const parseValue = (value: string, operator: string): string | number => {
    // If operator is numeric comparison, try to parse as number
    if (['>', '<', '>=', '<='].includes(operator)) {
      const num = Number(value);
      return isNaN(num) ? value : num;
    }
    
    // Default to string (we'll handle booleans in the API layer if needed)
    return value;
  };

  const handleConditionChange = (
    type: 'and' | 'or', 
    index: number, 
    field: keyof ConditionItem, 
    value: string
  ) => {
    // Clear any error messages when user makes changes
    if (error) setError(null);
    
    const conditions = type === 'and' ? [...formData.andConditions] : [...formData.orConditions];
    
    if (field === 'value') {
      // Parse the value based on the current operator
      const operator = conditions[index].operator;
      const parsedValue = parseValue(value, operator);
      conditions[index] = { ...conditions[index], [field]: parsedValue };
    } else {
      conditions[index] = { ...conditions[index], [field]: value };
      
      // If operator changed, re-parse the value
      if (field === 'operator' && conditions[index].value) {
        const currentValue = String(conditions[index].value);
        conditions[index].value = parseValue(currentValue, value);
      }
    }
    
    if (type === 'and') {
      setFormData({ ...formData, andConditions: conditions });
    } else {
      setFormData({ ...formData, orConditions: conditions });
    }
  };

  const addCondition = (type: 'and' | 'or') => {
    const newCondition = { table: '', condition: '', operator: '==', value: '' };
    if (type === 'and') {
      setFormData({ ...formData, andConditions: [...formData.andConditions, newCondition] });
    } else {
      setFormData({ ...formData, orConditions: [...formData.orConditions, newCondition] });
    }
  };

  const removeCondition = (type: 'and' | 'or', index: number) => {
    if (type === 'and') {
      const conditions = [...formData.andConditions];
      conditions.splice(index, 1);
      setFormData({ ...formData, andConditions: conditions });
    } else {
      const conditions = [...formData.orConditions];
      conditions.splice(index, 1);
      setFormData({ ...formData, orConditions: conditions });
    }
  };

  const prepareRuleRequest = () => {
    // Validate required fields
    if (!formData.code || !formData.name || !formData.description || !formData.fraud_type) {
      throw new Error('Please fill in all required fields');
    }

    // Validate conditions - there should be at least one valid condition
    const validAndConditions = formData.andConditions.filter(c => c.table && c.condition && c.value !== '');
    const validOrConditions = formData.orConditions.filter(c => c.table && c.condition && c.value !== '');
    
    if (validAndConditions.length === 0 && validOrConditions.length === 0) {
      throw new Error('Please add at least one valid condition');
    }

    // Create the base request structure
    const requestData = {
      code: formData.code,
      name: formData.name,
      description: formData.description,
      type: formData.type || 'custom',
      severity: formData.severity.toLowerCase(), // API expects lowercase
      fraud_type: formData.fraud_type,
      rule: {} // Will populate based on conditions
    };

    // Build the rule object based on what conditions are present
    // This ensures we don't create invalid rule structures
    if (validAndConditions.length > 0 && validOrConditions.length > 0) {
      // Both AND and OR conditions - use the nested structure
      requestData.rule = {
        and: [
          ...validAndConditions,
          { or: validOrConditions }
        ]
      };
    } else if (validAndConditions.length > 0) {
      // Only AND conditions
      requestData.rule = { and: validAndConditions };
    } else {
      // Only OR conditions
      requestData.rule = { or: validOrConditions };
    }

    return requestData;
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      // Prepare and validate rule request
      const ruleRequest = prepareRuleRequest();
      
      // Debug logging for request payload
      console.log('Creating rule with payload:', JSON.stringify(ruleRequest, null, 2));
      
      // Create rule using the store function
      const response = await createRule(ruleRequest);
      
      // Store the created rule
      setCreatedRule(response);
      
      // Set success message with appropriate ID from response
      const ruleId = response.id || response.code || 'new rule';
      setSuccess(`Rule ${ruleId} created successfully`);
      
      // Form will auto-close after delay due to useEffect
    } catch (err: any) {
      console.error('Error creating rule:', err);
      
      // Detailed error logging for debugging
      if (err.response) {
        console.error('API error response:', err.response.data);
        console.error('API error status:', err.response.status);
        
        // Handle specific API validation errors
        if (err.response.data?.detail && Array.isArray(err.response.data.detail)) {
          const validationErrors = err.response.data.detail;
          // Find rule structure errors
          const ruleError = validationErrors.find((e: any) => 
            e.loc && e.loc.includes('rule') && e.msg.includes('both')
          );
          
          if (ruleError) {
            setError('The API does not allow both AND and OR conditions at the same level. When using both types, OR conditions will be nested within the AND group.');
            return;
          }
        }
      }
      
      const errorMessage = err.response?.data?.detail || 
                          (typeof err.response?.data === 'string' ? err.response.data : err.message) || 
                          'Failed to create rule';
      setError(typeof errorMessage === 'string' ? errorMessage : JSON.stringify(errorMessage));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div 
      className="space-y-6 max-w-4xl p-4"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Add New Rule</h2>
        <Button variant="outline" size="icon" onClick={onClose} className="flex items-center gap-1">
          <X className="h-4 w-4" />
        </Button>
      </div>

      <div className="space-y-6 bg-white rounded-md p-6 shadow-sm">
        {error && (
          <div className="bg-red-50 p-3 rounded-md flex items-start gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
            <p className="text-sm text-red-700">{error}</p>
          </div>
        )}

        {success && (
          <div className="bg-green-50 p-3 rounded-md flex items-start gap-2">
            <CheckCircle2 className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
            <p className="text-sm text-green-700">{success}</p>
          </div>
        )}

        <div className="bg-blue-50 p-3 rounded-md flex items-start gap-2">
          <Info className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
          <div className="text-sm text-blue-700">
            <p className="mb-1 font-medium">About Rule Conditions:</p>
            <ul className="list-disc pl-5 space-y-1">
              <li>You can use only AND conditions or only OR conditions</li>
              <li>When using both AND and OR conditions together, the system will automatically nest the OR conditions within the AND structure</li>
              <li>This means all AND conditions must be true, AND at least one of the OR conditions must be true</li>
            </ul>
          </div>
        </div>

        <div className="space-y-5">
          <div className="space-y-2">
            <Label htmlFor="code">Rule Code*</Label>
            <Input 
              id="code" 
              value={formData.code} 
              onChange={(e) => handleInputChange('code', e.target.value)}
              placeholder="e.g. RULE_001"
              className="px-3 py-2 text-sm w-full"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="name">Rule Name*</Label>
            <Input 
              id="name" 
              value={formData.name} 
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="e.g. High Transaction Amount"
              className="px-3 py-2 text-sm w-full"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description*</Label>
            <Textarea 
              id="description" 
              value={formData.description} 
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Describe the purpose of this rule"
              rows={3}
              className="px-3 py-2 text-sm"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="type">Rule Type</Label>
              <Input 
                id="type" 
                value={formData.type} 
                onChange={(e) => handleInputChange('type', e.target.value)}
                placeholder="e.g. transaction"
                className="px-3 py-2 text-sm w-full"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="severity">Severity</Label>
              <Select 
                value={formData.severity} 
                onValueChange={(value) => handleInputChange('severity', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select severity" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Low">Low</SelectItem>
                  <SelectItem value="Medium">Medium</SelectItem>
                  <SelectItem value="High">High</SelectItem>
                  <SelectItem value="Critical">Critical</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="fraud_type">Fraud Type*</Label>
            <Input 
              id="fraud_type" 
              value={formData.fraud_type} 
              onChange={(e) => handleInputChange('fraud_type', e.target.value)}
              placeholder="e.g. Payment Fraud"
              className="px-3 py-2 text-sm w-full"
            />
          </div>
        </div>
      </div>

      <div className="space-y-4 bg-white rounded-md p-6 shadow-sm">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="font-medium">AND Conditions</h3>
            <p className="text-xs text-gray-500 mt-1">All of these conditions must be true</p>
          </div>
        </div>
        
        {formData.andConditions.map((condition, index) => (
          <div key={`and-${index}`} className="space-y-3 p-4 border rounded-md bg-gray-50">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Condition {index + 1}</span>
              {formData.andConditions.length > 1 && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => removeCondition('and', index)}
                  className="h-6 w-6 p-0"
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>
            
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-1">
                <Label htmlFor={`and-${index}-table`} className="text-xs">Table</Label>
                <Input 
                  id={`and-${index}-table`} 
                  value={condition.table} 
                  onChange={(e) => handleConditionChange('and', index, 'table', e.target.value)}
                  placeholder="Table name"
                  className="h-10 px-3 text-sm w-full"
                />
              </div>
              <div className="space-y-1">
                <Label htmlFor={`and-${index}-condition`} className="text-xs">Condition</Label>
                <Input 
                  id={`and-${index}-condition`} 
                  value={condition.condition} 
                  onChange={(e) => handleConditionChange('and', index, 'condition', e.target.value)}
                  placeholder="Field name"
                  className="h-10 px-3 text-sm w-full"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-1">
                <Label htmlFor={`and-${index}-operator`} className="text-xs">Operator</Label>
                <Select 
                  value={condition.operator} 
                  onValueChange={(value) => handleConditionChange('and', index, 'operator', value)}
                >
                  <SelectTrigger className="h-9 text-sm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="==">equals (==)</SelectItem>
                    <SelectItem value="!=">not equals (!=)</SelectItem>
                    <SelectItem value=">">greater than (&gt;)</SelectItem>
                    <SelectItem value="<">less than (&lt;)</SelectItem>
                    <SelectItem value=">=">greater than or equal (&gt;=)</SelectItem>
                    <SelectItem value="<=">less than or equal (&lt;=)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-1">
                <Label htmlFor={`and-${index}-value`} className="text-xs">Value</Label>
                <Input 
                  id={`and-${index}-value`} 
                  value={condition.value} 
                  onChange={(e) => handleConditionChange('and', index, 'value', e.target.value)}
                  placeholder="Value"
                  className="h-10 px-3 text-sm w-full"
                />
              </div>
            </div>
          </div>
        ))}
        <Button 
          variant="outline" 
          size="sm" 
          onClick={() => addCondition('and')}
          className="w-full flex items-center gap-1 mt-3"
        >
          <Plus className="h-3 w-3" />
          Add AND Condition
        </Button>
      </div>

      <div className="space-y-4 bg-white rounded-md p-6 shadow-sm">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="font-medium">OR Conditions</h3>
            <p className="text-xs text-gray-500 mt-1">At least one of these conditions must be true</p>
          </div>
        </div>
        
        {formData.orConditions.map((condition, index) => (
          <div key={`or-${index}`} className="space-y-3 p-4 border rounded-md bg-gray-50">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Condition {index + 1}</span>
              {formData.orConditions.length > 1 && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => removeCondition('or', index)}
                  className="h-6 w-6 p-0"
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>
            
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-1">
                <Label htmlFor={`or-${index}-table`} className="text-xs">Table</Label>
                <Input 
                  id={`or-${index}-table`} 
                  value={condition.table} 
                  onChange={(e) => handleConditionChange('or', index, 'table', e.target.value)}
                  placeholder="Table name"
                  className="h-10 px-3 text-sm w-full"
                />
              </div>
              <div className="space-y-1">
                <Label htmlFor={`or-${index}-condition`} className="text-xs">Condition</Label>
                <Input 
                  id={`or-${index}-condition`} 
                  value={condition.condition} 
                  onChange={(e) => handleConditionChange('or', index, 'condition', e.target.value)}
                  placeholder="Field name"
                  className="h-10 px-3 text-sm w-full"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-1">
                <Label htmlFor={`or-${index}-operator`} className="text-xs">Operator</Label>
                <Select 
                  value={condition.operator} 
                  onValueChange={(value) => handleConditionChange('or', index, 'operator', value)}
                >
                  <SelectTrigger className="h-9 text-sm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="==">equals (==)</SelectItem>
                    <SelectItem value="!=">not equals (!=)</SelectItem>
                    <SelectItem value=">">greater than (&gt;)</SelectItem>
                    <SelectItem value="<">less than (&lt;)</SelectItem>
                    <SelectItem value=">=">greater than or equal (&gt;=)</SelectItem>
                    <SelectItem value="<=">less than or equal (&lt;=)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-1">
                <Label htmlFor={`or-${index}-value`} className="text-xs">Value</Label>
                <Input 
                  id={`or-${index}-value`} 
                  value={condition.value} 
                  onChange={(e) => handleConditionChange('or', index, 'value', e.target.value)}
                  placeholder="Value"
                  className="h-10 px-3 text-sm w-full"
                />
              </div>
            </div>
          </div>
        ))}
        <Button 
          variant="outline" 
          size="sm" 
          onClick={() => addCondition('or')}
          className="w-full flex items-center gap-1 mt-3"
        >
          <Plus className="h-3 w-3" />
          Add OR Condition
        </Button>
      </div>

      <div className="flex gap-3">
        <Button 
          onClick={handleSubmit} 
          className="flex-1 flex items-center justify-center gap-2"
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <span className="animate-spin">
                <Save className="h-4 w-4" />
              </span>
              Creating Rule...
            </>
          ) : (
            <>
              <Save className="h-4 w-4" />
              Create Rule
            </>
          )}
        </Button>
        <Button 
          variant="outline"
          onClick={onClose}
          className="flex-1"
        >
          Cancel
        </Button>
      </div>
    </motion.div>
  );
}; 