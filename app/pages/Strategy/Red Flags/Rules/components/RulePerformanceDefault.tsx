import { FC, useState, useMemo, useEffect } from 'react';
import { Visualization } from '@/components/custom/visualization';
import { VisualizationData } from '@/components/custom/visualization/types';
import { Bar<PERSON><PERSON>, FileText } from 'lucide-react';
import { getPerformanceData, getRuleStatistics } from './rulePerformanceData';
import { AddToDashboardDropdown } from '@/app/components/dashboard/AddToDashboardDropdown';
import { ToggleTabs } from '@/components/custom/ToggleTabs';

interface RulePerformanceDefaultProps {
  ruleId: string;  // This is rule_code from the Rule type
  timePeriod?: string; // Optional prop for time period from parent
}

export const RulePerformanceDefault: FC<RulePerformanceDefaultProps> = ({ ruleId, timePeriod: externalTimePeriod }) => {
  // Add state for time period toggle
  const [timePeriod, setTimePeriod] = useState<string>('lifetime');
  // Add state for view toggle (Graph or Stats)
  const [view, setView] = useState<string>('graph');
  
  // Update internal timePeriod when external prop changes
  useEffect(() => {
    if (externalTimePeriod) {
      setTimePeriod(externalTimePeriod);
    }
  }, [externalTimePeriod]);

  // Get performance data from the data file
  const allPerformanceData = getPerformanceData(ruleId);
  
  // Filter performance data based on selected time period
  const performanceData = useMemo(() => {
    // Define the number of months to include based on the time period
    const getMonthsToInclude = () => {
      switch (timePeriod) {
        case '1d': return 1/30; // Approximately 1 day of data (scaled from monthly data)
        case '7d': return 7/30; // Approximately 7 days of data
        case '1m': return 1; // 1 month
        case '6m': return 6; // 6 months
        case '1y': return 12; // 12 months
        case 'lifetime': return 36; // Showing all available data (3 years)
        default: return 36; // Default to lifetime
      }
    };
    
    const monthsToInclude = getMonthsToInclude();
    
    // If we need all data, just return it
    if (timePeriod === '1y' || timePeriod === 'lifetime') {
      return allPerformanceData;
    }
    
    // Otherwise filter based on the number of months
    const dataCount = Math.max(1, Math.ceil(monthsToInclude));
    
    // For periods less than a month, we'll still show the latest month but scale the data
    if (monthsToInclude < 1) {
      if (allPerformanceData.length > 0) {
        const latestData = allPerformanceData[0];
        return [{
          ...latestData,
          triggers: Math.round(latestData.triggers * monthsToInclude),
          hitrate: latestData.hitrate
        }];
      }
      return [];
    }
    
    // For other periods, return the most recent N months
    return allPerformanceData.slice(0, dataCount);
  }, [allPerformanceData, timePeriod]);
  
  // Get statistics data for the stats visualization
  const allStatisticsData = getRuleStatistics(ruleId);
  
  // Adjust statistics based on the time period
  const statisticsData = useMemo(() => {
    // For all data, just return everything
    if (timePeriod === '1y' || timePeriod === 'lifetime') {
      return allStatisticsData;
    }
    
    // Define a scaling factor based on the time period
    const getScalingFactor = () => {
      switch (timePeriod) {
        case '1d': return 1/365; // 1 day
        case '7d': return 7/365; // 7 days
        case '1m': return 30/365; // 1 month
        case '6m': return 180/365; // 6 months
        case '1y': return 1; // 1 year (same as all)
        case 'lifetime': return 3; // 3 years of data for lifetime view
        default: return 3; // Default to lifetime
      }
    };
    
    const scalingFactor = getScalingFactor();
    
    // Scale the statistics based on the time period
    return {
      totalTriggers: Math.round(allStatisticsData.totalTriggers * scalingFactor),
      averageHitrate: allStatisticsData.averageHitrate, // Hitrate remains the same regardless of period
      fraudCases: Math.round(allStatisticsData.fraudCases * scalingFactor),
      highRiskCases: Math.round(allStatisticsData.highRiskCases * scalingFactor),
      efficiency: allStatisticsData.efficiency // Efficiency remains the same
    };
  }, [allStatisticsData, timePeriod]);
  
  // Transform statistics into the format expected by the Visualization component
  const statsData: VisualizationData[] = [{
    name: 'Stats',
    totalTriggers: statisticsData.totalTriggers,
    averageHitrate: statisticsData.averageHitrate,
    fraudCases: statisticsData.fraudCases,
    highRiskCases: statisticsData.highRiskCases,
    efficiency: statisticsData.efficiency
  }];

  // Performance Chart Visualization ID (would be stored in database in real app)
  const performanceChartVisualizationId = `rule-${ruleId}-performance-chart`;
  
  // Statistics Visualization ID (would be stored in database in real app)
  const statisticsVisualizationId = `rule-${ruleId}-statistics`;

  // Custom component to inject the dashboard dropdown
  const PerformanceChartHeader = () => (
    <AddToDashboardDropdown visualizationId={performanceChartVisualizationId} />
  );

  // Custom component to inject the dashboard dropdown for stats
  const StatisticsHeader = () => (
    <AddToDashboardDropdown visualizationId={statisticsVisualizationId} />
  );

  // View options
  const viewOptions = [
    { value: 'graph', label: 'Trend' },
    { value: 'stats', label: 'Stats' },
  ];

  // Time period options
  const timePeriodOptions = [
    { value: '1d', label: '1d' },
    { value: '7d', label: '7d' },
    { value: '1m', label: '1m' },
    { value: '6m', label: '6m' },
    { value: '1y', label: '1y' },
    { value: 'lifetime', label: 'All' },
  ];

  // Format the time period for display
  const getFormattedTimePeriod = () => {
    switch (timePeriod) {
      case '1d': return '1 Day';
      case '7d': return '7 Days';
      case '1m': return '1 Month';
      case '6m': return '6 Months';
      case '1y': return '1 Year';
      case 'lifetime': return 'Lifetime';
      default: return timePeriod;
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <ToggleTabs
          value={view}
          onValueChange={setView}
          options={viewOptions}
          className="rounded-md border border-gray-200 shadow-sm"
          gridColumnClass="grid-cols-2"
        />
        <ToggleTabs
          value={timePeriod}
          onValueChange={setTimePeriod}
          options={timePeriodOptions}
          className="rounded-md border border-gray-200 shadow-sm"
          gridColumnClass="grid-cols-6"
        />
      </div>
      
      {/* Conditionally render based on the selected view */}
      <div className="mt-2">
        {view === 'graph' ? (
          <Visualization
            type="combo"
            data={performanceData}
            title={`Performance Trend (${getFormattedTimePeriod()})`}
            description="Triggers count (bars) and fraud hit rate % (line)"
            xAxisKey="name"
            leftYAxisLabel="Trigger Count"
            rightYAxisLabel="Fraud Hit Rate (%)"
            yAxisKeys={[
              { 
                key: 'triggers', 
                color: '#6366f1', 
                type: 'bar',
                yAxisId: 'left'
              },
              { 
                key: 'hitrate', 
                color: '#ef4444', 
                type: 'line',
                yAxisId: 'right'
              }
            ]}
            visualizationId={performanceChartVisualizationId}
            customHeaderComponent={<PerformanceChartHeader />}
          />
        ) : (
          <Visualization
            type="stats"
            data={statsData}
            title={`Performance Stats (${getFormattedTimePeriod()})`}
            description="Key performance indicators for this rule"
            xAxisKey="name"
            yAxisKeys={[
              { 
                key: 'totalTriggers', 
                color: '#6366f1',
                label: 'Total Triggers',
                size: 'big',
                type: 'bar'
              },
              { 
                key: 'averageHitrate', 
                color: '#ef4444',
                label: 'Avg. Hit Rate (%)',
                size: 'big',
                type: 'bar'
              },
              { 
                key: 'fraudCases', 
                color: '#f97316',
                label: 'Fraud Cases',
                size: 'medium',
                type: 'bar'
              },
              { 
                key: 'highRiskCases', 
                color: '#eab308',
                label: 'High Risk Cases',
                size: 'medium',
                type: 'bar'
              },
              { 
                key: 'efficiency', 
                color: '#22c55e',
                label: 'Efficiency Score (%)',
                size: 'medium',
                type: 'bar'
              }
            ]}
            visualizationId={statisticsVisualizationId}
            customHeaderComponent={<StatisticsHeader />}
          />
        )}
      </div>
    </div>
  );
}; 