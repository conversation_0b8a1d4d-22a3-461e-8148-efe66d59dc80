import { FC, useEffect, useState } from 'react';
import { useRulesRepoStore } from '@/app/store/ruleRepo/RulesRepoStore';
import { MultiSelect } from '@/components/ui/multi-select2';
import { useArtifactStore } from '@/app/store/artifact/artifactStore';
import { RuleDetailArtifact } from './components';
import { Rule } from '@/app/types';
import { motion } from 'framer-motion';
import { VirtualList, VirtualListItemProps } from "@/components/custom/VirtualList";
import { RuleCard } from './components';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Loader2, AlertTriangle, RefreshCw, Plus } from 'lucide-react';
import { AddRuleForm } from './components/AddRuleForm';

// Animation variants for container
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

// Animation variants for items
const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: {
      duration: 0.3
    }
  }
};

export const RulesRepositoryTab: FC = () => {
  const { rules, getRules } = useRulesRepoStore();
  const { addTab, setActiveTabId, setCollapsed, removeTab } = useArtifactStore();
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(0);
  const pageSize = 20;
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  // Fetch rules with pagination
  const fetchRules = async (page: number = 0) => {
    setIsLoading(true);
    setError(null);
    try {
      await getRules(page * pageSize, pageSize, true);
      setCurrentPage(page);
    } catch (err: any) {
      console.error('Failed to fetch rules:', err);
      setError(err?.message || 'Failed to load rules. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial fetch with retry logic
  useEffect(() => {
    const initFetch = async () => {
      try {
        await fetchRules();
      } catch (err) {
        // If we've tried less than 3 times, retry
        if (retryCount < 3) {
          console.log(`Retrying fetch (attempt ${retryCount + 1})...`);
          setRetryCount(prev => prev + 1);
          setTimeout(() => initFetch(), 1000 * (retryCount + 1)); // Exponential backoff
        }
      }
    };

    initFetch();
  }, [retryCount]);

  // Update fraud types when rules data changes
  useEffect(() => {
    if (rules.data.length > 0 && selectedTypes.length === 0) {
      const uniqueTypes = Array.from(new Set(rules.data.map(rule => rule.fraud_type.toLowerCase())));
      if (uniqueTypes.length > 0) {
        setSelectedTypes([uniqueTypes[0]]);
      }
    }
  }, [rules.data]);

  // Calculate fraud type options from data
  const fraudTypeOptions = Array.from(
    new Set(rules.data.map(rule => rule.fraud_type))
  )
  .filter(Boolean) // Filter out empty or null fraud types
  .map(type => ({
    label: type,
    value: type.toLowerCase()
  }));

  const handleItemClick = (item: VirtualListItemProps) => {
    const rule = item.metadata as Rule;
    addTab({
      id: rule.rule_code,
      title: rule.rule_name,
      renderArtifact: () => <RuleDetailArtifact rule={rule} />
    });
    setCollapsed(false);
  };

  // Filter rules based on selected fraud types
  const filteredRules = rules.data.filter(rule => 
    selectedTypes.length === 0 || selectedTypes.includes((rule.fraud_type || '').toLowerCase())
  );

  const virtualItems = filteredRules.map(rule => ({
    id: rule.rule_code,
    title: rule.rule_name,
    content: <RuleCard rule={rule} onClick={() => handleItemClick({ 
      id: rule.rule_code, 
      title: rule.rule_name, 
      content: null, 
      metadata: rule 
    })}/>,
    metadata: rule
  }));

  const handleNextPage = () => {
    fetchRules(currentPage + 1);
  };

  const handlePrevPage = () => {
    if (currentPage > 0) {
      fetchRules(currentPage - 1);
    }
  };

  const handleRetry = () => {
    setRetryCount(0);
    fetchRules(currentPage);
  };

  const handleAddRuleClick = () => {
    // Simple approach with minimal wrapping
    addTab({
      id: 'add-new-rule',
      title: 'Add New Rule',
      renderArtifact: () => <AddRuleForm onClose={() => {
        setActiveTabId(null);
        removeTab('add-new-rule');
        handleRetry();
      }} />
    });
    // Make sure to set these after adding the tab
    setActiveTabId('add-new-rule');
    setCollapsed(false);
  };

  // Render error state
  if (error) {
    return (
      <motion.div 
        className="flex flex-col items-center justify-center h-[600px] p-6 text-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
      >
        <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
        <h2 className="text-xl font-semibold mb-2">Error Loading Rules</h2>
        <p className="text-muted-foreground mb-4">{error}</p>
        <Button onClick={handleRetry} className="flex items-center gap-2">
          <RefreshCw className="h-4 w-4" />
          Try Again
        </Button>
      </motion.div>
    );
  }

  return (
    <motion.div 
      className="w-full space-y-6 p-2"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div variants={itemVariants} className="flex items-center justify-between">
        <div className="w-full">
          <MultiSelect
            options={fraudTypeOptions}
            onValueChange={setSelectedTypes}
            defaultValue={selectedTypes}
            placeholder="Select fraud types..."
          />
        </div>
        
        {!isLoading && (
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleRetry} 
            className="ml-2 flex items-center gap-1"
            title="Refresh rules"
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
        )}

        <Button 
          variant="outline" 
          size="sm" 
          onClick={handleAddRuleClick} 
          className="ml-2 flex items-center gap-1"
          title="Add new rule"
        >
          <Plus className="h-4 w-4" />
          Add Rule
        </Button>
      </motion.div>

      {isLoading ? (
        <motion.div 
          variants={itemVariants}
          className="flex flex-col items-center justify-center h-[600px]"
        >
          <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
          <span>Loading rules...</span>
        </motion.div>
      ) : rules.data.length === 0 ? (
        <motion.div 
          variants={itemVariants}
          className="flex flex-col items-center justify-center h-[600px] text-center"
        >
          <AlertTriangle className="h-12 w-12 text-yellow-500 mb-4" />
          <h2 className="text-xl font-semibold mb-2">No Rules Found</h2>
          <p className="text-muted-foreground">
            No rules are available. This could be due to API issues or missing data.
          </p>
          <Button onClick={handleRetry} className="mt-4 flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        </motion.div>
      ) : filteredRules.length === 0 ? (
        <motion.div 
          variants={itemVariants}
          className="flex flex-col items-center justify-center h-[600px] text-center"
        >
          <AlertTriangle className="h-12 w-12 text-yellow-500 mb-4" />
          <h2 className="text-xl font-semibold mb-2">No Matching Rules</h2>
          <p className="text-muted-foreground">
            There are no rules matching the selected fraud types.
          </p>
        </motion.div>
      ) : (
        <motion.div variants={itemVariants} className="w-full">
          <VirtualList
            items={virtualItems}
            onItemClick={handleItemClick}
            itemHeight={100}
            viewportHeight={600}
            itemsPerPage={pageSize}
            className="w-full"
          />
        </motion.div>
      )}
      
      {/* Pagination controls - only show when there are items */}
      {filteredRules.length > 0 && (
        <motion.div 
          variants={itemVariants}
          className="flex justify-center items-center gap-4 mt-6 pb-4"
        >
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handlePrevPage} 
            disabled={currentPage === 0 || isLoading}
            className="flex items-center gap-1 px-4"
          >
            {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-1" /> : <ChevronLeft className="h-4 w-4" />}
            Previous
          </Button>
          <div className="flex items-center bg-gray-100 px-3 py-1 rounded-md">
            <span className="text-sm font-medium">Page {currentPage + 1}</span>
          </div>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleNextPage}
            disabled={rules.data.length < pageSize || isLoading}
            className="flex items-center gap-1 px-4"
          >
            Next
            {isLoading ? <Loader2 className="h-4 w-4 animate-spin ml-1" /> : <ChevronRight className="h-4 w-4" />}
          </Button>
        </motion.div>
      )}
    </motion.div>
  );
}; 