import { FC, useEffect } from 'react';
import { useRulesRepoStore } from '@/app/store/ruleRepo/RulesRepoStore';
import { motion } from 'framer-motion';
import { Brain } from 'lucide-react';
import { TabSectionHeading } from '@/components/custom/TabSectionHeading';
import { LlmMetricsList } from '../Metrics/components';

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 }
};

export const AnomalyDetectionTab: FC = () => {
  const { llmMetrics, getLlmMetrics } = useRulesRepoStore();

  useEffect(() => {
    getLlmMetrics();
  }, [getLlmMetrics]);

  return (
    <motion.div
      className="space-y-6 p-4"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div variants={itemVariants}>
        <TabSectionHeading icon={Brain} iconColorClass="text-purple-500">
          LLM Anomaly Detection
        </TabSectionHeading>
        <LlmMetricsList metrics={llmMetrics.data} />
      </motion.div>
    </motion.div>
  );
};

export default AnomalyDetectionTab;
