export type CaseEventType = 'trigger' | 'status_change' | 'document' | 'communication' | 'note' | 'account_status' | 'others';
export type CaseChannel = 'Partner Bank' | 'MHA Portal' | 'Complaint' | 'LEA' | 'Internal';
export type CaseStatus = 'Open' | 'In Progress' | 'Closed';
export type CasePriority = 'High' | 'Medium' | 'Low';

export interface CaseEvent {
  id: string;
  timestamp: string;
  type: CaseEventType;
  description: string;
  user?: string;
  metadata: Record<string, any>;
  content?: string;
}

export interface Investigation {
  id: string;
  caseNumber: string;
  title: string;
  description: string;
  status: CaseStatus;
  priority: CasePriority;
  assignee: string;
  merchantId: string;
  merchantName: string;
  // queueType: string;
  channel: CaseChannel;
  // type: CaseType;
  createdAt: string;
  lastUpdated: string;
  slaDeadline: string;
  events: CaseEvent[];
}