import { FC, useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { TimelineHeader } from './components/TimelineHeader';
import { TimelineFilters } from './components/TimelineFilters';
import { VirtualList } from '@/components/custom/VirtualList';
import { useInvestigationHubStore } from '@/app/store/caseManagement/InvestigationHubStore';
import { useInvestigationIDStore } from '@/app/store/caseManagement/InvestigationIDStore';
import { useActiveContextStore } from '@/app/store/activeContextStore';
import { useArtifactStore } from '@/app/store/artifact/artifactStore';
import { getFilteredItems, handleItemClick } from './utils/timelineUtils';
import { AddCaseNoteArtifact } from './components/AddCaseNoteArtifact';
import { NewEmailArtifact } from './components/NewEmailArtifact';
import { useEmailStore } from '@/app/store/emails/emailStore';
import { useTestingStore } from '@/app/store/testing/testingStore';
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: {
      duration: 0.3
    }
  }
};

export function UnifiedCaseTimeline() {
  const { activeContexts } = useActiveContextStore();
  const { caseEvents, fetchCaseEvents } = useInvestigationHubStore();
  const { selectedinvestigationId, selectedInvestigation } = useInvestigationIDStore();
  const { addTab, setActiveTabId, setCollapsed } = useArtifactStore();
  const [selectedTypes, setSelectedTypes] = useState<string[]>(['others', 'note', 'communication', 'document', 'status_change', 'trigger']);
  const { threads, fetchEmails, loadingStates } = useEmailStore();
  const [isLoading, setIsLoading] = useState(false);
  const { mymail, setMymail } = useTestingStore();

  console.log("UnifiedCaseTimeline rendered - active case:", activeContexts?.case);
  console.log("Selected investigation ID:", selectedinvestigationId);
  console.log("Selected investigation:", selectedInvestigation);

  useEffect(() => {
    console.log("loadingStates", loadingStates)
  }, [loadingStates])

  useEffect(() => {
    async function fetchData() {
      setIsLoading(true);
      try {
        if (selectedinvestigationId) {
          console.log("Fetching data for investigation ID:", selectedinvestigationId);
          await Promise.all([
            fetchCaseEvents(selectedinvestigationId),
            // fetchEmails(selectedInvestigation?.merchant_email || '')
            fetchEmails(mymail)
          ]);
          console.log("Data fetched successfully for investigation");
        } else {
          console.warn("No investigation ID available to fetch data");
        }
      } catch (error) {
        console.error('Error fetching timeline data:', error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchData();
  }, [selectedinvestigationId, fetchCaseEvents, fetchEmails, selectedInvestigation, mymail]);

  if (!activeContexts?.case) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-gray-500">No Case ID Selected</p>
      </div>
    );
  }

  const handleAddNote = () => {
    addTab({
      id: `add-note-${activeContexts.case}`,
      title: 'Add Case Note',
      renderArtifact: () => (
        <AddCaseNoteArtifact
          caseId={selectedinvestigationId || ''}
          onClose={() => setCollapsed(true)}
        />
      )
    });
    setActiveTabId(`add-note-${activeContexts.case}`);
    setCollapsed(false);
  };

  const handleAddCommunication = () => {
    addTab({
      id: `new-email-${activeContexts.case}`,
      title: 'New Communication',
      renderArtifact: () => (
        <NewEmailArtifact
          onClose={() => setCollapsed(true)}
        />
      )
    });
    setActiveTabId(`new-email-${activeContexts.case}`);
    setCollapsed(false);
  };

  return (
    <motion.div 
      className="space-y-6 p-4"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <TimelineHeader 
        caseNumber={selectedInvestigation?.case_number || ''}
        investigationId={selectedInvestigation?.investigation_id || ''}
        summary={selectedInvestigation?.description || ''}
      />

      <TimelineFilters
        selectedTypes={selectedTypes}
        onTypesChange={setSelectedTypes}
        onAddNote={handleAddNote}
        onAddCommunication={handleAddCommunication}
      />

      {isLoading ? (
        <div className="flex justify-center py-8">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="loading-spinner"
          />
        </div>
      ) : (
        <motion.div variants={itemVariants}>
          <VirtualList
            items={getFilteredItems(caseEvents.case_events, threads, selectedTypes)}
            onItemClick={(item) => handleItemClick(item, addTab, setActiveTabId, setCollapsed)}
            itemHeight={90}
            viewportHeight={600}
          />
        </motion.div>
      )}
    </motion.div>
  );
}
