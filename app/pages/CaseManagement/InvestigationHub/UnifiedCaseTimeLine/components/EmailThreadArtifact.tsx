import { FC, useEffect, useState } from 'react';
import { useEmailStore, EmailSummary } from '@/app/store/emails/emailStore';
import { Button } from '@/components/ui/button';
import { Reply, Send, Paperclip, Search } from 'lucide-react';
import { format } from 'date-fns';
import { motion, AnimatePresence } from 'framer-motion';
import { useArtifactStore } from '@/app/store/artifact/artifactStore';
import { NewEmailArtifact } from './NewEmailArtifact';
import { CustomCard } from '@/components/custom/CustomCard';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { LoadingSpinner } from '@/app/components/ui/loading-spinner';
import { EmailQueryArtifact } from './EmailQueryArtifact';
import { useTestingStore } from '@/app/store/testing/testingStore';
import ReactMarkdown from 'react-markdown';
import { cn } from '@/lib/utils';
import { CollapseButton } from '@/components/custom/CollapseButton';
import { ContextMenu, ContextMenuContent, ContextMenuItem, ContextMenuTrigger } from "@/components/ui/context-menu";
import { DocumentQueryArtifact } from './DocumentQueryArtifact';
import { SafeMarkdown } from '@/app/components/SafeMarkdown';

interface EmailThreadArtifactProps {
  threadId: string;
  onClose: () => void;
}

const AttachmentContextMenu: FC<{ 
  attachment: string;
  onOpen: () => void;
  onAnalyze: () => void;
}> = ({ attachment, onOpen, onAnalyze }) => {
  return (
    <ContextMenu>
      <ContextMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="text-xs max-w-[200px]"
        >
          <span className="truncate">{attachment}</span>
        </Button>
      </ContextMenuTrigger>
      <ContextMenuContent>
        <ContextMenuItem onClick={onOpen}>Open Document</ContextMenuItem>
        <ContextMenuItem onClick={onAnalyze}>Analyze Document</ContextMenuItem>
      </ContextMenuContent>
    </ContextMenu>
  );
};

export const EmailThreadArtifact: FC<EmailThreadArtifactProps> = ({ threadId, onClose }) => {
  const { 
    threads, 
    fetchAttachment, 
    getEmailSummary, 
    getThreadSummary,
    loadingStates 
  } = useEmailStore();
  const { addTab, setActiveTabId } = useArtifactStore();
  const { mymail } = useTestingStore();
  
  const thread = threads.find(t => t.thread_id === threadId);
  const [isSummaryExpanded, setIsSummaryExpanded] = useState(false);
  const [localsummary , Setlocalsummary] = useState<EmailSummary | null>(null);

  // Get summary from store
  const emailSummary = getThreadSummary(threadId);

  useEffect(() => {
    const fetchSummary = async () => {
      if (!thread?.emails[0]) return;
      
      // Check if we already have the summary
      const existingSummary = getThreadSummary(threadId);
      if (existingSummary) return;

      try {
        // Fetch and store summary
        await getEmailSummary(mymail, threadId);
      } catch (error) {
        console.error('Error fetching email summary:', error);
      }
    };
    
    fetchSummary();
  }, [threadId, thread, getEmailSummary, getThreadSummary]);

  const handleReply = () => {
    const latestEmail = thread?.emails[thread.emails.length - 1];
    if (!latestEmail) return;

    addTab({
      id: `reply-${threadId}`,
      title: `Reply: ${latestEmail.subject}`,
      renderArtifact: () => (
        <NewEmailArtifact
          onClose={onClose}
          replyTo={{
            subject: latestEmail.subject,
            receiver: latestEmail.sender,
            email_id: latestEmail.id,
          }}
        />
      )
    });
    setActiveTabId(`reply-${threadId}`);
  };

  const handleAttachmentClick = async (filename: string) => {
    try {
        const blob = await fetchAttachment(filename);
        const url = window.URL.createObjectURL(blob);
        window.open(url, '_blank');
        setTimeout(() => window.URL.revokeObjectURL(url), 100);
    } catch (error) {
        console.error('Error downloading attachment:', error);
    }
  };

  const handleOpenQuery = () => {
    if (!thread) return;
    
    addTab({
      id: `query-${threadId}`,
      title: `Analyze: ${thread.emails[0].subject}`,
      renderArtifact: () => (
        <EmailQueryArtifact
          email={mymail}
          threadId={thread.thread_id}
        />
      )
    });
    setActiveTabId(`query-${threadId}`);
  };

  const handleAttachmentAnalyze = (attachment: string, emailId: string) => {
    addTab({
      id: `document-query-${attachment}`,
      title: `Analyze: ${attachment}`,
      renderArtifact: () => (
        <DocumentQueryArtifact
          filename={attachment}
          emailId={emailId}
        />
      )
    });
    setActiveTabId(`document-query-${attachment}`);
  };

  return (
    <div className="space-y-3 p-3 max-w-4xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gray-50 rounded-lg p-3"
      >
        <div className="space-y-2">
          <div className="flex justify-between items-start">
            <div className="space-y-1.5 flex-1">
              <h3 className="text-xs font-medium text-gray-600">Email Summary</h3>
              {loadingStates.gettingEmailSummary ? (
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <LoadingSpinner size="sm" />
                  Generating summary...
                </div>
              ) : emailSummary ? (
                <div className="space-y-2">
                  <div className={cn(
                    "text-xs text-gray-600",
                    !isSummaryExpanded && "line-clamp-3"
                  )}>
                    <SafeMarkdown>{emailSummary.email_summary}</SafeMarkdown>
                  </div>
                  
                  <AnimatePresence>
                    {isSummaryExpanded && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        className="grid grid-cols-2 gap-3 pt-2 border-t border-gray-200"
                      >
                        <div>
                          <p className="text-xs font-medium text-gray-600">Key Topics</p>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {emailSummary.key_topics.map(topic => (
                              <span 
                                key={topic}
                                className="inline-flex items-center px-1.5 py-0.5 rounded text-[10px] font-medium bg-purple-100 text-purple-800"
                              >
                                {topic}
                              </span>
                            ))}
                          </div>
                        </div>
                        
                        <div>
                          <p className="text-xs font-medium text-gray-600">Statistics</p>
                          <div className="mt-1 space-y-0.5 text-xs text-gray-600">
                            <p>Total Emails: {emailSummary.total_emails}</p>
                            <p>Documents: {emailSummary.total_documents}</p>
                            <p>{emailSummary.document_insights}</p>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>

                  <div className="flex justify-center">
                    <CollapseButton
                      isExpanded={isSummaryExpanded}
                      onClick={() => setIsSummaryExpanded(!isSummaryExpanded)}
                    />
                  </div>
                </div>
              ) : null}
            </div>
            <Button
              variant="outline"
              size="sm"
              className="gap-1.5 text-xs font-medium hover:bg-gray-50"
              onClick={handleOpenQuery}
            >
              <Search className="h-3.5 w-3.5" />
              Analyze Email
            </Button>
          </div>
        </div>
      </motion.div>
      
      <CustomCard className="p-3">
        <div className="flex justify-between items-center mb-3">
          <div>
            <h2 className="text-sm font-semibold mb-0.5">
              {thread?.emails[0].subject}
            </h2>
            <p className="text-xs text-gray-500">
              {thread?.emails.length} messages in this conversation
            </p>
          </div>
          <Button 
            onClick={handleReply} 
            variant="outline"
            size="sm"
            className="gap-1.5 text-xs font-medium hover:bg-gray-50"
          >
            <Reply className="h-3.5 w-3.5" />
            Reply
          </Button>
        </div>

        <div className="space-y-4">
          {thread?.emails.map((email, index) => (
            <motion.div
              key={email.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`relative pl-4 ${index !== 0 ? 'border-l border-gray-100' : ''}`}
            >
              <div className="mb-2">
                <div className="flex justify-between items-start mb-1.5">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 rounded-full bg-purple-100 flex items-center justify-center">
                      <span className="text-xs font-medium text-purple-700">
                        {email.sender.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <p className="text-sm font-medium">{email.sender}</p>
                      <p className="text-xs text-gray-500">To: {email.receiver}</p>
                    </div>
                  </div>
                  <span className="text-xs text-gray-500">
                    {format(new Date(email.timestamp), 'MMM dd, yyyy HH:mm')}
                  </span>
                </div>
              </div>

              <div className="pl-8">
                <div className={cn(
                  "prose prose-xs max-w-none text-gray-700",
                  "prose-p:text-xs prose-p:leading-relaxed prose-p:my-1",
                  "prose-headings:text-sm prose-headings:font-medium prose-headings:my-1.5",
                  "prose-ul:my-1 prose-li:my-0.5 prose-li:text-xs"
                )}>
                  {email.content}
                </div>

                {(email.attachments || []).length > 0 && (
                  <div className="mt-4 pt-3 border-t border-gray-100">
                    <div className="flex items-center gap-2 mb-2">
                      <Paperclip className="h-4 w-4 text-gray-400" />
                      <p className="text-sm font-medium text-gray-600">
                        {(email.attachments || []).length} Attachment{(email.attachments || []).length > 1 ? 's' : ''}
                      </p>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {(email.attachments || []).map(attachment => (
                        <AttachmentContextMenu
                          key={attachment}
                          attachment={attachment}
                          onOpen={() => handleAttachmentClick(attachment)}
                          onAnalyze={() => handleAttachmentAnalyze(attachment, email.id)}
                        />
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          ))}
        </div>
      </CustomCard>
    </div>
  );
}; 