import { FC, useState, useEffect } from 'react';
import { useEmailStore } from '@/app/store/emails/emailStore';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Send, Lightbulb } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { LoadingSpinner } from '@/app/components/ui/loading-spinner';
import { ScrollArea } from '@/components/ui/scroll-area';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { cn } from '@/lib/utils';
import { SafeMarkdown } from '@/app/components/SafeMarkdown';

interface EmailQueryArtifactProps {
  email: string;
  threadId: string;
}

interface QueryResponse {
  answer: string;
  has_email_context: boolean;
  document_count: number;
}

export const EmailQueryArtifact: FC<EmailQueryArtifactProps> = ({ email, threadId }) => {
  const { getSuggestedPrompts, queryEmail, loadingStates, getStoredEmailPrompts } = useEmailStore();
  const [customQuery, setCustomQuery] = useState('');
  const [response, setResponse] = useState<QueryResponse | null>(null);

  // Get prompts from store
  const suggestedPrompts = getStoredEmailPrompts(threadId) || [];

  useEffect(() => {
    const fetchPrompts = async () => {
      if (suggestedPrompts.length > 0) return;
      try {
        await getSuggestedPrompts(email, threadId);
      } catch (error) {
        console.error('Error fetching prompts:', error);
      }
    };
    fetchPrompts();
  }, [email, threadId]);

  const handleQuerySubmit = async (prompt: string) => {
    try {
      const result = await queryEmail(email, prompt, threadId);
      setResponse(result);
      setCustomQuery('');
    } catch (error) {
      console.error('Error querying email:', error);
    }
  };

  return (
    <div className="space-y-6 p-4">
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Email Analysis</h2>
        
        {/* Suggested Prompts Section */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium flex items-center gap-2">
            <Lightbulb className="h-4 w-4 text-yellow-500" />
            Suggested Questions
          </h3>
          <div className="grid grid-cols-2 gap-2">
            <AnimatePresence>
              {loadingStates.gettingSuggestedPrompts ? (
                // Loading State
                <>
                  {[1, 2, 3, 4].map((index) => (
                    <motion.div
                      key={`skeleton-${index}`}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="h-[52px] bg-gray-100 rounded-md animate-pulse"
                    />
                  ))}
                </>
              ) : (
                // Loaded Prompts
                suggestedPrompts.map((prompt, index) => (
                  <motion.div
                    key={prompt}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Button
                      variant="outline"
                      className="w-full text-left justify-start h-auto whitespace-normal"
                      onClick={() => setCustomQuery(prompt)}
                    >
                      {prompt}
                    </Button>
                  </motion.div>
                ))
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Custom Query Section */}
        <div className="space-y-2">
          <h3 className="text-sm font-medium">Ask a Custom Question</h3>
          <div className="flex gap-2">
            <Textarea
              value={customQuery}
              onChange={(e) => setCustomQuery(e.target.value)}
              placeholder="Type your question here..."
              className="flex-1"
            />
            <Button 
              onClick={() => handleQuerySubmit(customQuery)}
              disabled={!customQuery.trim() || loadingStates.queryingEmail}
            >
              {loadingStates.queryingEmail ? (
                <LoadingSpinner size="sm" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Response Section */}
        <AnimatePresence>
          {response && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="space-y-4"
            >
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex justify-between items-start mb-4">
                  <h3 className="text-sm font-medium">Analysis Result</h3>
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded-md text-xs">
                      {response.document_count} Documents Analyzed
                    </span>
                    {response.has_email_context && (
                      <span className="px-2 py-1 bg-green-100 text-green-800 rounded-md text-xs">
                        Email Context Available
                      </span>
                    )}
                  </div>
                </div>
                
                <ScrollArea className="max-h-[400px]">
                  <SafeMarkdown 
                    className={cn(
                      "prose prose-sm max-w-none",
                      "prose-p:text-sm prose-p:leading-relaxed prose-p:my-1",
                      "prose-headings:text-base prose-headings:font-medium prose-headings:my-2",
                      "prose-ul:my-1 prose-li:my-0.5 prose-li:text-sm"
                    )}
                    remarkPlugins={[remarkGfm]}
                  >
                    {response.answer}
                  </SafeMarkdown>
                </ScrollArea>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}; 