import { FC, useState, useEffect } from 'react';
import { useEmailStore } from '@/app/store/emails/emailStore';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Send, Lightbulb, FileText } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { LoadingSpinner } from '@/app/components/ui/loading-spinner';
import { ScrollArea } from '@/components/ui/scroll-area';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { cn } from '@/lib/utils';
import { CollapseButton } from '@/components/custom/CollapseButton';
import { SafeMarkdown } from '@/app/components/SafeMarkdown';

interface DocumentQueryArtifactProps {
  filename: string;
  emailId: string;
}

interface DocumentSummary {
  summary: string;
  key_points: string[];
  document_type: string;
  metadata: Record<string, string>;
}

export const DocumentQueryArtifact: FC<DocumentQueryArtifactProps> = ({ 
  filename,
  emailId 
}) => {
  const { 
    fetchEmailDocuments, 
    getDocumentSummary,
    getStoredDocumentSummary,
    getDocumentPrompts,
    getStoredDocumentPrompts,
    queryDocument,
    loadingStates 
  } = useEmailStore();

  const [documentId, setDocumentId] = useState<string>('');
  const [customQuery, setCustomQuery] = useState('');
  const [queryResponse, setQueryResponse] = useState<string | null>(null);
  const [isSummaryExpanded, setIsSummaryExpanded] = useState(false);

  // Get stored data
  const summary = documentId ? getStoredDocumentSummary(documentId) : null;
  const suggestedPrompts = documentId ? getStoredDocumentPrompts(documentId) || [] : [];

  useEffect(() => {
    const initializeDocument = async () => {
      try {
        const result = await fetchEmailDocuments(emailId);
        const doc = result.find((d: any) => d.filename === filename);
        if (doc) {
          setDocumentId(doc.document_id);
          await getDocumentSummary(doc.document_id);
          await getDocumentPrompts(doc.document_id);
        }
      } catch (error) {
        console.error('Error initializing document:', error);
      }
    };
    initializeDocument();
  }, [emailId, filename]);

  const handleQuerySubmit = async (prompt: string) => {
    try {
      const result = await queryDocument(documentId, prompt);
      setQueryResponse(result.answer);
      console.log("queryResponse in document query artifact", result.answer);
      setCustomQuery('');
    } catch (error) {
      console.error('Error querying document:', error);
    }
  };

  console.log('suggestedPrompts:', suggestedPrompts);

  return (
    <div className="space-y-6 p-4">
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <FileText className="h-5 w-5 text-purple-500" />
          <h2 className="text-xl font-semibold">Document Analysis: {filename}</h2>
        </div>

        {/* Document Summary Section with Loading State */}
        <div className="space-y-2">
          <div className="flex justify-between items-start">
            <h3 className="text-sm font-medium">Document Summary</h3>
            {loadingStates.gettingDocumentSummary ? (
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <LoadingSpinner size="sm" />
                Analyzing document...
              </div>
            ) : summary?.document_type && (
              <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-xs">
                {summary.document_type}
              </span>
            )}
          </div>

          <AnimatePresence>
            {loadingStates.gettingDocumentSummary ? (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="space-y-2"
              >
                <div className="h-4 bg-gray-100 rounded animate-pulse w-3/4" />
                <div className="h-4 bg-gray-100 rounded animate-pulse w-2/3" />
                <div className="h-4 bg-gray-100 rounded animate-pulse w-1/2" />
              </motion.div>
            ) : summary && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="bg-gray-50 rounded-lg p-4 space-y-3"
              >
                <div className={cn(
                  "prose prose-sm max-w-none",
                  "prose-p:text-sm prose-p:leading-relaxed prose-p:my-1",
                  "prose-headings:text-base prose-headings:font-medium prose-headings:my-2",
                  "prose-ul:my-1 prose-li:my-0.5 prose-li:text-sm",
                  !isSummaryExpanded && "line-clamp-3"
                )}>
                  <SafeMarkdown>{summary.summary}</SafeMarkdown>
                </div>

                <AnimatePresence>
                  {isSummaryExpanded && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="pt-3 border-t border-gray-200"
                    >
                      <div className="flex flex-wrap gap-2">
                        {summary.key_points.map((point, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-purple-100 text-purple-800 rounded-md text-xs"
                          >
                            {point}
                          </span>
                        ))}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>

                <div className="flex justify-center">
                  <CollapseButton
                    isExpanded={isSummaryExpanded}
                    onClick={() => setIsSummaryExpanded(!isSummaryExpanded)}
                  />
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Suggested Prompts Section with Loading State */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium flex items-center gap-2">
            <Lightbulb className="h-4 w-4 text-yellow-500" />
            Suggested Questions
          </h3>
          <div className="grid grid-cols-2 gap-2">
            <AnimatePresence>
              {loadingStates.gettingDocumentPrompts ? (
                <>
                  {[1, 2, 3, 4].map((index) => (
                    <motion.div
                      key={`skeleton-${index}`}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="h-[52px] bg-gray-100 rounded-md animate-pulse"
                    />
                  ))}
                </>
              ) : (
                suggestedPrompts.map((prompt, index) => (
                  <motion.div
                    key={prompt}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Button
                      variant="outline"
                      className="w-full text-left justify-start h-auto whitespace-normal"
                      onClick={() => setCustomQuery(prompt)}
                    >
                      {prompt}
                    </Button>
                  </motion.div>
                ))
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Custom Query Section */}
        <div className="space-y-2">
          <h3 className="text-sm font-medium">Ask a Custom Question</h3>
          <div className="flex gap-2">
            <Textarea
              value={customQuery}
              onChange={(e) => setCustomQuery(e.target.value)}
              placeholder="Ask a question about this document..."
              className="flex-1"
            />
            <Button 
              onClick={() => handleQuerySubmit(customQuery)}
              disabled={!customQuery.trim() || loadingStates.queryingDocument}
            >
              {loadingStates.queryingDocument ? (
                <LoadingSpinner size="sm" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Response Section */}
        <AnimatePresence>
          {queryResponse && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="space-y-4"
            >
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex justify-between items-start mb-4">
                  <h3 className="text-sm font-medium">Analysis Result</h3>
                </div>
                <ScrollArea className="max-h-[400px]">
                  <div className={cn(
                    "prose prose-sm max-w-none",
                    "prose-p:text-sm prose-p:leading-relaxed prose-p:my-1",
                    "prose-headings:text-base prose-headings:font-medium prose-headings:my-2",
                    "prose-ul:my-1 prose-li:my-0.5 prose-li:text-sm"
                  )}>
                    <SafeMarkdown>{queryResponse}</SafeMarkdown>
                  </div>
                </ScrollArea>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}; 