import { FC } from 'react';
import { MessageSquare } from 'lucide-react';
import { CustomCard } from '@/components/custom/CustomCard';
import { format } from 'date-fns';
import { motion } from 'framer-motion';
import { EmailMessage } from '@/app/store/emails/emailStore';
import { itemVariants } from '../../components/animations';

interface EmailListItemProps {
  email: EmailMessage;
  onClick: () => void;
}

export const EmailListItem: FC<EmailListItemProps> = ({ email, onClick }) => {
  return (
    <motion.div variants={itemVariants}>
      <CustomCard className="p-4 hover:shadow-md transition-shadow" onClick={onClick}>
        <div className="flex items-start space-x-4">
          <div className="p-2 rounded-full text-purple-500 bg-opacity-10">
            <MessageSquare className="h-5 w-5" />
          </div>
          <div className="flex-1 space-y-1">
            <p className="text-sm font-medium">{email.subject}</p>
            <div className="flex items-center text-xs text-gray-500">
              <span>{format(new Date(email.timestamp), 'MMM dd, yyyy HH:mm')}</span>
              <span className="mx-2">•</span>
              <span>Email</span>
            </div>
            {(email.attachments || []).length > 0 && (
              <div className="mt-2 text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <span className="font-medium">Attachments:</span>
                  <span>{(email.attachments || []).length}</span>
                </div>
              </div>
            )}
          </div>
        </div>
      </CustomCard>
    </motion.div>
  );
}; 