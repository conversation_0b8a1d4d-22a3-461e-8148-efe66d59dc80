import { FC } from 'react';
import { FileText, Copy, RefreshCw } from 'lucide-react';
import { CustomCard } from '@/components/custom/CustomCard';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import { EmptyState } from '@/app/pages/Merchant/MerchantInvestigation/Overview/components/EmptyState';

interface TimelineHeaderProps {
  caseNumber: string;
  investigationId: string;
  summary: string | null;
  // onRefresh: () => void;
}

export const TimelineHeader: FC<TimelineHeaderProps> = ({ 
  caseNumber, 
  investigationId, 
  summary, 
  // onRefresh 
}) => {
  const hasSummary = summary && summary.length > 0;

  const copyInvestigationId = () => {
    navigator.clipboard.writeText(investigationId);
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      <CustomCard className="p-4">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-blue-600" />
              <h2 className="text-lg font-semibold text-gray-900">Case #{caseNumber}</h2>
              <div className="flex items-center gap-1 text-sm text-gray-500">
                <span>({investigationId})</span>
                <Copy 
                  className="h-3 w-3 cursor-pointer hover:text-gray-700" 
                  onClick={copyInvestigationId}
                />
              </div>
            </div>
            {/* <Button
              variant="outline"
              size="icon"
              onClick={onRefresh}
              className="shrink-0"
            >
              <RefreshCw className="h-4 w-4" />
            </Button> */}
          </div>
          <div className="text-sm text-gray-600">
            {hasSummary ? summary : <EmptyState message="No summary available" />}
          </div>
        </div>
      </CustomCard>
    </motion.div>
  );
}; 