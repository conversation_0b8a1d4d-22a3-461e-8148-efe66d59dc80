import { FC, useState } from 'react';
import { CustomCard } from '@/components/custom/CustomCard';
import { TabSectionHeading } from '@/components/custom/TabSectionHeading';
import { FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { useInvestigationHubStore } from '@/app/store/caseManagement/InvestigationHubStore';
import { useInvestigationIDStore } from '@/app/store/caseManagement/InvestigationIDStore';
import { caseNote } from '@/app/types';

interface AddCaseNoteArtifactProps {
  caseId: string;
  onClose: () => void;
}

export const AddCaseNoteArtifact: FC<AddCaseNoteArtifactProps> = ({
  caseId,
  onClose,
}) => {
  const [newNote, setNewNote] = useState<caseNote>({
    title: '',
    timestamp: '',
    user: '',
    description: ''
  });
  const { postInvestigationNote, fetchCaseEvents } = useInvestigationHubStore();
  const { selectedinvestigationId } = useInvestigationIDStore();
  const handleAddNote = async () => {
    if (!newNote.title.trim() || !newNote.description.trim()) return;

    const noteToPost: caseNote = {
      title: newNote.title,
      description: newNote.description,
      timestamp: new Date().toISOString(),
      user: 'Admin'
    };

    try {
      await postInvestigationNote(caseId, noteToPost);
      setNewNote({
        title: '',
        timestamp: '',
        user: '',
        description: ''
      });
      if (selectedinvestigationId) {
        await fetchCaseEvents(selectedinvestigationId);
      }
      onClose();
    } catch (error) {
      console.error('Error posting note:', error);
    }
  };

  return (
    <div className="p-6 space-y-6">
      <CustomCard className="p-4">
        <div className="space-y-4">
          <TabSectionHeading icon={FileText} iconColorClass="text-green-500">
            Add Case Note
          </TabSectionHeading>
          
          <div>
            <Input
              placeholder="Title of the note"
              value={newNote.title}
              onChange={(e) => setNewNote({ ...newNote, title: e.target.value })}
              className="w-full"
            />
          </div>
          <div>
            <Textarea
              placeholder="Description of the note"
              value={newNote.description}
              onChange={(e) => setNewNote({ ...newNote, description: e.target.value })}
              className="min-h-[100px]"
            />
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button
              onClick={handleAddNote}
              disabled={!newNote.title.trim() || !newNote.description.trim()}
            >
              Add Note
            </Button>
          </div>
        </div>
      </CustomCard>
    </div>
  );
};