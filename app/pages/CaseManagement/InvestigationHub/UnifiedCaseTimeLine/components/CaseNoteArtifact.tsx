import { FC } from 'react';
import { CustomCard } from '@/components/custom/CustomCard';
import { TabSectionHeading } from '@/components/custom/TabSectionHeading';
import { FileText, User, Clock } from 'lucide-react';
import { format } from 'date-fns';
import { motion } from 'framer-motion';
import { CaseEvent } from '@/app/types';

interface CaseNoteArtifactProps {
  note: CaseEvent;
  onClose: () => void;
}

export const CaseNoteArtifact: FC<CaseNoteArtifactProps> = ({
  note,
  onClose,
}) => {
  return (
    <div className="p-6 space-y-6">
      <CustomCard className="p-4">
        <div className="space-y-4">
          <TabSectionHeading icon={FileText} iconColorClass="text-orange-500">
            Case Note Details
          </TabSectionHeading>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-4"
          >
            {/* Note Title */}
            <div className="space-y-1">
              <h3 className="text-lg font-semibold">{note.content}</h3>
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <Clock className="h-4 w-4" />
                {format(new Date(note.timestamp), 'MMM dd, yyyy HH:mm')}
              </div>
            </div>

            {/* Author Info */}
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <User className="h-4 w-4" />
              <span>Created by: {note?.user || 'Unknown User'}</span>
            </div>

            {/* Note Content */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="prose prose-sm max-w-none">
                {note.description || "no description"}
              </div>
            </div>

            {/* Additional Metadata */}
            {note.meta_data && Object.entries(note.meta_data)
              .filter(([key]) => !['user', 'description'].includes(key))
              .map(([key, value]) => (
                <div key={key} className="flex items-center gap-2 text-sm">
                  <span className="font-medium capitalize">{key.replace('_', ' ')}:</span>
                  <span className="text-gray-600">{String(value)}</span>
                </div>
              ))
            }
          </motion.div>
        </div>
      </CustomCard>
    </div>
  );
}; 