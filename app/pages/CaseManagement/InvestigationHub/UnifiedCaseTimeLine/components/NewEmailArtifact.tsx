import { FC, useState } from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Send, Paperclip, X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useEmailStore } from '@/app/store/emails/emailStore';
import { SuccessNotification } from '@/app/components/notifications/SuccessNotification';
import { ErrorNotification } from '@/app/components/notifications/ErrorNotification';
import { LoadingSpinner } from '@/app/components/ui/loading-spinner';

interface NewEmailArtifactProps {
  onClose: () => void;
  replyTo?: {
    subject: string;
    receiver: string;
    email_id: string;
  };
}

export const NewEmailArtifact: FC<NewEmailArtifactProps> = ({ onClose, replyTo }) => {
  const { sendEmail, sendEmailWithAttachment, replyToEmail, replyToEmailWithAttachment, loadingStates } = useEmailStore();
  
  const [subject, setSubject] = useState(replyTo ? `Re: ${replyTo.subject}` : '');
  const [to, setTo] = useState(replyTo?.receiver || '');
  const [content, setContent] = useState('');
  const [attachment, setAttachment] = useState<File | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleAttachmentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setAttachment(e.target.files[0]);
    }
  };

  const handleSend = async () => {
    console.log("karthik2")
    try {
      setError(null);
      if (replyTo?.email_id) {
        // If this is a reply
        if (attachment) {
          await replyToEmailWithAttachment(replyTo.email_id, subject, content, attachment);
        } else {
          console.log("karthik")
          await replyToEmail(replyTo.email_id, subject, content);
        }
      } else {
        // If this is a new email
        if (attachment) {
          await sendEmailWithAttachment('<EMAIL>', to, subject, content, attachment);
        } else {
          await sendEmail('<EMAIL>', to, subject, content);
        }
      }
      setShowSuccess(true);
      setTimeout(() => {
        setShowSuccess(false);
        onClose();
      }, 2000);
    } catch (error) {
      console.error('Failed to send email:', error);
      setError(error instanceof Error ? error.message : 'Failed to send email');
    }
  };

  return (
    <div className="space-y-4 p-4">
      <motion.div 
        className="space-y-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div>
          <label className="text-sm font-medium">To:</label>
          <Input
            value={to}
            onChange={(e) => setTo(e.target.value)}
            placeholder="<EMAIL>"
            disabled={!!replyTo}
            className="w-full"
          />
        </div>
        
        <div>
          <label className="text-sm font-medium">Subject:</label>
          <Input
            value={subject}
            onChange={(e) => setSubject(e.target.value)}
            placeholder="Enter subject"
            disabled={!!replyTo}
            className="w-full"
          />
        </div>
        
        <div>
          <label className="text-sm font-medium">Message:</label>
          <Textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="Type your message here..."
            className="min-h-[200px]"
          />
        </div>

        <div className="flex items-center gap-4">
          <div className="flex-1">
            {attachment ? (
              <div className="flex items-center gap-2 text-sm">
                <Paperclip className="h-4 w-4" />
                {attachment.name}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setAttachment(null)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ) : (
              <label className="cursor-pointer">
                <Input
                  type="file"
                  className="hidden"
                  onChange={handleAttachmentChange}
                />
                <div className="flex items-center gap-2 text-sm text-gray-500 hover:text-gray-700">
                  <Paperclip className="h-4 w-4" />
                  Attach file
                </div>
              </label>
            )}
          </div>

          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              onClick={handleSend}
              disabled={loadingStates.sendingEmail || !content || (!replyTo && (!subject || !to))}
            >
              <Send className="h-4 w-4 mr-2" />
              {loadingStates.sendingEmail ? 'Sending...' : 'Send'}
            </Button>
          </div>
        </div>
      </motion.div>

      <SuccessNotification
        message="Email sent successfully!"
        isVisible={showSuccess}
        onClose={() => setShowSuccess(false)}
      />

      <ErrorNotification
        message={error || ''}
        isVisible={!!error}
        onClose={() => setError(null)}
      />
    </div>
  );
}; 