import { FC } from 'react';
import { MultiSelect } from '@/components/ui/multi-select2';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import { AlertTriangle, Activity, FileText, MessageSquare, User, Clock } from 'lucide-react';

interface TimelineFiltersProps {
  selectedTypes: string[];
  onTypesChange: (types: string[]) => void;
  onAddNote: () => void;
  onAddCommunication: () => void;
}

export const TimelineFilters: FC<TimelineFiltersProps> = ({
  selectedTypes,
  onTypesChange,
  onAddNote,
  onAddCommunication,
}) => {
  const filterOptions = [
    { value: 'trigger', label: 'Triggers', icon: AlertTriangle },
    { value: 'status_change', label: 'Status Changes', icon: Activity },
    { value: 'document', label: 'Documents', icon: FileText },
    { value: 'communication', label: 'Communications', icon: MessageSquare },
    { value: 'note', label: 'Notes', icon: User },
    { value: 'others', label: 'Others', icon: Clock },
  ];

  return (
    <motion.div 
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.1 }}
      className="space-y-4"
    >
      <div className="flex items-center">
        <MultiSelect
          options={filterOptions}
          onValueChange={onTypesChange}
          defaultValue={selectedTypes}
          placeholder="Select event types..."
          className="min-w-[300px]"
        />
      </div>

      <div className="flex items-center gap-3">
        <Button 
          variant="outline" 
          onClick={onAddCommunication}
          className="flex items-center gap-2"
        >
          <MessageSquare className="h-4 w-4" />
          Add Communication
        </Button>
        <Button 
          variant="outline" 
          onClick={onAddNote}
          className="flex items-center gap-2"
        >
          <FileText className="h-4 w-4" />
          Add Case Note
        </Button>
      </div>
    </motion.div>
  );
}; 