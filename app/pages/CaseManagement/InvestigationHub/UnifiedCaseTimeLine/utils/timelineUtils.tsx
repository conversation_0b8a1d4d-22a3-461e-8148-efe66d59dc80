import { VirtualListItemProps } from '@/components/custom/VirtualList';
import { CaseEvent } from '@/app/types';
import { CustomCard } from '@/components/custom/CustomCard';
import { format } from 'date-fns';
import {
  AlertTriangle,
  Activity,
  FileText,
  MessageSquare,
  User,
  Clock,
} from 'lucide-react';
import { motion } from 'framer-motion';
import { EmailThread, EmailMessage } from '@/app/store/emails/emailStore';
import { EmailListItem } from '../components/EmailListItem';
import { EmailThreadArtifact } from '../components/EmailThreadArtifact';
import { CaseNoteArtifact } from '../components/CaseNoteArtifact';

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: {
      duration: 0.3
    }
  }
};

const getEventIcon = (type: string) => {
  switch (type) {
    case 'trigger':
      return AlertTriangle;
    case 'status_change':
      return Activity;
    case 'document':
      return FileText;
    case 'communication':
      return MessageSquare;
    case 'note':
      return User;
    default:
      return Clock;
  }
};

const getStatusColor = (type: string) => {
  switch (type) {
    case 'trigger':
      return 'text-red-500';
    case 'status_change':
      return 'text-blue-500';
    case 'document':
      return 'text-green-500';
    case 'communication':
      return 'text-purple-500';
    case 'note':
      return 'text-orange-500';
    default:
      return 'text-gray-500';
  }
};

const renderMetadataDetails = (event: CaseEvent) => {
  const Icon = getEventIcon(event.type);
  return (
    <div className="flex items-start space-x-4">
      <div className={`p-2 rounded-full ${getStatusColor(event.type)} bg-opacity-10`}>
        <Icon className="h-5 w-5" />
      </div>
      <div className="flex-1 space-y-1">
        <p className="text-sm font-medium">{event.content}</p>
        <div className="flex items-center text-xs text-gray-500">
          <span>{format(new Date(event.timestamp), 'MMM dd, yyyy HH:mm')}</span>
          <span className="mx-2">•</span>
          <span className="capitalize">{event.type.replace('_', ' ')}</span>
        </div>
        {event.meta_data && (
          <div className="mt-2 text-sm text-gray-600">
            {Object.entries(event.meta_data).map(([key, value]) => (
              <div key={key} className="flex items-center gap-2">
                <span className="font-medium">{key}:</span>
                <span>{String(value)}</span>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export const getFilteredItems = (
  events: CaseEvent[], 
  threads: EmailThread[], 
  selectedTypes: string[]
): VirtualListItemProps[] => {
  let items: VirtualListItemProps[] = [];

  // Add case events
  if (events) {
    items.push(...events
      .filter(event => selectedTypes.includes(event.type))
      .map(event => ({
        id: event.case_event_id,
        title: event.content,
        content: (
          <motion.div variants={itemVariants}>
            <CustomCard className="p-4 hover:shadow-md transition-shadow">
              {renderMetadataDetails(event)}
            </CustomCard>
          </motion.div>
        ),
        metadata: {
          type: event.type,
          data: event,
        }
      })));
  }

  // Add email threads if communications are selected
  if (selectedTypes.includes('communication') && threads) {
    items.push(...threads.map(thread => {
      const firstEmail = thread.emails[0];
      return {
        id: thread.thread_id,
        title: firstEmail.subject,
        content: (
          <EmailListItem
            email={firstEmail}
            onClick={() => {}} // This will be handled by handleItemClick
          />
        ),
        metadata: {
          type: 'email',
          data: thread,
        }
      };
    }));
  }

  // Sort all items by timestamp
  return items.sort((a, b) => {
    const dateA = new Date(
      a.metadata.type === 'email' 
        ? a.metadata.data.emails[0].timestamp 
        : a.metadata.data.timestamp
    );
    const dateB = new Date(
      b.metadata.type === 'email' 
        ? b.metadata.data.emails[0].timestamp 
        : b.metadata.data.timestamp
    );
    return dateB.getTime() - dateA.getTime();
  });
};

export const handleItemClick = (
  item: VirtualListItemProps,
  addTab: (tab: any) => void,
  setActiveTabId: (id: string) => void,
  setCollapsed: (collapsed: boolean) => void
) => {
  if (item.metadata.type === 'email') {
    const thread = item.metadata.data as EmailThread;
    addTab({
      id: `email-${thread.thread_id}`,
      title: thread.emails[0].subject,
      renderArtifact: () => (
        <EmailThreadArtifact
          threadId={thread.thread_id}
          onClose={() => setCollapsed(true)}
        />
      )
    });
    setActiveTabId(`email-${thread.thread_id}`);
  } else if (item.metadata.type === 'note') {
    const note = item.metadata.data as CaseEvent;
    addTab({
      id: `note-${note.case_event_id}`,
      title: note.content,
      renderArtifact: () => (
        <CaseNoteArtifact
          note={note}
          onClose={() => setCollapsed(true)}
        />
      )
    });
    setActiveTabId(`note-${note.case_event_id}`);
  }
  setCollapsed(false);
}; 