import { FC } from 'react';
import { useActiveContext } from '@/app/layout/ActiveContext/useActiveContext';
import { CustomCard } from '@/components/custom/CustomCard';
import { TabSectionHeading } from '@/components/custom/TabSectionHeading';
import { Link } from 'lucide-react';

export const RelatedCasesTab: FC = () => {
  const { activeContexts } = useActiveContext();
  const caseId = activeContexts?.case;

  if (!caseId) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-gray-500">No Case ID Selected</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold">Case #{caseId}</h2>
      </div>

      <div className="space-y-2">
        <TabSectionHeading icon={Link} iconColorClass="text-purple-500">
          Related Cases
        </TabSectionHeading>
        <CustomCard className="p-4">
          <p className="text-sm text-gray-500">Related cases list will be implemented here</p>
        </CustomCard>
      </div>
    </div>
  );
}; 