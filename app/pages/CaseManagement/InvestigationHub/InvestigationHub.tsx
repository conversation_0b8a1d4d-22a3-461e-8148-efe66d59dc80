import { FC, useEffect } from 'react';
import { Workspace } from '@/app/layout/Workspace/Workspace';
import { RelatedCasesTab } from './RelatedCasesTab';
// import { useActiveContext } from '@/app/layout/ActiveContext/useActiveContext';
import { UnifiedCaseTimeline } from './UnifiedCaseTimeLine/UnifiedCaseTimeline';
// import { EmailCommunicationTab } from './EmailCommunicationTab';
// import { CheckPerfomancepage } from './CheckPerfomancepage';

const InvestigationHub: FC = () => {
  // const { activeContexts, handleSelect } = useActiveContext();
  // const caseId = activeContexts?.case;

  // useEffect(() => {
  //   if (caseId) {
  //     console.log('Case selected:', caseId);
  //   }
  // }, []);

  const tabs = [
    {
      id: 'investigation-timeline',
      label: 'Investigation Timeline',
      content: <UnifiedCaseTimeline />
    },    
    {
      id: 'related-cases',
      label: 'Related Cases',
      content: <RelatedCasesTab />
    },
    // {
    //   id: 'email-communication',
    //   label: 'Email Communication',
    //   content: <EmailCommunicationTab />
    // },
    // {
    //   id: 'check-performance',
    //   label: 'Check Performance',
    //   content: <CheckPerfomancepage />
    // }
  ];

  console.log('investigation hub rendering');
  return <Workspace tabs={tabs} />;
};

export default InvestigationHub; 