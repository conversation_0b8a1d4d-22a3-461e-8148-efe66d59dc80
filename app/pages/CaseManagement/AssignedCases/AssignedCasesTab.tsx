import { FC, useEffect } from 'react';
import { CustomCard } from '@/components/custom/CustomCard';
import { TabSectionHeading } from '@/components/custom/TabSectionHeading';
import { UserCheck, Clock, AlertTriangle } from 'lucide-react';
import { BubbleTag } from '@/components/custom/BubbleTag';
import StandardList from '@/components/custom/StandardList';
import { CaseInvestigationType } from '@/app/types';
import { useActiveContext } from '@/app/layout/ActiveContext/useActiveContext';
import { useAssignedCasesStore } from '@/app/store/caseManagement/AssignedCasesStore';
import { useInvestigationIDStore } from '@/app/store/caseManagement/InvestigationIDStore';

interface CaseArtifact extends CaseInvestigationType {
  renderArtifact: () => React.ReactNode;
}

export const AssignedCasesTab: FC = () => {
  const { selectedInvestigation } = useInvestigationIDStore();
  const { investigatorDetails, fetchInvestigatorDetails, assignedCases, fetchAssignedCases } = useAssignedCasesStore();
  const { handleSelect } = useActiveContext();

  useEffect(() => {
    if (selectedInvestigation) {
      console.log("Selected Investigation details", selectedInvestigation);
      console.log("Selected Investigation email", selectedInvestigation.assignee_Email);
      fetchInvestigatorDetails(selectedInvestigation?.assignee_Email || '');
      fetchAssignedCases(selectedInvestigation?.assignee_Email || '');
    }
  }, [selectedInvestigation]);

  useEffect(() => {
    console.log("Investigator Details", investigatorDetails);
    console.log("Assigned Cases", assignedCases);
  }, [assignedCases, investigatorDetails]);

  return (
    <div className="space-y-6">

  {/* Summary Cards */}
  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
    <CustomCard className="p-4">
      <div className="flex items-center gap-2 mb-2">
        <UserCheck className="h-5 w-5 text-blue-500" />
        <h3 className="font-medium">Active Cases</h3>
      </div>
      <p className="text-2xl font-semibold">{investigatorDetails?.current_caseload || 0}</p>
      <p className="text-sm text-gray-500">Currently assigned</p>
    </CustomCard>

    <CustomCard className="p-4">
      <div className="flex items-center gap-2 mb-2">
        <AlertTriangle className="h-5 w-5 text-red-500" />
        <h3 className="font-medium">High Priority</h3>
      </div>
      <p className="text-2xl font-semibold">
        {assignedCases?.filter(c => c.priority === 'High').length || 0}
      </p>
      <p className="text-sm text-gray-500">Requires attention</p>
    </CustomCard>

    <CustomCard className="p-4">
      <div className="flex items-center gap-2 mb-2">
        <Clock className="h-5 w-5 text-yellow-500" />
        <h3 className="font-medium">SLA Adherence</h3>
      </div>
      <p className="text-2xl font-semibold">
        {investigatorDetails?.SLA_adherence_percentage || 0}%
      </p>
      <p className="text-sm text-gray-500">On-time completion rate</p>
    </CustomCard>
  </div>

      {/* Cases List */}
      <div className="space-y-2">
        <TabSectionHeading icon={UserCheck} iconColorClass="text-blue-500">
          My Cases
        </TabSectionHeading>
        <StandardList
          items={assignedCases?.map(caseItem => ({
            id: caseItem.id,
            title: caseItem.title,
            content: (
              <div className="flex items-center justify-between p-2">
                <div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">#{caseItem.case_number}</span>
                    <BubbleTag text={caseItem.priority} color={
                      caseItem.priority === 'High' ? 'red' :
                      caseItem.priority === 'Medium' ? 'yellow' : 'blue'
                    } />
                    {/* <BubbleTag text={caseItem.queueType} color="orange" /> */}
                    <BubbleTag text={caseItem.status} color="blue" />
                  </div>
                  <p className="text-sm text-gray-500">{caseItem.merchant_name}</p>
                </div>
                <div className="text-sm text-gray-500">
                  SLA: {new Date(caseItem.sla_deadline).toLocaleDateString()}
                </div>
              </div>
            ),
            metadata: {
              ...caseItem,
              renderArtifact: () => (
                <div className="p-4">
                  <h3 className="text-lg font-semibold mb-4">Add Investigation Notes</h3>
                  <div className="space-y-2">
                    <p><span className="font-medium">Case Number:</span> {caseItem.case_number}</p>
                    <p><span className="font-medium">Title:</span> {caseItem.title}</p>
                    <p><span className="font-medium">Priority:</span> {caseItem.priority}</p>
                    {/* <p><span className="font-medium">Queue Type:</span> {caseItem.queueType}</p> */}
                    <p><span className="font-medium">Merchant:</span> {caseItem.merchant_name}</p>
                    <p><span className="font-medium">Created:</span> {new Date(caseItem.created_at).toLocaleString()}</p>
                    <p><span className="font-medium">Last Updated:</span> {new Date(caseItem.last_updated).toLocaleString()}</p>
                    <p><span className="font-medium">SLA Deadline:</span> {new Date(caseItem.sla_deadline).toLocaleString()}</p>
                  </div>
                </div>
              )
            } as CaseArtifact,
            // onClick: () => handleSelect('case', caseItem.id)
          })) || []}
          className="space-y-2"
        />
      </div>
    </div>
  );
};
