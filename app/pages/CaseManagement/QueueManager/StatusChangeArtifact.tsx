import { FC, useEffect } from 'react';
import { CustomCard } from '@/components/custom/CustomCard';
import { Button } from '@/components/ui/button';
import { useCaseManagementStore } from '@/app/store/caseManagement/QueueManagerStore';

interface StatusChangeArtifactProps {
  selectedCases: string[];
  onClose: () => void;
}

export const StatusChangeArtifact: FC<StatusChangeArtifactProps> = ({
  selectedCases,
  onClose,
}) => {
  const { allInvestigations, fetchAllInvestigations, postUpdateCase } = useCaseManagementStore();

  useEffect(() => {
    console.log("selectedCases chayan", selectedCases);
  }, [selectedCases]);

  const selectedCasesInfo = allInvestigations?.investigations.filter(
    inv => selectedCases.includes(inv.investigation_id)
  );

  const handleStatusChange = async (newStatus: 'closed' | 'open/inprogress') => {
    console.log("newStatus chayan", newStatus);
    
    const updatePromises = selectedCasesInfo?.map(caseInfo => {
      let status: string = newStatus;
      if (newStatus === 'open/inprogress') {
        status = caseInfo.assignee_Email ? 'in progress' : 'open';
      }
      console.log(`Changing status of case ${caseInfo.case_number} to ${status}`);
      return postUpdateCase(caseInfo.investigation_id, {
        assignee_Name: caseInfo.assignee_Name,
        assignee_Email: caseInfo.assignee_Email,
        status: status
      });
    }) || [];

    await Promise.all(updatePromises);
    await fetchAllInvestigations();
    

    onClose();
  };
  
  return (
    <div className="p-6 space-y-6">
      <CustomCard className="p-4">
        <h3 className="font-medium mb-4">Selected Cases Summary</h3>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-500">Total Cases:</span>
            <span>{selectedCases.length}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-500">Assigned Cases:</span>
            <span>
              {selectedCasesInfo?.filter(c => c.assignee_Email).length || 0}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-500">Unassigned Cases:</span>
            <span>
              {selectedCasesInfo?.filter(c => !c.assignee_Email).length || 0}
            </span>
          </div>
        </div>
      </CustomCard>

      <div className="flex justify-end gap-2">
        {/* <Button variant="outline" onClick={() => {
          setArtifact(null);
          onClose();
        }}>
          Cancel
        </Button> */}
        <Button
          variant="secondary"
          onClick={() => handleStatusChange('open/inprogress')}
        >
          Open / In Progress
        </Button>
        <Button
          variant="default"
          onClick={() => handleStatusChange('closed')}
        >
          Closed
        </Button>
      </div>
    </div>
  );
};