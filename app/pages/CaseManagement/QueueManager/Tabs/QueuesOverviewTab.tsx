import { FC, useEffect } from 'react';
import { CustomCard } from '@/components/custom/CustomCard';
import { TabSectionHeading } from '@/components/custom/TabSectionHeading';
import { QueueMetrics } from '../types';
import { AlertTriangle, Clock, ListChecks } from 'lucide-react';
import { BubbleTag } from '@/components/custom/BubbleTag';


const queueMetrics: QueueMetrics[] = [
  {
    queueType: 'Fraud',
    totalCases: 45,
    highPriority: 12,
    nearingSLA: 8,
    avgResolutionTime: '2d 4h',
    slaBreachRisk: 15
  },
  {
    queueType: 'Compliance',
    totalCases: 32,
    highPriority: 8,
    nearingSLA: 5,
    avgResolutionTime: '3d 6h',
    slaBreachRisk: 10
  },
  {
    queueType: 'Customer Complaint',
    totalCases: 28,
    highPriority: 6,
    nearingSLA: 4,
    avgResolutionTime: '1d 8h',
    slaBreachRisk: 8
  },
  {
    queueType: 'Document Review',
    totalCases: 56,
    highPriority: 15,
    nearingSLA: 12,
    avgResolutionTime: '4d 2h',
    slaBreachRisk: 20
  }
];

export const QueuesOverviewTab: FC = () => {

  useEffect(() => {
    console.log("QueuesOverviewTab mounted");
  }, []);

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <CustomCard className="p-4">
          <div className="flex items-center gap-2 mb-2">
            <ListChecks className="h-5 w-5 text-blue-500" />
            <h3 className="font-medium">Total Active Cases</h3>
          </div>
          <p className="text-2xl font-semibold">161</p>
          <p className="text-sm text-gray-500">Across all queues</p>
        </CustomCard>

        <CustomCard className="p-4">
          <div className="flex items-center gap-2 mb-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            <h3 className="font-medium">High Priority</h3>
          </div>
          <p className="text-2xl font-semibold">41</p>
          <p className="text-sm text-gray-500">Requires immediate attention</p>
        </CustomCard>

        <CustomCard className="p-4">
          <div className="flex items-center gap-2 mb-2">
            <Clock className="h-5 w-5 text-yellow-500" />
            <h3 className="font-medium">Nearing SLA</h3>
          </div>
          <p className="text-2xl font-semibold">29</p>
          <p className="text-sm text-gray-500">Due within 24 hours</p>
        </CustomCard>
      </div>

      {/* Queue Details */}
      <div className="space-y-2">
        <TabSectionHeading icon={ListChecks} iconColorClass="text-blue-500">
          Queue Details
        </TabSectionHeading>
        <div className="space-y-4">
          {queueMetrics.map((queue) => (
            <CustomCard key={queue.queueType} className="p-4">
              <div className="flex justify-between items-start">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="font-medium">{queue.queueType}</h3>
                    <BubbleTag
                      text={`${queue.slaBreachRisk}% SLA Risk`}
                      color={queue.slaBreachRisk > 15 ? 'red' : 'yellow'}
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-x-8 gap-y-2 text-sm">
                    <div>
                      <span className="text-gray-500">Total Cases:</span>
                      <span className="ml-2 font-medium">{queue.totalCases}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">High Priority:</span>
                      <span className="ml-2 font-medium">{queue.highPriority}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Nearing SLA:</span>
                      <span className="ml-2 font-medium">{queue.nearingSLA}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Avg. Resolution:</span>
                      <span className="ml-2 font-medium">{queue.avgResolutionTime}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CustomCard>
          ))}
        </div>
      </div>
    </div>
  );
}; 