import { FC, useEffect } from 'react';
import { CustomCard } from '@/components/custom/CustomCard';
import { TabSectionHeading } from '@/components/custom/TabSectionHeading';
import { Clock, AlertTriangle, CheckCircle } from 'lucide-react';
import { BubbleTag } from '@/components/custom/BubbleTag';

interface SLAMetrics {
  category: string;
  totalCases: number;
  withinSLA: number;
  atRisk: number;
  breached: number;
  adherenceRate: number;
}

const slaMetrics: SLAMetrics[] = [
  {
    category: 'High Priority Cases',
    totalCases: 45,
    withinSLA: 32,
    atRisk: 8,
    breached: 5,
    adherenceRate: 71
  },
  {
    category: 'Medium Priority Cases',
    totalCases: 78,
    withinSLA: 65,
    atRisk: 10,
    breached: 3,
    adherenceRate: 83
  },
  {
    category: 'Low Priority Cases',
    totalCases: 38,
    withinSLA: 35,
    atRisk: 2,
    breached: 1,
    adherenceRate: 92
  }
];

export const SLAMonitoringTab: FC = () => {

  useEffect(() => {
    console.log("CaseAssignmentTab mounted");
  }, []);
  
  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <CustomCard className="p-4">
          <div className="flex items-center gap-2 mb-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            <h3 className="font-medium">Within SLA</h3>
          </div>
          <p className="text-2xl font-semibold text-green-600">132</p>
          <p className="text-sm text-gray-500">Cases on track</p>
        </CustomCard>

        <CustomCard className="p-4">
          <div className="flex items-center gap-2 mb-2">
            <Clock className="h-5 w-5 text-yellow-500" />
            <h3 className="font-medium">At Risk</h3>
          </div>
          <p className="text-2xl font-semibold text-yellow-600">20</p>
          <p className="text-sm text-gray-500">Require attention</p>
        </CustomCard>

        <CustomCard className="p-4">
          <div className="flex items-center gap-2 mb-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            <h3 className="font-medium">SLA Breached</h3>
          </div>
          <p className="text-2xl font-semibold text-red-600">9</p>
          <p className="text-sm text-gray-500">Past deadline</p>
        </CustomCard>
      </div>

      {/* SLA Performance by Category */}
      <div className="space-y-2">
        <TabSectionHeading icon={Clock} iconColorClass="text-blue-500">
          SLA Performance by Category
        </TabSectionHeading>
        <div className="space-y-4">
          {slaMetrics.map((metric) => (
            <CustomCard key={metric.category} className="p-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <h3 className="font-medium">{metric.category}</h3>
                    <BubbleTag
                      text={`${metric.adherenceRate}% Adherence`}
                      color={
                        metric.adherenceRate >= 90 ? 'green' :
                        metric.adherenceRate >= 75 ? 'yellow' : 'red'
                      }
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <p className="text-sm text-gray-500">Total Cases</p>
                    <p className="text-lg font-semibold">{metric.totalCases}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Within SLA</p>
                    <p className="text-lg font-semibold text-green-600">{metric.withinSLA}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">At Risk</p>
                    <p className="text-lg font-semibold text-yellow-600">{metric.atRisk}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Breached</p>
                    <p className="text-lg font-semibold text-red-600">{metric.breached}</p>
                  </div>
                </div>
                {/* Progress bar */}
                <div className="h-2 bg-gray-100 rounded-full overflow-hidden">
                  <div className="h-full flex">
                    <div 
                      className="bg-green-500" 
                      style={{ width: `${(metric.withinSLA / metric.totalCases) * 100}%` }}
                    />
                  </div>
                </div>
              </div>
            </CustomCard>
          ))}
        </div>
      </div>
    </div>
  );
}; 