import { FC, useState, useEffect } from 'react';
import { CustomCard } from '@/components/custom/CustomCard';
import { TabSectionHeading } from '@/components/custom/TabSectionHeading';
import { Case, Investigator } from '../types';
import { UserCheck, Users, AlertTriangle } from 'lucide-react';
import { BubbleTag } from '@/components/custom/BubbleTag';
import StandardList from '@/components/custom/StandardList';
import { Button } from '@/components/ui/button';
import { useCaseManagementStore } from '@/app/store/caseManagement/QueueManagerStore';


const unassignedCases: Case[] = [
  {
    id: '1',
    caseNumber: 'CASE-001',
    title: 'Suspicious Transaction Pattern',
    priority: 'High',
    status: 'Open',
    queueType: 'Fraud',
    createdAt: '2024-03-10T10:00:00',
    lastUpdated: '2024-03-10T10:00:00',
    slaDeadline: '2024-03-12T10:00:00',
    merchantId: 'M123',
    merchantName: 'Tech Solutions Ltd'
  },
  // Add more cases...
];

interface CaseArtifact extends Case {
  renderArtifact: () => React.ReactNode;
}

export const CaseAssignmentTab: FC = () => {
  const { investigators, fetchInvestigators, allInvestigations } = useCaseManagementStore();
  const [selectedInvestigator, setSelectedInvestigator] = useState<string>('');
  const [selectedCases, setSelectedCases] = useState<string[]>([]);

  useEffect(() => {
    fetchInvestigators();
  }, []);

  useEffect(() => {
    console.log("CaseAssignmentTab mounted");
  }, []);

  const handleAssignCases = () => {
    // Implement case assignment logic
    console.log('Assigning cases:', selectedCases, 'to investigator:', selectedInvestigator);
  };

  return (
    <div className="space-y-6">
      {/* Investigators Section */}
      <div className="space-y-2">
        <TabSectionHeading icon={Users} iconColorClass="text-blue-500">
          Available Investigators
        </TabSectionHeading>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {investigators?.investigators.map((investigator) => (
            <CustomCard
              key={investigator.id}
              className={`p-4 cursor-pointer hover:bg-gray-50 ${
                selectedInvestigator === investigator.id ? 'ring-2 ring-blue-500' : ''
              }`}
              onClick={() => setSelectedInvestigator(investigator.id)}
            >
              <div className="flex items-center gap-2 mb-2">
                <UserCheck className="h-5 w-5 text-blue-500" />
                <h3 className="font-medium">{investigator.name}</h3>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500">Current Caseload:</span>
                  <span>{investigator.current_caseload}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Expertise:</span>
                  <span>{investigator.expertise}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">SLA Adherence:</span>
                  <span>{investigator.SLA_adherence_percentage}%</span>
                </div>
              </div>
            </CustomCard>
          ))}
        </div>
      </div>

      {/* Unassigned Cases Section */}
      <div className="space-y-2">
        <TabSectionHeading icon={AlertTriangle} iconColorClass="text-red-500">
          Unassigned Cases
        </TabSectionHeading>
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setSelectedCases([])}
                disabled={selectedCases.length === 0}
              >
                Clear Selection
              </Button>
              <Button
                onClick={handleAssignCases}
                disabled={!selectedInvestigator || selectedCases.length === 0}
              >
                Assign Selected Cases
              </Button>
            </div>
          </div>
          <StandardList
            items={unassignedCases.map(caseItem => ({
              id: caseItem.id,
              title: caseItem.title,
              content: (
                <div className="flex items-center justify-between p-2">
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">#{caseItem.caseNumber}</span>
                      <BubbleTag text={caseItem.priority} color={
                        caseItem.priority === 'High' ? 'red' :
                        caseItem.priority === 'Medium' ? 'yellow' : 'blue'
                      } />
                      <BubbleTag text={caseItem.queueType} color="orange" />
                    </div>
                    <p className="text-sm text-gray-500">{caseItem.merchantName}</p>
                  </div>
                  <div className="text-sm text-gray-500">
                    Created: {new Date(caseItem.createdAt).toLocaleDateString()}
                  </div>
                </div>
              ),
              selected: selectedCases.includes(caseItem.id),
              metadata: {
                ...caseItem,
                renderArtifact: () => (
                  <div className="p-4">
                    <h3 className="text-lg font-semibold mb-4">Case Details</h3>
                    <div className="space-y-2">
                      <p><span className="font-medium">Case Number:</span> {caseItem.caseNumber}</p>
                      <p><span className="font-medium">Title:</span> {caseItem.title}</p>
                      <p><span className="font-medium">Priority:</span> {caseItem.priority}</p>
                      <p><span className="font-medium">Queue Type:</span> {caseItem.queueType}</p>
                      <p><span className="font-medium">Merchant:</span> {caseItem.merchantName}</p>
                      <p><span className="font-medium">Created:</span> {new Date(caseItem.createdAt).toLocaleString()}</p>
                      <p><span className="font-medium">Last Updated:</span> {new Date(caseItem.lastUpdated).toLocaleString()}</p>
                      <p><span className="font-medium">SLA Deadline:</span> {new Date(caseItem.slaDeadline).toLocaleString()}</p>
                    </div>
                  </div>
                )
              } as CaseArtifact
            }))}
            onItemClick={(item) => {
              setSelectedCases(prev =>
                prev.includes(item.id)
                  ? prev.filter(id => id !== item.id)
                  : [...prev, item.id]
              );
            }}
            className="space-y-2"
          />
        </div>
      </div>
    </div>
  );
};
