import { FC, useEffect } from 'react';
import { Workspace } from '@/app/layout/Workspace/Workspace';
import { QueuesOverviewTab } from './Tabs/QueuesOverviewTab';
import { CaseAssignmentTab } from './Tabs/CaseAssignmentTab';
import { SLAMonitoringTab } from './Tabs/SLAMonitoringTab';
import { UnifiedQueueTab } from './UnifiedQueueTab';
import { useCaseManagementStore } from '@/app/store/caseManagement/QueueManagerStore';    

const QueueManager: FC = () => {
  

  const tabs = [
    // {
    //   id: 'queues-overview',
    //   label: 'Queues Overview',
    //   content: <QueuesOverviewTab />
    // },
    // {
    //   id: 'case-assignment',
    //   label: 'Case Assignment',
    //   content: <CaseAssignmentTab />
    // },
    // {
    //   id: 'sla-monitoring',
    //   label: 'SLA Monitoring',
    //   content: <SLAMonitoringTab />
    // },
    {
      id: 'unified-queue',
      label: 'Unified Queue',
      content: <UnifiedQueueTab />
    }
  ];

  return <Workspace tabs={tabs} />;
};

export default QueueManager;
