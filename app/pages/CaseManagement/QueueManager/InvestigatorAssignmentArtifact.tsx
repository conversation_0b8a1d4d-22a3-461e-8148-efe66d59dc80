// Create new file: aphrodite/app/pages/CaseManagement/QueueManager/InvestigatorAssignmentArtifact.tsx
import { FC, useState, useEffect } from 'react';
import { CustomCard } from '@/components/custom/CustomCard';
import { UserCheck } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useCaseManagementStore } from '@/app/store/caseManagement/QueueManagerStore';
import { useWorkspace } from "@/app/layout/Workspace/WorkspaceContext";

interface InvestigatorAssignmentArtifactProps {
  selectedCases: string[];
  onClose: () => void;
}

export const InvestigatorAssignmentArtifact: FC<InvestigatorAssignmentArtifactProps> = ({
  selectedCases,
  onClose,
}) => {
  const { investigators, fetchInvestigators, postUpdateCase, allInvestigations, fetchAllInvestigations } = useCaseManagementStore();
  const [selectedInvestigator, setSelectedInvestigator] = useState<string>('');
  const { setArtifact } = useWorkspace();

  useEffect(() => {
    fetchInvestigators();
  }, []);

  const selectedInvestigatorInfo = investigators?.investigators.find(
    inv => inv.id === selectedInvestigator
  );

  const handleAssign = async () => {
    const selectedCasesInfo = allInvestigations?.investigations.filter(
      inv => selectedCases.includes(inv.investigation_id)
    );

    const updatePromises = selectedCasesInfo?.map(caseInfo => {
      return postUpdateCase(caseInfo.investigation_id, {
        assignee_Name: selectedInvestigatorInfo?.name || '',
        assignee_Email: selectedInvestigatorInfo?.id || '',
        status: 'in progress'
      });
    }) || [];

    await Promise.all(updatePromises);
    await fetchAllInvestigations();
    
    setArtifact(null);
    onClose();
  };

  return (
    <div className="p-6 space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {investigators?.investigators.map((investigator) => (
          <CustomCard
            key={investigator.id}
            className={`p-4 cursor-pointer hover:bg-gray-50 ${
              selectedInvestigator === investigator.id ? 'ring-2 ring-blue-500' : ''
            }`}
            onClick={() => setSelectedInvestigator(investigator.id)}
          >
            <div className="flex items-center gap-2 mb-2">
              <UserCheck className="h-5 w-5 text-blue-500" />
              <h3 className="font-medium">{investigator.name}</h3>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-500">Current Caseload:</span>
                <span>{investigator.current_caseload}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Expertise:</span>
                <span>{investigator.expertise}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">SLA Adherence:</span>
                <span>{investigator.SLA_adherence_percentage}%</span>
              </div>
            </div>
          </CustomCard>
        ))}
      </div>

      <div className="flex justify-end gap-2">
        <Button variant="outline" onClick={() => {
          setArtifact(null);
          onClose();
        }}>
          Cancel
        </Button>
        <Button
          onClick={handleAssign}
          disabled={!selectedInvestigator}
        >
          Assign Cases
        </Button>
      </div>
    </div>
  );
};