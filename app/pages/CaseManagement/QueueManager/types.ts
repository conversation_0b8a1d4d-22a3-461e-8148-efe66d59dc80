export interface Case {
  id: string;
  caseNumber: string;
  title: string;
  priority: 'High' | 'Medium' | 'Low';
  status: 'Open' | 'In Progress' | 'Pending' | 'Closed';
  assignee?: string;
  queueType: 'Fraud' | 'Compliance' | 'Customer Complaint' | 'Document Review';
  createdAt: string;
  lastUpdated: string;
  slaDeadline: string;
  merchantId: string;
  merchantName: string;
}

export interface QueueMetrics {
  queueType: string;
  totalCases: number;
  highPriority: number;
  nearingSLA: number;
  avgResolutionTime: string;
  slaBreachRisk: number;
}

export interface Investigator {
  id: string;
  name: string;
  currentCaseload: number;
  expertise: ('Fraud' | 'Compliance' | 'Customer Service' | 'Documentation')[];
  availability: 'Available' | 'Busy' | 'Offline';
  performance: {
    avgResolutionTime: string;
    casesResolved: number;
    slaAdherence: number;
  };
} 