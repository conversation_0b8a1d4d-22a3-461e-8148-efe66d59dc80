import { useEffect, useState, useMemo } from "react";
import { useCaseManagementStore } from "@/app/store/caseManagement/QueueManagerStore";
import { CaseInvestigationCard } from "@/components/custom/CaseInvestigationCard";
import { CaseInvestigationSLACard } from "@/components/custom/CaseInvestigationSLACard";
import { CaseInvestigationPriorityCard } from "@/components/custom/CaseInvestigationPriorityCard";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { CircleDollarSign, Plus } from "lucide-react";
import { VirtualList } from "@/components/custom/VirtualList";
import { CaseItem } from "@/app/pages/CaseManagement/QueueManager/CaseItem";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { InvestigatorAssignmentArtifact } from "@/app/pages/CaseManagement/QueueManager/InvestigatorAssignmentArtifact";
import { StatusChangeArtifact } from "@/app/pages/CaseManagement/QueueManager/StatusChangeArtifact";
import { useArtifactStore } from '@/app/store/artifact/artifactStore';
import { useActiveContext } from "@/app/layout/ActiveContext/useActiveContext";

export const UnifiedQueueTab = () => {

  const { fetchAllInvestigations, allInvestigations, fetchInvestigators } = useCaseManagementStore();
  const { addTab, setActiveTabId, setCollapsed } = useArtifactStore();

  useEffect(() => {
    fetchAllInvestigations();
    fetchInvestigators();
    console.log("QueueManager mounted");
  }, []);


  const [status, setStatus] = useState('all');
  const [priority, setPriority] = useState('all');
  const [assignee, setAssignee] = useState('all');  // renamed from merchant
  const [selectedCases, setSelectedCases] = useState<string[]>([]);
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false);
  const { handleSelect } = useActiveContext();


  const filterByAssignee = (selectedAssignee: string, investigationAssigneeEmail: string) => {
    if (selectedAssignee === 'all') return true;
    if (selectedAssignee === 'unassigned') return !investigationAssigneeEmail;
    return investigationAssigneeEmail === selectedAssignee;
  }

  const filterByStatus = (status: string, investigationStatus: string) => {
    return status === 'all' || investigationStatus === status;
  }

  const filterByPriority = (priority: string, investigationPriority: string) => {
    return priority === 'all' || investigationPriority === priority;
  }

  const filteredInvestigations = useMemo(() => allInvestigations?.investigations.filter(investigation => {
    return filterByAssignee(assignee, investigation.assignee_Email)
      && filterByStatus(status, investigation.status.toLowerCase())
      && filterByPriority(priority, investigation.priority.toLowerCase())
  }), [allInvestigations, assignee, status, priority]);

  const getOpenCases = () => {
    return filteredInvestigations?.filter(investigation => investigation.status.toLowerCase() === 'open').length || 0;
  }

  const getInProgressCases = () => {
    return filteredInvestigations?.filter(investigation => investigation.status.toLowerCase() === 'in progress').length || 0;
  }

  const getClosedCases = () => {
    return filteredInvestigations?.filter(investigation => investigation.status.toLowerCase() === 'closed').length || 0;
  }

  const getWithinSLA = () => {
    return filteredInvestigations?.filter(investigation => {
      const slaDeadline = new Date(investigation.sla_deadline);
      const currentDate = new Date();
      return currentDate < slaDeadline;
    }).length || 0;
  }

  const getRiskCases = () => {
    return filteredInvestigations?.filter(investigation => {
      const slaDeadline = new Date(investigation.sla_deadline);
      const currentDate = new Date();
      return currentDate === slaDeadline;
    }).length || 0;
  }

  const getBreachedCases = () => {
    return filteredInvestigations?.filter(investigation => {
      const slaDeadline = new Date(investigation.sla_deadline);
      const currentDate = new Date();
      return currentDate > slaDeadline;
    }).length || 0;
  }

  const getHighPriorityCases = () => {
    return filteredInvestigations?.filter(investigation => investigation.priority.toLowerCase() === 'high').length || 0;
  }

  const getMediumPriorityCases = () => {
    return filteredInvestigations?.filter(investigation => investigation.priority.toLowerCase() === 'medium').length || 0;
  }

  const getLowPriorityCases = () => {
    return filteredInvestigations?.filter(investigation => investigation.priority.toLowerCase() === 'low').length || 0;
  }

  console.log("filteredInvestigations pankaj : ", filteredInvestigations);

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allCases = filteredInvestigations?.map(inv => inv.investigation_id) || [];
      setSelectedCases(allCases);
    } else {
      setSelectedCases([]);
    }
  };

  const virtualListItems = useMemo(() => {
    return filteredInvestigations?.map(investigation => ({
      id: investigation.investigation_id,
      title: investigation.title,
      content: (
        <div className="flex items-center w-full">
          <Checkbox
            checked={selectedCases.includes(investigation.investigation_id)}
            onCheckedChange={(checked) => {
              setSelectedCases(prev =>
                checked
                  ? [...prev, investigation.investigation_id]
                  : prev.filter(id => id !== investigation.investigation_id)
              );
            }}
            className="ml-4"
          />
          <div className="flex-1" onClick={() => handleSelect('case', investigation.case_number, investigation.investigation_id)}>
            <CaseItem investigation={investigation} />
          </div>
        </div>
      ),
      metadata: investigation
    })) || [];
  }, [filteredInvestigations, selectedCases]);

  const handleOpenAssignDialog = () => {
    addTab({
      id: 'investigator-assignment',
      title: 'Assign Cases to Investigator',
      renderArtifact: () => (
        <InvestigatorAssignmentArtifact
          selectedCases={selectedCases}
          onClose={() => setIsAssignDialogOpen(false)}
        />
      )
    });
    setActiveTabId('investigator-assignment');
    setCollapsed(false);
  };

  const handleOpenStatusDialog = () => {
    addTab({
      id: 'status-change',
      title: 'Change Case Status',
      renderArtifact: () => (
        <StatusChangeArtifact
          selectedCases={selectedCases}
          onClose={() => setIsAssignDialogOpen(false)}
        />
      )
    });
    setActiveTabId('status-change');
    setCollapsed(false);
  };

  // useEffect(() => {
  //   if (isAssignDialogOpen) {
  //     console.log("seting artifact chayan");
  //     setArtifact({
  //       id: 'status-change',
  //       title: 'Change Case Status',
  //       renderArtifact: () => (
  //         <StatusChangeArtifact
  //           selectedCases={selectedCases}
  //           onClose={() => setIsAssignDialogOpen(false)}
  //         />
  //       )
  //     });
  //   }
  // }, [selectedCases, isAssignDialogOpen]);

  return (
    <div className="space-y-2">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <CaseInvestigationCard
          title="Case Status"
          icon={CircleDollarSign}
          openCases={getOpenCases()}
          inProgressCases={getInProgressCases()}
          closedCases={getClosedCases()}
        />
        <CaseInvestigationSLACard
          title="SLA Status"
          icon={CircleDollarSign}
          withinSLA={getWithinSLA()}
          riskCases={getRiskCases()}
          breachedCases={getBreachedCases()}
        />
        <CaseInvestigationPriorityCard
          title="Priority Distribution"
          icon={CircleDollarSign}
          highPriorityCases={getHighPriorityCases()}
          mediumPriorityCases={getMediumPriorityCases()}
          lowPriorityCases={getLowPriorityCases()}
        />
      </div>

      {/* Filters */}
      <div className="flex flex-wrap items-center gap-4 bg-gray-50 p-4 rounded-lg">
        <div className="flex flex-1 gap-4">
          <Select value={status} onValueChange={setStatus}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select Status" />
            </SelectTrigger>
            <SelectContent>
              {['all', 'open', 'in progress', 'closed'].map((option) => (
                <SelectItem key={option} value={option}>
                  {option.charAt(0).toUpperCase() + option.slice(1).replace('_', ' ')}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={priority} onValueChange={setPriority}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select Priority" />
            </SelectTrigger>
            <SelectContent>
              {['all', 'high', 'medium', 'low'].map((option) => (
                <SelectItem key={option} value={option}>
                  {option.charAt(0).toUpperCase() + option.slice(1)} Priorities
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={assignee} onValueChange={setAssignee}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select Assignee" />
            </SelectTrigger>
            <SelectContent>
              {['all', 'unassigned', ...new Set(allInvestigations?.investigations
                .map(i => i.assignee_Email)
                .filter(email => email !== ""))
              ].map((option) => (
                <SelectItem key={option} value={option}>
                  {option === 'all' ? 'All Assignees' : 
                   option === 'unassigned' ? 'Unassigned' : 
                   option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex gap-2">
          <Button
            size="sm"
            variant="secondary"
            className="shrink-0 shadow-sm hover:shadow-md transition-shadow"
            onClick={handleOpenStatusDialog}
            disabled={selectedCases.length === 0}
          >
            Change Status
          </Button>
          <Button
            size="sm"
            variant="default"
            className="shrink-0 shadow-sm hover:shadow-md transition-shadow"
            onClick={handleOpenAssignDialog}
            disabled={selectedCases.length === 0}
          >
            <Plus className="h-4 w-4 mr-2" />
            Assign Case
          </Button>
        </div>
      </div>

      {/* Cases List */}
      <div className="bg-white rounded-lg shadow">
        <VirtualList
          items={virtualListItems}
          itemHeight={120}
          viewportHeight={460}
          className="p-2"
        />
      </div>

    </div>
  )
};
