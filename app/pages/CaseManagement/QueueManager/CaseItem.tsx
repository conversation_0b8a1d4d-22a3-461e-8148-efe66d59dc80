import { CaseInvestigationType } from "@/app/types";
import { BubbleTag } from "@/components/custom/BubbleTag";
import { AlertTriangle, Clock } from "lucide-react";

interface CaseItemProps {
  investigation: CaseInvestigationType;
}

type BubbleTagColor = 'red' | 'yellow' | 'green' | 'blue' | 'orange' | 'gray';

export const CaseItem = ({ investigation }: CaseItemProps) => {
  const getSLAStatus = () => {
    const slaDeadline = new Date(investigation.sla_deadline);
    const currentDate = new Date();
    const timeDiff = slaDeadline.getTime() - currentDate.getTime();
    const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

    if (daysDiff < 0) return { text: 'SLA Breached', color: 'red' as BubbleTagColor };
    if (daysDiff === 0) return { text: 'Due Today', color: 'yellow' as BubbleTagColor };
    return { text: 'Within SLA', color: 'green' as BubbleTagColor };
  };

  const getPriorityColor = (priority: string): BubbleTagColor => {
    switch (priority.toLowerCase()) {
      case 'high': return 'red';
      case 'medium': return 'yellow';
      default: return 'blue';
    }
  };

  const slaStatus = getSLAStatus();

  return (
    <div className="flex items-center justify-between p-4">
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <span className="font-medium">#{investigation.case_number}</span>
          <BubbleTag text={investigation.priority} color={getPriorityColor(investigation.priority)} />
          <BubbleTag text={investigation.status} color="blue" />
          <BubbleTag text={slaStatus.text} color={slaStatus.color} />
        </div>
        <p className="text-sm text-gray-500">{investigation.merchant_name}</p>
        <p className="text-sm font-medium">{investigation.title}</p>
      </div>
      <div className="flex flex-col items-end gap-2">
        <div className="flex items-center gap-1 text-sm text-gray-500">
          <Clock className="h-4 w-4" />
          {new Date(investigation.created_at).toLocaleDateString()}
        </div>
        <div className="text-sm text-gray-500">
          Assigned to: {investigation.assignee_Name || 'Unassigned'}
        </div>
      </div>
    </div>
  );
};