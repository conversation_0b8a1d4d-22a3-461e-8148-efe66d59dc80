import { useState } from 'react';
import { Plus, X, Activity, TrendingUp, Wallet, Users, ShoppingCart, Repeat, ArrowDownLeft, Smile, UserPlus } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { visualizationConfig } from "../../../config/visualizationConfig";

interface StatItem {
  key: string;
  label: string;
  color: string;
  icon: string;
  rowId: string;
}

interface StatRow {
  id: string;
  size: "big" | "medium" | "small";
  label: string;
}

interface StatsSettingsTabProps {
  data: any[];
  yAxisKeys: any[];
  onSettingsChange: (settings: any) => void;
}

const iconOptions = [
  { value: "Activity", label: "Activity", icon: Activity },
  { value: "TrendingUp", label: "Trending Up", icon: TrendingUp },
  { value: "Wallet", label: "Wallet", icon: Wallet },
  { value: "Users", label: "Users", icon: Users },
  { value: "ShoppingCart", label: "Shopping Cart", icon: ShoppingCart },
  { value: "Repeat", label: "Repeat", icon: Repeat },
  { value: "ArrowDownLeft", label: "Arrow Down Left", icon: ArrowDownLeft },
  { value: "Smile", label: "Smile", icon: Smile },
  { value: "UserPlus", label: "User Plus", icon: UserPlus },
];

const sizeOptions = [
  { value: "big", label: "Big", description: "Large cards, 1-2 per row" },
  { value: "medium", label: "Medium", description: "Medium cards, 2-4 per row" },
  { value: "small", label: "Small", description: "Small cards, up to 5 per row" },
];

export function StatsSettingsTab({ data, yAxisKeys, onSettingsChange }: StatsSettingsTabProps) {
  const columns = data && data.length > 0 ? Object.keys(data[0]) : [];
  const availableColumns = columns.length > 0 ? columns : ['revenue', 'users', 'sessions', 'value'];

  // Initialize stats and rows separately
  const [stats, setStats] = useState<StatItem[]>(() => {
    if (!yAxisKeys || yAxisKeys.length === 0) {
      return [];
    }
    return yAxisKeys.map((stat, index) => ({
      key: stat.key,
      label: stat.label || stat.key,
      color: stat.color,
      icon: stat.icon || 'Activity',
      rowId: stat.rowId || 'row-1'
    }));
  });

  const [rows, setRows] = useState<StatRow[]>(() => {
    if (!yAxisKeys || yAxisKeys.length === 0) {
      return [];
    }

    // Extract unique row configurations from yAxisKeys
    const rowMap = new Map<string, StatRow>();
    yAxisKeys.forEach(stat => {
      const rowId = stat.rowId || 'row-1';
      if (!rowMap.has(rowId)) {
        rowMap.set(rowId, {
          id: rowId,
          size: stat.size || 'small',
          label: `Row ${rowId.split('-')[1] || '1'}`
        });
      }
    });

    return Array.from(rowMap.values());
  });

  const [isAddRowDialogOpen, setIsAddRowDialogOpen] = useState(false);
  const [newRowSize, setNewRowSize] = useState<"big" | "medium" | "small">("medium");

  const updateSettings = (updatedStats: StatItem[], updatedRows: StatRow[]) => {
    // Convert stats back to yAxisKeys format
    const newYAxisKeys = updatedStats.map(stat => {
      const row = updatedRows.find(r => r.id === stat.rowId);
      return {
        key: stat.key,
        label: stat.label,
        color: stat.color,
        icon: stat.icon,
        size: row?.size || 'small',
        rowId: stat.rowId,
        type: "bar" as const,
        yAxisId: "left" as const
      };
    });

    onSettingsChange({
      yAxisKeys: newYAxisKeys,
      type: "stats"
    });
  };

  const addRow = (size: "big" | "medium" | "small") => {
    const newRowId = `row-${rows.length + 1}`;
    const newRow: StatRow = {
      id: newRowId,
      size: size,
      label: `Row ${rows.length + 1}`
    };
    const updatedRows = [...rows, newRow];
    setRows(updatedRows);
    updateSettings(stats, updatedRows);
    setIsAddRowDialogOpen(false);
  };

  const removeRow = (rowId: string) => {
    const updatedRows = rows.filter(row => row.id !== rowId);
    const updatedStats = stats.filter(stat => stat.rowId !== rowId);
    setRows(updatedRows);
    setStats(updatedStats);
    updateSettings(updatedStats, updatedRows);
  };

  const updateRowSize = (rowId: string, size: "big" | "medium" | "small") => {
    const updatedRows = rows.map(row =>
      row.id === rowId ? { ...row, size } : row
    );
    setRows(updatedRows);
    updateSettings(stats, updatedRows);
  };

  const addColumn = () => {
    const unusedColumns = availableColumns.filter(col =>
      !stats.some(stat => stat.key === col)
    );

    if (unusedColumns.length > 0 && rows.length > 0) {
      const newStat: StatItem = {
        key: unusedColumns[0],
        label: unusedColumns[0],
        color: visualizationConfig.defaultColors[Math.floor(Math.random() * visualizationConfig.defaultColors.length)],
        icon: 'Activity',
        rowId: rows[0].id // Default to first row
      };

      const updatedStats = [...stats, newStat];
      setStats(updatedStats);
      updateSettings(updatedStats, rows);
    }
  };

  const removeColumn = (statIndex: number) => {
    const updatedStats = stats.filter((_, index) => index !== statIndex);
    setStats(updatedStats);
    updateSettings(updatedStats, rows);
  };

  const updateStat = (statIndex: number, field: keyof StatItem, value: string) => {
    const updatedStats = stats.map((stat, index) =>
      index === statIndex ? { ...stat, [field]: value } : stat
    );
    setStats(updatedStats);
    updateSettings(updatedStats, rows);
  };

  return (
    <div className="space-y-6 mt-4">
      {/* Rows Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <Label className="text-sm font-medium text-gray-700">Rows</Label>
            <p className="text-sm text-gray-600 mt-1">
              Configure the layout rows for your stats display.
            </p>
          </div>
          <Dialog open={isAddRowDialogOpen} onOpenChange={setIsAddRowDialogOpen}>
            <DialogTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="h-9 px-3 text-sm"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Row
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Add New Row</DialogTitle>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Row Size</Label>
                  <Select value={newRowSize} onValueChange={(value: "big" | "medium" | "small") => setNewRowSize(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {sizeOptions.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          <div>
                            <div className="font-medium">{option.label}</div>
                            <div className="text-xs text-gray-500">{option.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsAddRowDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={() => addRow(newRowSize)}>
                    Add Row
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {rows.map((row, rowIndex) => (
          <Card key={row.id} className="p-3 border border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Label className="text-sm font-medium text-gray-700">
                  Row {rowIndex + 1}
                </Label>
                <Select
                  value={row.size}
                  onValueChange={(value: "big" | "medium" | "small") => updateRowSize(row.id, value)}
                >
                  <SelectTrigger className="w-32 h-8 text-sm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {sizeOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => removeRow(row.id)}
                disabled={rows.length <= 1}
                className="h-8 px-2 text-sm text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </Card>
        ))}

        {rows.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No rows configured. Click "Add Row" to start building your stats layout.
          </div>
        )}
      </div>

      {/* Columns Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <Label className="text-sm font-medium text-gray-700">Columns</Label>
            <p className="text-sm text-gray-600 mt-1">
              Configure the data columns for your stats display.
            </p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={addColumn}
            disabled={rows.length === 0}
            className="h-9 px-3 text-sm"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Column
          </Button>
        </div>

        <div className="space-y-3">
          {stats.map((stat, statIndex) => (
            <Card key={statIndex} className="p-4 border border-gray-200">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium text-gray-700">
                    Column {statIndex + 1}
                  </Label>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => removeColumn(statIndex)}
                    className="h-8 px-2 text-sm text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-1">
                    <Label className="text-xs font-medium text-gray-600">Data Column</Label>
                    <Select
                      value={stat.key}
                      onValueChange={(value) => updateStat(statIndex, 'key', value)}
                    >
                      <SelectTrigger className="h-8 text-sm">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {availableColumns.map(col => (
                          <SelectItem key={col} value={col}>{col}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-1">
                    <Label className="text-xs font-medium text-gray-600">Label</Label>
                    <Input
                      value={stat.label}
                      onChange={(e) => updateStat(statIndex, 'label', e.target.value)}
                      className="h-8 text-sm"
                      placeholder="Column label"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-1">
                    <Label className="text-xs font-medium text-gray-600">Color</Label>
                    <div className="flex items-center gap-2">
                      <div
                        className="w-6 h-6 rounded border border-gray-300"
                        style={{ backgroundColor: stat.color }}
                      />
                      <Input
                        type="color"
                        value={stat.color}
                        onChange={(e) => updateStat(statIndex, 'color', e.target.value)}
                        className="h-8 w-16 p-1"
                      />
                      <span className="text-xs text-gray-500 font-mono">{stat.color}</span>
                    </div>
                  </div>

                  <div className="space-y-1">
                    <Label className="text-xs font-medium text-gray-600">Row</Label>
                    <Select
                      value={stat.rowId}
                      onValueChange={(value) => updateStat(statIndex, 'rowId', value)}
                    >
                      <SelectTrigger className="h-8 text-sm">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {rows.map((row, rowIndex) => (
                          <SelectItem key={row.id} value={row.id}>
                            Row {rowIndex + 1} ({row.size})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-1">
                    <Label className="text-xs font-medium text-gray-600">Icon</Label>
                    <Select
                      value={stat.icon}
                      onValueChange={(value) => updateStat(statIndex, 'icon', value)}
                    >
                      <SelectTrigger className="h-8 text-sm">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {iconOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            <div className="flex items-center gap-2">
                              <option.icon size={14} />
                              {option.label}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </Card>
          ))}

          {stats.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              No columns configured. Click "Add Column" to start adding data columns.
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
