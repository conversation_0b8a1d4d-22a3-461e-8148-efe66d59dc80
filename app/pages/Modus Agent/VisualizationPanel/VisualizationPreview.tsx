import { RefreshCw } from 'lucide-react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Composed<PERSON><PERSON>,
  Bar,
  Line,
  XAxis,
  <PERSON>A<PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
} from "recharts";
import {
  prepareVisualizationData,
  needsDataProcessing,
  AggregationConfig
} from "../../../utils/dataProcessing";

interface VisualizationPreviewProps {
  title?: string;
  description?: string;
  data: any[];
  xAxisKey: string | string[];
  yAxisKeys: any[];
  type: string;
  onRefresh?: () => void;
  primaryGroupField?: string;
  secondaryGroupField?: string;
}

// Configuration for data processing
interface VisualizationPreviewConfig {
  primaryField: string;
  secondaryField: string;
  aggregationConfig: AggregationConfig;
}

export function VisualizationPreview({
  title = "Transaction Analysis by Date and City",
  description = "Combo graph with transaction sum (bar), transaction count (line), and XYZ values",
  data,
  xAxisKey,
  yAxis<PERSON><PERSON><PERSON>,
  type,
  onRefresh,
  primaryGroup<PERSON>ield,
  secondaryGroup<PERSON>ield
}: VisualizationPreviewProps) {
  // Mock chart data for preview - matches the format from your image
  const mockData = [
    { name: "D1, C2", date: "D1", city: "C2", transactions: 8200, xyz: 6500, transactionCount: 5.0 },
    { name: "D3, C2", date: "D3", city: "C2", transactions: 6500, xyz: 5000, transactionCount: 5.0 },
    { name: "D3, C4", date: "D3", city: "C4", transactions: 5000, xyz: 4200, transactionCount: 5.0 },
    { name: "D4, C1", date: "D4", city: "C1", transactions: 8000, xyz: 6800, transactionCount: 5.0 },
    { name: "D4, C2", date: "D4", city: "C2", transactions: 9800, xyz: 7500, transactionCount: 5.0 },
    { name: "D4, C3", date: "D4", city: "C3", transactions: 6000, xyz: 4800, transactionCount: 5.0 },
    { name: "D5, C1", date: "D5", city: "C1", transactions: 12000, xyz: 9500, transactionCount: 5.2 },
    { name: "D5, C4", date: "D5", city: "C4", transactions: 9200, xyz: 7200, transactionCount: 5.0 }
  ];

  // Process data if we have raw data, otherwise use mock data
  let chartData;
  if (data && data.length > 0) {
    // Use the new utility function to prepare visualization data
    // Handle both array and string xAxisKey formats
    if (Array.isArray(xAxisKey)) {
      // Array format: use xAxisKey directly
      chartData = prepareVisualizationData(data, xAxisKey, {
        transactions: 'sum',
        transactionCount: 'avg',
        xyz: 'sum'
      });
    } else {
      // Legacy string format: use primaryGroupField and secondaryGroupField
      chartData = prepareVisualizationData(data, [primaryGroupField || 'date', secondaryGroupField || 'category'], {
        transactions: 'sum',
        transactionCount: 'avg',
        xyz: 'sum'
      });
    }
  } else {
    chartData = mockData;
  }

  return (
    <div className="bg-gray-50 border rounded-lg p-4 mb-4">
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
            <div className="w-4 h-4 bg-blue-500 rounded"></div>
          </div>
          <div>
            <h3 className="font-semibold text-sm">{title}</h3>
            <p className="text-xs text-gray-600">{description}</p>
          </div>
        </div>
        <Button variant="ghost" size="sm" onClick={onRefresh}>
          <RefreshCw className="h-4 w-4 mr-1" />
          Refresh
        </Button>
      </div>

      {/* Tabs */}
      <div className="flex gap-1 mb-3">
        <button className="px-3 py-1 text-xs bg-white border rounded font-medium">
          Visualization
        </button>
        <button className="px-3 py-1 text-xs text-gray-600 hover:bg-gray-100 rounded">
          Data
        </button>
        <button className="px-3 py-1 text-xs text-gray-600 hover:bg-gray-100 rounded">
          Config
        </button>
      </div>

      {/* Chart Preview */}
      <div className="bg-white border rounded p-3 h-64">
        <ResponsiveContainer width="100%" height="100%">
          <ComposedChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="name"
              tick={{ fontSize: 11 }}
              angle={-45}
              textAnchor="end"
              height={60}
            />
            <YAxis
              yAxisId="left"
              orientation="left"
              tick={{ fontSize: 11 }}
              label={{
                value: 'Sum of Transactions',
                angle: -90,
                position: 'insideLeft',
                style: { fontSize: 11, fill: '#666' }
              }}
            />
            <YAxis
              yAxisId="right"
              orientation="right"
              tick={{ fontSize: 11 }}
              label={{
                value: 'Count of Transactions',
                angle: 90,
                position: 'insideRight',
                style: { fontSize: 11, fill: '#666' }
              }}
            />
            <Tooltip
              content={({ active, payload, label }) => {
                if (active && payload && payload.length) {
                  return (
                    <div className="bg-white p-2 border border-gray-200 rounded shadow-sm text-xs">
                      <p className="font-medium">{label}</p>
                      {payload.map((entry, index) => (
                        <p key={index} style={{ color: entry.color }}>
                          {entry.name}: {entry.value?.toLocaleString() ?? 'N/A'}
                        </p>
                      ))}
                    </div>
                  );
                }
                return null;
              }}
            />
            <Legend
              wrapperStyle={{ fontSize: 11 }}
              verticalAlign="top"
              align="center"
              iconType="rect"
            />

            {/* Bar for transactions */}
            <Bar
              dataKey="transactions"
              name="Sum of Transactions"
              fill="#FFA500"
              yAxisId="left"
            />

            {/* Line for transaction count */}
            <Line
              type="monotone"
              dataKey="transactionCount"
              name="Count of Transactions"
              stroke="#FF0000"
              strokeWidth={2}
              dot={{ fill: '#FF0000', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6 }}
              yAxisId="right"
            />
          </ComposedChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
