import { useState, useEffect } from 'react';
import {
  Plus, X, BarChart3
} from 'lucide-react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { MultiSelect } from "@/components/ui/multi-select2";
import { visualizationConfig } from "../../../config/visualizationConfig";
import { StatsSettingsTab } from "./StatsSettingsTab";

interface SeriesConfig {
  key: string;
  label?: string;
  type: "bar" | "line";
  yAxisId: "left" | "right";
  color: string;
  aggregation?: string;
  groupBy?: string;
}

interface VisualizationPanelSettingsProps {
  data: any[];
  xAxisKey: string | string[];
  yAxisKeys: any[];
  groupBy?: string;
  errorColumn?: string;
  stacking?: string;
  normalization?: boolean;
  missingNullHandling?: string;
  onSettingsChange: (settings: any) => void;
  onApplyChanges: () => void;
  visualizationId?: string;
  dashboardId?: string;
  primaryGroupField?: string;
  secondaryGroupField?: string;
}

type TabValue = "general" | "grouping" | "xaxis" | "yaxis" | "series" | "colors" | "datalabels" | "stats";

export function VisualizationPanelSettings({
  data,
  xAxisKey,
  yAxisKeys,
  groupBy,
  errorColumn,
  stacking,
  normalization,
  missingNullHandling,
  onSettingsChange,
  visualizationId,
  dashboardId
}: VisualizationPanelSettingsProps) {
  const [activeTab, setActiveTab] = useState<TabValue>("general");

  // Debug data structure
  console.log('VisualizationPanelSettings - data:', data);
  console.log('VisualizationPanelSettings - xAxisKey:', xAxisKey);
  console.log('VisualizationPanelSettings - yAxisKeys:', yAxisKeys);

  const columns = data && data.length > 0 ? Object.keys(data[0]) : [];

  // If no columns detected, provide some fallback columns for testing
  const availableColumns = columns.length > 0 ? columns : ['month', 'revenue', 'users', 'sessions', 'value'];

  // Local state for editing
  const [visualizationType, setVisualizationType] = useState("combo");
  const [localXAxisKey, setLocalXAxisKey] = useState<string[]>(
    Array.isArray(xAxisKey) ? xAxisKey : (xAxisKey ? [xAxisKey] : (availableColumns.length > 0 ? [availableColumns[0]] : []))
  );
  const [localYKeys, setLocalYKeys] = useState<SeriesConfig[]>(
    yAxisKeys.map((key, index) => ({
      key: typeof key === 'string' ? key : key.key,
      label: typeof key === 'string' ? key : key.label || key.key,
      type: typeof key === 'string' ? "bar" : key.type || "bar",
      yAxisId: typeof key === 'string' ? "left" : key.yAxisId || "left",
      color: typeof key === 'string' ? visualizationConfig.defaultColors[index % visualizationConfig.defaultColors.length] : key.color || visualizationConfig.defaultColors[index % visualizationConfig.defaultColors.length],
      aggregation: typeof key === 'string' ? "sum" : (key as any).aggregation || "sum",
      groupBy: typeof key === 'string' ? "none" : (key as any).groupBy || "none"
    }))
  );
  const [localGroupBy, setLocalGroupBy] = useState(groupBy || "none");
  const [localErrorColumn, setLocalErrorColumn] = useState(errorColumn || "none");
  const [localStacking, setLocalStacking] = useState(stacking || "none");
  const [localNormalization, setLocalNormalization] = useState(normalization || false);
  const [localMissingNullHandling, setLocalMissingNullHandling] = useState(missingNullHandling || "0");

  // Helper function to derive group fields from xAxisKey
  const deriveGroupFields = (xAxisKey: string[]) => {
    // Use the provided fields or detect from available columns
    const primaryField = xAxisKey[0] || (availableColumns.length > 0 ? availableColumns[0] : 'field1');
    const secondaryField = xAxisKey[1] || (availableColumns.length > 1 ? availableColumns[1] : 'field2');

    return {
      primaryGroupField: primaryField,
      secondaryGroupField: secondaryField
    };
  };

  // Initialize group fields from xAxisKey
  const initialGroupFields = deriveGroupFields(localXAxisKey);
  const [primaryGroupField, setPrimaryGroupField] = useState(initialGroupFields.primaryGroupField);
  const [secondaryGroupField, setSecondaryGroupField] = useState(initialGroupFields.secondaryGroupField);
  const [showDataLabels, setShowDataLabels] = useState(true);
  const [numberFormat, setNumberFormat] = useState("");
  const [percentFormat, setPercentFormat] = useState("0|0.00%");
  const [dateFormat, setDateFormat] = useState("YYYY-MM-DD HH:mm");
  const [dataLabelMode, setDataLabelMode] = useState("automatic");
  const [xAxisLabel, setXAxisLabel] = useState("");
  const [xAxisScaleType, setXAxisScaleType] = useState("linear");
  const [showXAxisLine, setShowXAxisLine] = useState(true);
  const [showXTicks, setShowXTicks] = useState(true);
  const [yAxisLabel, setYAxisLabel] = useState("");
  const [showYAxisLine, setShowYAxisLine] = useState(true);
  const [showYTicks, setShowYTicks] = useState(true);
  const [horizontal, setHorizontal] = useState(false);

  // Initialize series from data columns if empty
  useEffect(() => {
    if (localYKeys.length === 0 && availableColumns.length > 0) {
      const numericColumns = availableColumns.filter(col => {
        const sampleValue = data[0]?.[col];
        return typeof sampleValue === 'number';
      });

      if (numericColumns.length > 0) {
        const newSeries = numericColumns.slice(0, 2).map((col, index) => ({
          key: col,
          label: col,
          type: "bar" as const,
          yAxisId: "left" as const,
          color: visualizationConfig.defaultColors[index % visualizationConfig.defaultColors.length],
          aggregation: "sum",
          groupBy: "none"
        }));
        setLocalYKeys(newSeries);
      } else {
        // If no numeric columns found, use first few available columns
        const defaultSeries = availableColumns.slice(1, 3).map((col, index) => ({
          key: col,
          label: col,
          type: "bar" as const,
          yAxisId: "left" as const,
          color: visualizationConfig.defaultColors[index % visualizationConfig.defaultColors.length],
          aggregation: "sum",
          groupBy: "none"
        }));
        setLocalYKeys(defaultSeries);
      }
    }
  }, [availableColumns, data, localYKeys.length]);

  // Update group fields when xAxisKey changes
  useEffect(() => {
    const groupFields = deriveGroupFields(localXAxisKey);
    setPrimaryGroupField(groupFields.primaryGroupField);
    setSecondaryGroupField(groupFields.secondaryGroupField);
  }, [localXAxisKey]);

  // Update settings when xAxisKey changes
  useEffect(() => {
    updateSettings();
  }, [localXAxisKey]);

  // Update settings helper
  const updateSettings = (overrides = {}) => {
    // Derive group fields from current xAxisKey
    const currentGroupFields = deriveGroupFields(localXAxisKey);

    const updatedSettings = {
      xAxisKey: localXAxisKey.length === 1 ? localXAxisKey[0] : localXAxisKey,
      yAxisKeys: localYKeys,
      groupBy: localGroupBy,
      errorColumn: localErrorColumn,
      stacking: localStacking,
      normalization: localNormalization,
      missingNullHandling: localMissingNullHandling,
      type: visualizationType,
      primaryGroupField: currentGroupFields.primaryGroupField,
      secondaryGroupField: currentGroupFields.secondaryGroupField,
      ...overrides
    };
    onSettingsChange(updatedSettings);
  };

  const handleSeriesChange = (idx: number, field: string, value: any) => {
    const updated = localYKeys.map((s, i) => i === idx ? { ...s, [field]: value } : s);
    setLocalYKeys(updated);
    updateSettings();
  };

  const addColumn = () => {
    const unusedColumns = availableColumns.filter(col =>
      !localYKeys.some(series => series.key === col)
    );

    if (unusedColumns.length > 0) {
      const newSeries: SeriesConfig = {
        key: unusedColumns[0],
        label: unusedColumns[0],
        type: "bar",
        yAxisId: "left",
        color: visualizationConfig.defaultColors[localYKeys.length % visualizationConfig.defaultColors.length],
        aggregation: "sum",
        groupBy: "none"
      };

      const updated = [...localYKeys, newSeries];
      setLocalYKeys(updated);
      updateSettings();
    }
  };

  const removeColumn = (idx: number) => {
    const updated = localYKeys.filter((_, i) => i !== idx);
    setLocalYKeys(updated);
    updateSettings();
  };

  return (
    <div className="w-full">
      <Tabs defaultValue={activeTab} value={activeTab} onValueChange={(value) => setActiveTab(value as TabValue)} className="w-full">
        <TabsList className="grid grid-cols-8 w-full h-12 bg-gray-100 p-1">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="grouping">Grouping</TabsTrigger>
          <TabsTrigger value="xaxis">X axis</TabsTrigger>
          <TabsTrigger value="yaxis">Y axis</TabsTrigger>
          <TabsTrigger value="series">Series</TabsTrigger>
          <TabsTrigger value="colors">Colors</TabsTrigger>
          <TabsTrigger value="datalabels">Labels</TabsTrigger>
          <TabsTrigger value="stats">Stats</TabsTrigger>
        </TabsList>

        <TabsContent value="general">
          <div className="space-y-6 mt-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Visualization type</Label>
                <Select value={visualizationType} onValueChange={(value) => {
                  setVisualizationType(value);
                  updateSettings({ type: value });
                }}>
                  <SelectTrigger className="h-10 text-sm">
                    <BarChart3 className="h-4 w-4 mr-2" />
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {visualizationConfig.visualizationTypes.map(type => (
                      <SelectItem key={type.value} value={type.value}>{type.label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Data labels</Label>
                <Select value={dataLabelMode} onValueChange={(value) => {
                  setDataLabelMode(value);
                  updateSettings({ dataLabelMode: value });
                }}>
                  <SelectTrigger className="h-10 text-sm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {visualizationConfig.dataLabelModes.map(mode => (
                      <SelectItem key={mode.value} value={mode.value}>{mode.label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <Switch
                checked={showDataLabels}
                onCheckedChange={(checked) => {
                  setShowDataLabels(checked);
                  updateSettings({ showDataLabels: checked });
                }}
              />
              <Label className="text-sm font-medium text-gray-700">Show data labels</Label>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Number format</Label>
                <Input
                  className="h-10 text-sm"
                  value={numberFormat}
                  onChange={e => {
                    setNumberFormat(e.target.value);
                    updateSettings({ numberFormat: e.target.value });
                  }}
                  placeholder="e.g. 0,0.00"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Percent format</Label>
                <Input
                  className="h-10 text-sm"
                  value={percentFormat}
                  onChange={e => {
                    setPercentFormat(e.target.value);
                    updateSettings({ percentFormat: e.target.value });
                  }}
                  placeholder="0|0.00%"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">Date/time format</Label>
              <Input
                className="h-10 text-sm"
                value={dateFormat}
                onChange={e => {
                  setDateFormat(e.target.value);
                  updateSettings({ dateFormat: e.target.value });
                }}
                placeholder="YYYY-MM-DD HH:mm"
              />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="grouping">
          <div className="space-y-6 mt-4">
            <div className="space-y-3">
              <Label className="text-sm font-medium text-gray-700">Data Grouping Configuration</Label>
              <p className="text-sm text-gray-600">
                Configure how to group your data by combining two fields (e.g., Date + City)
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Primary Field (e.g., Date)</Label>
                <Select value={primaryGroupField} onValueChange={(value) => {
                  setPrimaryGroupField(value);
                  updateSettings({ primaryGroupField: value });
                }}>
                  <SelectTrigger className="h-10 text-sm">
                    <SelectValue placeholder="Select primary field..." />
                  </SelectTrigger>
                  <SelectContent>
                    {availableColumns.map(col => (
                      <SelectItem key={col} value={col}>{col}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Secondary Field (e.g., City)</Label>
                <Select value={secondaryGroupField} onValueChange={(value) => {
                  setSecondaryGroupField(value);
                  updateSettings({ secondaryGroupField: value });
                }}>
                  <SelectTrigger className="h-10 text-sm">
                    <SelectValue placeholder="Select secondary field..." />
                  </SelectTrigger>
                  <SelectContent>
                    {availableColumns.map(col => (
                      <SelectItem key={col} value={col}>{col}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="p-4 bg-gray-50 rounded-lg border">
              <Label className="text-sm font-medium text-gray-700">Grouping Preview</Label>
              <p className="text-sm text-gray-600 mt-2">
                Data will be grouped by: <span className="font-mono bg-white px-2 py-1 rounded">{primaryGroupField}, {secondaryGroupField}</span>
              </p>
              <p className="text-sm text-gray-500 mt-2">
                Example: "2024-01-15, New York", "2024-01-16, Los Angeles"
              </p>
            </div>

            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <Label className="text-sm font-medium text-blue-800">How it works</Label>
              <ul className="text-sm text-blue-700 mt-2 space-y-1">
                <li>• Data rows with the same {primaryGroupField} + {secondaryGroupField} combination will be grouped together</li>
                <li>• Numeric values will be aggregated using the method selected in the Series tab (Sum, Average, etc.)</li>
                <li>• The chart X-axis will show the combined grouping values</li>
              </ul>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="xaxis">
          <div className="space-y-6 mt-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Axis title</Label>
                <Input
                  className="h-10 text-sm"
                  value={xAxisLabel}
                  onChange={e => {
                    setXAxisLabel(e.target.value);
                    updateSettings({ xAxisLabel: e.target.value });
                  }}
                  placeholder="Enter axis title"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Scale type</Label>
                <Select value={xAxisScaleType} onValueChange={(value) => {
                  setXAxisScaleType(value);
                  updateSettings({ xAxisScaleType: value });
                }}>
                  <SelectTrigger className="h-10 text-sm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {visualizationConfig.scaleTypes.map(scale => (
                      <SelectItem key={scale.value} value={scale.value}>{scale.label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                <Switch
                  checked={showXAxisLine}
                  onCheckedChange={(checked) => {
                    setShowXAxisLine(checked);
                    updateSettings({ showXAxisLine: checked });
                  }}
                />
                <Label className="text-sm font-medium text-gray-700">Show axis line</Label>
              </div>

              <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                <Switch
                  checked={showXTicks}
                  onCheckedChange={(checked) => {
                    setShowXTicks(checked);
                    updateSettings({ showXTicks: checked });
                  }}
                />
                <Label className="text-sm font-medium text-gray-700">Show tick marks</Label>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="yaxis">
          <div className="space-y-6 mt-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">Axis title</Label>
              <Input
                className="h-10 text-sm"
                value={yAxisLabel}
                onChange={e => {
                  setYAxisLabel(e.target.value);
                  updateSettings({ yAxisLabel: e.target.value });
                }}
                placeholder="Enter axis title"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                <Switch
                  checked={showYAxisLine}
                  onCheckedChange={(checked) => {
                    setShowYAxisLine(checked);
                    updateSettings({ showYAxisLine: checked });
                  }}
                />
                <Label className="text-sm font-medium text-gray-700">Show axis line</Label>
              </div>

              <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                <Switch
                  checked={showYTicks}
                  onCheckedChange={(checked) => {
                    setShowYTicks(checked);
                    updateSettings({ showYTicks: checked });
                  }}
                />
                <Label className="text-sm font-medium text-gray-700">Show tick marks</Label>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="series">
          <div className="space-y-3 mt-3">
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-1">
                <Label className="text-xs font-medium">X columns</Label>
                <MultiSelect
                  options={availableColumns.map(col => ({ value: col, label: col }))}
                  onValueChange={(values) => {
                    setLocalXAxisKey(values);
                  }}
                  defaultValue={localXAxisKey}
                  placeholder="Select X-axis columns..."
                  variant="inverted"
                  animation={2}
                  maxCount={3}
                  className="h-8 text-xs"
                />
                {localXAxisKey.length > 1 && (
                  <p className="text-xs text-gray-600 mt-1">
                    Data will be grouped by: <span className="font-mono bg-gray-100 px-1 rounded">{localXAxisKey.join(', ')}</span>
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Group by</Label>
                <Select value={localGroupBy} onValueChange={(value) => {
                  setLocalGroupBy(value);
                  updateSettings({ groupBy: value });
                }}>
                  <SelectTrigger className="h-10 text-sm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    {availableColumns.map(col => (
                      <SelectItem key={col} value={col}>{col}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium text-gray-700">Y columns</Label>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-9 px-3 text-sm"
                    onClick={addColumn}
                    disabled={localYKeys.length >= availableColumns.length - 1}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Column
                  </Button>
                </div>

                <div className="space-y-4">
                  {localYKeys.map((series, idx) => (
                    <div key={idx} className="p-4 border rounded-lg bg-white shadow-sm">
                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <div className="space-y-2">
                          <Label className="text-sm font-medium text-gray-700">Data Column</Label>
                          <Select value={series.key} onValueChange={v => handleSeriesChange(idx, "key", v)}>
                            <SelectTrigger className="h-10 text-sm">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {availableColumns.map(col => (
                                <SelectItem key={col} value={col}>{col}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label className="text-sm font-medium text-gray-700">Aggregation</Label>
                          <Select
                            value={series.aggregation || "sum"}
                            onValueChange={v => handleSeriesChange(idx, "aggregation", v)}
                          >
                            <SelectTrigger className="h-10 text-sm">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {visualizationConfig.aggregationOptions.map(option => (
                                <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="grid grid-cols-3 gap-4 mb-4">
                        <div className="space-y-2">
                          <Label className="text-sm font-medium text-gray-700">Type</Label>
                          <Select
                            value={series.type || "bar"}
                            onValueChange={v => handleSeriesChange(idx, "type", v)}
                          >
                            <SelectTrigger className="h-10 text-sm">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {visualizationConfig.chartTypes.map(type => (
                                <SelectItem key={type.value} value={type.value}>{type.label}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label className="text-sm font-medium text-gray-700">Axis</Label>
                          <Select
                            value={series.yAxisId || "left"}
                            onValueChange={v => handleSeriesChange(idx, "yAxisId", v)}
                          >
                            <SelectTrigger className="h-10 text-sm">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {visualizationConfig.yAxisPositions.map(pos => (
                                <SelectItem key={pos.value} value={pos.value}>{pos.label}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label className="text-sm font-medium text-gray-700 opacity-0">Remove</Label>
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-10 w-full text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200"
                            onClick={() => removeColumn(idx)}
                            disabled={localYKeys.length <= 1}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm font-medium text-gray-700">Series Label</Label>
                        <Input
                          className="h-10 text-sm"
                          value={series.label || series.key}
                          onChange={e => handleSeriesChange(idx, "label", e.target.value)}
                          placeholder="Enter series label"
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {localXAxisKey.length > 1 && (
                <div className="p-3 bg-blue-50 rounded border border-blue-200">
                  <Label className="text-xs font-medium text-blue-800">Multiple X-Axis Grouping</Label>
                  <ul className="text-xs text-blue-700 mt-1 space-y-1">
                    <li>• Data will be automatically grouped by combining the selected columns</li>
                    <li>• Example: If you select "Date" and "City", data will be grouped as "2024-01-15, New York"</li>
                    <li>• Numeric values will be aggregated using the method selected for each Y-axis series</li>
                    <li>• This creates unique combinations for more detailed analysis</li>
                  </ul>
                </div>
              )}

              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700">Bar Chart Display</Label>
                <div className="space-y-3">
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-gray-700">Stacking Mode</Label>
                    <Select value={localStacking} onValueChange={(value) => {
                      setLocalStacking(value);
                      updateSettings({ stacking: value });
                    }}>
                      <SelectTrigger className="h-10 text-sm">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {visualizationConfig.stackingOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="p-4 bg-gray-50 rounded-lg border">
                    <p className="text-sm text-gray-600">
                      {localStacking === "normal" && "Bars will be stacked on top of each other. Lines remain as overlays."}
                      {localStacking === "percent" && "Bars will be stacked and normalized to 100%. Lines remain as overlays."}
                      {localStacking === "none" && "Bars will be displayed side-by-side (grouped). Lines remain as overlays."}
                    </p>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                  <Switch
                    checked={horizontal}
                    onCheckedChange={(checked) => {
                      setHorizontal(checked);
                      updateSettings({ horizontal: checked });
                    }}
                  />
                  <Label className="text-sm font-medium text-gray-700">Horizontal chart</Label>
                </div>

                <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                  <Switch
                    checked={localNormalization}
                    onCheckedChange={(value) => {
                      setLocalNormalization(value);
                      updateSettings({ normalization: value });
                    }}
                  />
                  <Label className="text-sm font-medium text-gray-700">Normalize to percentage</Label>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="colors">
          <div className="space-y-4 mt-4">
            <Label className="text-sm font-medium text-gray-700">Series Colors</Label>
            <div className="grid grid-cols-1 gap-3">
              {localYKeys.map((series, idx) => (
                <div key={idx} className="flex items-center gap-4 p-4 border rounded-lg bg-white shadow-sm">
                  <Input
                    type="color"
                    value={series.color || "#4f46e5"}
                    onChange={e => handleSeriesChange(idx, "color", e.target.value)}
                    className="w-12 h-10 p-1 border rounded"
                  />
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium text-gray-700 truncate">{series.label || series.key}</div>
                    <div className="text-sm text-gray-500 font-mono">{series.color || "#4f46e5"}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="datalabels">
          <div className="space-y-6 mt-4">
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <Switch
                checked={showDataLabels}
                onCheckedChange={(checked) => {
                  setShowDataLabels(checked);
                  updateSettings({ showDataLabels: checked });
                }}
              />
              <Label className="text-sm font-medium text-gray-700">Show data labels</Label>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Number format</Label>
                <Input
                  className="h-10 text-sm"
                  value={numberFormat}
                  onChange={e => {
                    setNumberFormat(e.target.value);
                    updateSettings({ numberFormat: e.target.value });
                  }}
                  placeholder="e.g. 0,0.00"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Percent format</Label>
                <Input
                  className="h-10 text-sm"
                  value={percentFormat}
                  onChange={e => {
                    setPercentFormat(e.target.value);
                    updateSettings({ percentFormat: e.target.value });
                  }}
                  placeholder="e.g. 0|0.00%"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Date/time format</Label>
                <Input
                  className="h-10 text-sm"
                  value={dateFormat}
                  onChange={e => {
                    setDateFormat(e.target.value);
                    updateSettings({ dateFormat: e.target.value });
                  }}
                  placeholder="YYYY-MM-DD HH:mm"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Label position</Label>
                <Select value={dataLabelMode} onValueChange={(value) => {
                  setDataLabelMode(value);
                  updateSettings({ dataLabelMode: value });
                }}>
                  <SelectTrigger className="h-10 text-sm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {visualizationConfig.dataLabelModes.map(mode => (
                      <SelectItem key={mode.value} value={mode.value}>{mode.label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="stats">
          <StatsSettingsTab
            data={data}
            yAxisKeys={localYKeys}
            onSettingsChange={(settings) => {
              if (settings.yAxisKeys) {
                setLocalYKeys(settings.yAxisKeys);
              }
              updateSettings(settings);
            }}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
