import { useState, useEffect } from 'react';
import {
  ChevronDown, ChevronUp, Eye, RotateCcw, Save, X
} from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { Card } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { MultiSelect } from "@/components/ui/multi-select2";
import { visualizationConfig } from '@/app/config/visualizationConfig';
import { useLivePreviewStore } from '@/app/store/visualization/livePreviewStore';
import type { Visualization as VisualizationType } from '@/app/data/hardcodeddata/dashboardStructures';
import { StatsSettingsTab } from './StatsSettingsTab';

interface VisualizationPanelProps {
  messages?: Array<{
    text: string;
    isUser: boolean;
    code?: {
      language: string;
      content: string;
    } | null;
    graph?: {
      type: string;
      data: any;
      xAxisKey?: string | string[];
      yAxisKeys?: any[];
      groupBy?: string;
      errorColumn?: string;
      stacking?: 'none' | 'normal' | 'percent';
      normalization?: boolean;
      missingNullHandling?: string;
    } | null;
  }>;
  onViewGraph?: (graph: {
    type: string;
    data: any;
    xAxisKey?: string | string[];
    yAxisKeys?: any[];
    groupBy?: string;
    errorColumn?: string;
    stacking?: 'none' | 'normal' | 'percent';
    normalization?: boolean;
    missingNullHandling?: string;
  }, text: string) => void;
  visualizationId?: string;
  dashboardId?: string;
  visualization?: VisualizationType;
  onSave?: (updatedVisualization: VisualizationType) => void;
  onCancel?: () => void;
}

interface VizConfig {
  data: any[];
  xAxisKey: string | string[];
  yAxisKeys: any[];
  groupBy?: string;
  errorColumn?: string;
  stacking?: 'none' | 'normal' | 'percent';
  normalization?: boolean;
  missingNullHandling?: string;
  type: 'combo' | 'stats';
  primaryGroupField?: string;
  secondaryGroupField?: string;
  title?: string;
  description?: string;
  leftYAxisLabel?: string;
  rightYAxisLabel?: string;
}

export function VisualizationPanel({
  messages,
  onViewGraph,
  visualizationId,
  dashboardId,
  visualization,
  onSave,
  onCancel
}: VisualizationPanelProps) {
  const [isCollapsed, setIsCollapsed] = useState(true);
  const [activeTab, setActiveTab] = useState<'general' | 'data' | 'styling' | 'stats'>("general");

  // Access live preview store
  const { setLivePreviewConfig, clearLivePreviewConfig } = useLivePreviewStore();

  // Helper function to derive group fields from xAxisKey
  const deriveGroupFields = (xAxisKey: string | string[]) => {
    if (Array.isArray(xAxisKey)) {
      return {
        primaryGroupField: xAxisKey[0] || 'date',
        secondaryGroupField: xAxisKey[1] || 'city'
      };
    } else {
      // For single string, use default fallback
      return {
        primaryGroupField: 'date',
        secondaryGroupField: 'city'
      };
    }
  };

  // Initialize config from either messages or visualization prop
  const initialConfig = visualization ? {
    ...visualization,
    groupBy: (visualization as any).groupBy || '',
    errorColumn: (visualization as any).errorColumn || '',
    stacking: (visualization as any).stacking || 'none', // Use actual stacking value from visualization
    normalization: (visualization as any).normalization || false,
    missingNullHandling: (visualization as any).missingNullHandling || '0',
    ...deriveGroupFields(visualization.xAxisKey), // Derive group fields from xAxisKey
    type: visualization.type === 'combo' || visualization.type === 'stats' ? visualization.type : 'combo',
  } : (messages?.[0]?.graph || {}) as Partial<VizConfig>;

  const [vizConfig, setVizConfig] = useState<VizConfig>({
    data: initialConfig?.data || [],
    xAxisKey: initialConfig?.xAxisKey || '',
    yAxisKeys: initialConfig?.yAxisKeys || [],
    groupBy: initialConfig?.groupBy || '',
    errorColumn: initialConfig?.errorColumn || '',
    stacking: (initialConfig?.stacking as 'none' | 'normal' | 'percent') || 'none',
    normalization: initialConfig?.normalization || false,
    missingNullHandling: initialConfig?.missingNullHandling || '0',
    type: initialConfig?.type === 'combo' || initialConfig?.type === 'stats' ? initialConfig.type : 'combo',
    primaryGroupField: initialConfig?.primaryGroupField || (initialConfig?.data && initialConfig.data.length > 0 ? Object.keys(initialConfig.data[0])[0] : 'field1') || 'field1',
    secondaryGroupField: initialConfig?.secondaryGroupField || (initialConfig?.data && initialConfig.data.length > 0 ? Object.keys(initialConfig.data[0])[1] : 'field2') || 'field2',
    title: initialConfig?.title || 'Visualization',
    description: initialConfig?.description || '',
    leftYAxisLabel: initialConfig?.leftYAxisLabel || '',
    rightYAxisLabel: initialConfig?.rightYAxisLabel || '',
  });

  // Get available columns from data
  const columns = vizConfig.data && vizConfig.data.length > 0
    ? Object.keys(vizConfig.data[0])
    : ['month', 'revenue', 'users', 'sessions', 'value'];

  // Auto-switch to appropriate tab based on visualization type
  useEffect(() => {
    if (vizConfig.type === 'stats' && (activeTab === 'data' || activeTab === 'styling')) {
      setActiveTab('stats');
    } else if (vizConfig.type === 'combo' && activeTab === 'stats') {
      setActiveTab('data');
    }
  }, [vizConfig.type, activeTab]);

  // Keep vizConfig in sync if messages[0]?.graph changes
  useEffect(() => {
    if (messages?.[0]?.graph) {
      const g = messages[0].graph as Partial<VizConfig>;
      setVizConfig(prev => ({
        ...prev,
        data: g?.data || prev.data,
        xAxisKey: g?.xAxisKey || prev.xAxisKey,
        yAxisKeys: g?.yAxisKeys || prev.yAxisKeys,
        groupBy: g?.groupBy || prev.groupBy,
        errorColumn: g?.errorColumn || prev.errorColumn,
        stacking: (g?.stacking as 'none' | 'normal' | 'percent' | undefined) || prev.stacking,
        normalization: g?.normalization || prev.normalization,
        missingNullHandling: g?.missingNullHandling || prev.missingNullHandling,
        type: g?.type === 'combo' || g?.type === 'stats' ? g.type : 'combo',
      }));
    }
  }, [messages]);

  // Set up initial live preview when editing a visualization
  useEffect(() => {
    if (visualizationId && visualization) {
      setLivePreviewConfig(visualizationId, vizConfig);
    }

    // Cleanup on unmount
    return () => {
      if (visualizationId) {
        clearLivePreviewConfig(visualizationId);
      }
    };
  }, [visualizationId, visualization]); // Only run when these change

  const handleApplyChanges = () => {
    if (onViewGraph) {
      onViewGraph({
        ...vizConfig,
        type: vizConfig.type,
      }, "Customized Visualization");
    }
    if (onSave) {
      // Properly map VizConfig to Visualization interface
      const updatedVisualization: VisualizationType = {
        ...visualization!, // Keep existing properties like visualization_id, dashboard_id, className
        ...vizConfig,
        stacking: vizConfig.stacking || 'none', // Ensure stacking is properly included
      };
      onSave(updatedVisualization);
    }

    // Clear live preview when saving
    if (visualizationId) {
      clearLivePreviewConfig(visualizationId);
    }
  };

  const handleSettingsChange = (settings: any) => {
    let updatedConfig = { ...vizConfig, ...settings };

    // If xAxisKey is being updated, also update the group fields
    if (settings.xAxisKey !== undefined) {
      const groupFields = deriveGroupFields(settings.xAxisKey);
      updatedConfig = { ...updatedConfig, ...groupFields };
    }

    setVizConfig(updatedConfig);

    // Update live preview for dashboard visualizations
    if (visualizationId) {
      setLivePreviewConfig(visualizationId, updatedConfig);
    }
  };

  const handleReset = () => {
    const initialConfig = visualization ? {
      ...visualization,
      groupBy: (visualization as any).groupBy || '',
      errorColumn: (visualization as any).errorColumn || '',
      stacking: (visualization as any).stacking || 'none', // Use actual stacking value from visualization
      normalization: (visualization as any).normalization || false,
      missingNullHandling: (visualization as any).missingNullHandling || '0',
      ...deriveGroupFields(visualization.xAxisKey), // Derive group fields from xAxisKey
      type: visualization.type === 'combo' || visualization.type === 'stats' ? visualization.type : 'combo',
    } : (messages?.[0]?.graph || {}) as Partial<VizConfig>;
    setVizConfig({
      data: initialConfig?.data || [],
      xAxisKey: initialConfig?.xAxisKey || '',
      yAxisKeys: initialConfig?.yAxisKeys || [],
      groupBy: initialConfig?.groupBy || '',
      errorColumn: initialConfig?.errorColumn || '',
      stacking: (initialConfig?.stacking as 'none' | 'normal' | 'percent' | undefined) || 'none',
      normalization: initialConfig?.normalization || false,
      missingNullHandling: initialConfig?.missingNullHandling || '0',
      type: initialConfig?.type === 'combo' || initialConfig?.type === 'stats' ? initialConfig.type : 'combo',
      primaryGroupField: initialConfig?.primaryGroupField || (initialConfig?.data && initialConfig.data.length > 0 ? Object.keys(initialConfig.data[0])[0] : 'field1') || 'field1',
      secondaryGroupField: initialConfig?.secondaryGroupField || (initialConfig?.data && initialConfig.data.length > 0 ? Object.keys(initialConfig.data[0])[1] : 'field2') || 'field2',
      title: initialConfig?.title || 'Visualization',
      description: initialConfig?.description || '',
      leftYAxisLabel: initialConfig?.leftYAxisLabel || '',
      rightYAxisLabel: initialConfig?.rightYAxisLabel || '',
    });
  };

  const updateYAxisKey = (index: number, updates: Partial<typeof vizConfig.yAxisKeys[0]>) => {
    const newYAxisKeys = [...vizConfig.yAxisKeys];
    newYAxisKeys[index] = { ...newYAxisKeys[index], ...updates };
    const updatedConfig = { ...vizConfig, yAxisKeys: newYAxisKeys };
    setVizConfig(updatedConfig);

    // Update live preview
    if (visualizationId) {
      setLivePreviewConfig(visualizationId, updatedConfig);
    }
  };

  const addYAxisKey = () => {
    const availableColumns = columns.filter(col =>
      !vizConfig.yAxisKeys.some(key => key.key === col) && col !== vizConfig.xAxisKey
    );

    if (availableColumns.length > 0) {
      const newKey = {
        key: availableColumns[0],
        color: visualizationConfig.defaultColors[vizConfig.yAxisKeys.length % visualizationConfig.defaultColors.length],
        label: availableColumns[0],
        type: "bar" as const
      };
      const updatedConfig = {
        ...vizConfig,
        yAxisKeys: [...vizConfig.yAxisKeys, newKey]
      };
      setVizConfig(updatedConfig);

      // Update live preview
      if (visualizationId) {
        setLivePreviewConfig(visualizationId, updatedConfig);
      }
    }
  };

  const removeYAxisKey = (index: number) => {
    if (vizConfig.yAxisKeys.length > 1) {
      const newYAxisKeys = vizConfig.yAxisKeys.filter((_, i) => i !== index);
      const updatedConfig = { ...vizConfig, yAxisKeys: newYAxisKeys };
      setVizConfig(updatedConfig);

      // Update live preview
      if (visualizationId) {
        setLivePreviewConfig(visualizationId, updatedConfig);
      }
    }
  };

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <div
      className={`w-full bg-white transition-all duration-300 ${
        isCollapsed
          ? 'h-auto border-t border-gray-200'
          : 'border border-gray-200 rounded-lg shadow-lg'
      }`}
    >
      {/* Header */}
      <div className="flex flex-col p-3 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <Eye className="h-4 w-4 text-blue-600" />
              <span className="font-semibold text-sm">Edit Visualization</span>
            </div>
            <span className="text-xs text-gray-500">{vizConfig.title}</span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="h-7 px-2"
            onClick={toggleCollapse}
          >
            {isCollapsed ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </div>
        <div className="flex items-center justify-end gap-2">
          <Button variant="outline" size="sm" className="h-7 px-2 text-xs" onClick={handleReset}>
            <RotateCcw className="h-3 w-3 mr-1" />
            Reset
          </Button>
          {onCancel && (
            <Button variant="outline" size="sm" className="h-7 px-2 text-xs" onClick={() => {
              // Clear live preview when canceling
              if (visualizationId) {
                clearLivePreviewConfig(visualizationId);
              }
              onCancel();
            }}>
              <X className="h-3 w-3 mr-1" />
              Cancel
            </Button>
          )}
          <Button size="sm" className="h-7 px-2 text-xs" onClick={handleApplyChanges}>
            <Save className="h-3 w-3 mr-1" />
            Save Changes
          </Button>
        </div>
      </div>

      {/* Content */}
      {!isCollapsed && (
        <div className="flex flex-col overflow-scroll">


          {/* Settings Panel */}
          <div className="border-t border-gray-200 bg-white/90 backdrop-blur-sm overflow-y-auto shadow-lg">
            <div className="p-6">
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 mb-2">Configuration</h3>
                <p className="text-sm text-gray-600">Customize your visualization settings</p>
              </div>
              <Tabs
                value={activeTab}
                onValueChange={(value) => setActiveTab(value as 'general' | 'data' | 'styling' | 'stats')}
              >
                <TabsList className="flex w-full bg-gray-100/50 p-1 mb-6">
                  <TabsTrigger value="general" className="flex-1 justify-center data-[state=active]:bg-white data-[state=active]:shadow-sm">General</TabsTrigger>
                  {vizConfig.type === 'combo' && (
                    <>
                      <TabsTrigger value="data" className="flex-1 justify-center data-[state=active]:bg-white data-[state=active]:shadow-sm">Data</TabsTrigger>
                      <TabsTrigger value="styling" className="flex-1 justify-center data-[state=active]:bg-white data-[state=active]:shadow-sm">Styling</TabsTrigger>
                    </>
                  )}
                  {vizConfig.type === 'stats' && (
                    <TabsTrigger value="stats" className="flex-1 justify-center data-[state=active]:bg-white data-[state=active]:shadow-sm">Stats</TabsTrigger>
                  )}
                </TabsList>
              </Tabs>
              <div className="mt-6 space-y-8">
                {activeTab === "general" && (
                  <div className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="title" className="text-sm font-medium text-gray-700">Title</Label>
                      <Input
                        id="title"
                        value={vizConfig.title}
                        onChange={(e) => handleSettingsChange({ title: e.target.value })}
                        className="bg-white/50 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 w-full px-3 py-2"
                        placeholder="Enter visualization title"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="description" className="text-sm font-medium text-gray-700">Description</Label>
                      <Input
                        id="description"
                        value={vizConfig.description}
                        onChange={(e) => handleSettingsChange({ description: e.target.value })}
                        className="bg-white/50 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 w-full px-3 py-2"
                        placeholder="Enter visualization description"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="type" className="text-sm font-medium text-gray-700">Chart Type</Label>
                      <Select
                        value={vizConfig.type}
                        onValueChange={(value: any) => handleSettingsChange({ type: value })}
                      >
                        <SelectTrigger className="bg-white/50 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 w-full px-3 py-2">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="combo">Combo Chart</SelectItem>
                          <SelectItem value="stats">Stats Display</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}
                {activeTab === "data" && vizConfig.type === 'combo' && (
                  <div className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="xAxis" className="text-sm font-medium text-gray-700">X-Axis Columns</Label>
                      <MultiSelect
                        options={columns.map(col => ({ value: col, label: col }))}
                        onValueChange={(values) => {
                          const xAxisKey = values.length === 1 ? values[0] : values;
                          handleSettingsChange({ xAxisKey });
                        }}
                        defaultValue={Array.isArray(vizConfig.xAxisKey) ? vizConfig.xAxisKey : (vizConfig.xAxisKey ? [vizConfig.xAxisKey] : [])}
                        placeholder="Select X-axis columns..."
                        variant="inverted"
                        animation={2}
                        maxCount={3}
                        className="bg-white/50 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 w-full"
                      />
                      {Array.isArray(vizConfig.xAxisKey) && vizConfig.xAxisKey.length > 1 && (
                        <p className="text-sm text-gray-600 mt-2">
                          Data will be grouped by: <span className="font-mono bg-gray-100 px-2 py-1 rounded">{vizConfig.xAxisKey.join(', ')}</span>
                        </p>
                      )}
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-medium text-gray-700">Y-Axis Columns</Label>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={addYAxisKey}
                          className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
                        >
                          Add Column
                        </Button>
                      </div>
                      <div className="space-y-3">
                        {vizConfig.yAxisKeys.map((yKey, index) => (
                          <Card key={index} className="p-4 bg-gradient-to-r from-gray-50 to-blue-50/30 border-gray-200/50 w-full">
                            <div className="space-y-3">
                              <div className="flex items-center justify-between">
                                <Label className="text-sm font-medium text-gray-700">Column {index + 1}</Label>
                                {vizConfig.yAxisKeys.length > 1 && (
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() => removeYAxisKey(index)}
                                    className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600"
                                  >
                                    <X className="h-3 w-3" />
                                  </Button>
                                )}
                              </div>
                              <Select
                                value={yKey.key}
                                onValueChange={(value) => updateYAxisKey(index, { key: value })}
                              >
                                <SelectTrigger className="bg-white/70 border-gray-200 w-full">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {columns.map(col => (
                                    <SelectItem key={col} value={col}>{col}</SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <Input
                                placeholder="Custom label (optional)"
                                value={yKey.label || yKey.key}
                                onChange={(e) => updateYAxisKey(index, { label: e.target.value })}
                                className="bg-white/70 border-gray-200 w-full px-3 py-2"
                              />
                              <div className="flex items-center gap-2">
                                <div className="flex items-center gap-2">
                                  <Input
                                    type="color"
                                    value={yKey.color}
                                    onChange={(e) => updateYAxisKey(index, { color: e.target.value })}
                                    className="w-10 h-8 p-1 border-gray-200"
                                  />
                                  <span className="text-xs text-gray-500 font-mono">{yKey.color}</span>
                                </div>
                                <Select
                                  value={yKey.type || "bar"}
                                  onValueChange={(value: any) => updateYAxisKey(index, { type: value })}
                                >
                                  <SelectTrigger className="flex-1 bg-white/70 border-gray-200">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="bar">Bar</SelectItem>
                                    <SelectItem value="line">Line</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                            </div>
                          </Card>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
                {activeTab === "styling" && vizConfig.type === 'combo' && (
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="leftYAxisLabel">Left Y-Axis Label</Label>
                      <Input
                        id="leftYAxisLabel"
                        value={vizConfig.leftYAxisLabel || ""}
                        onChange={(e) => handleSettingsChange({ leftYAxisLabel: e.target.value })}
                        className="mt-1 w-full px-3 py-2"
                      />
                    </div>
                    <div>
                      <Label htmlFor="rightYAxisLabel">Right Y-Axis Label</Label>
                      <Input
                        id="rightYAxisLabel"
                        value={vizConfig.rightYAxisLabel || ""}
                        onChange={(e) => handleSettingsChange({ rightYAxisLabel: e.target.value })}
                        className="mt-1 w-full px-3 py-2"
                      />
                    </div>
                    <div>
                      <Label htmlFor="stacking">Bar Stacking</Label>
                      <Select
                        value={vizConfig.stacking || 'none'}
                        onValueChange={(value) => handleSettingsChange({ stacking: value })}
                      >
                        <SelectTrigger id="stacking" className="mt-1 w-full px-3 py-2">
                          <SelectValue placeholder="Select stacking type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">None</SelectItem>
                          <SelectItem value="normal">Normal</SelectItem>
                          <SelectItem value="percent">Percentage</SelectItem>
                        </SelectContent>
                      </Select>
                      <p className="text-xs text-gray-500 mt-1">
                        Stacked bars combine multiple data series on top of each other
                      </p>
                    </div>
                  </div>
                )}
                {activeTab === "stats" && vizConfig.type === 'stats' && (
                  <StatsSettingsTab
                    data={vizConfig.data}
                    yAxisKeys={vizConfig.yAxisKeys}
                    onSettingsChange={(settings) => {
                      // Pass all settings directly to handleSettingsChange
                      handleSettingsChange(settings);
                    }}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Collapsed State Info */}
      {isCollapsed && (
        <div className="flex items-center justify-between p-2 text-xs text-gray-500">
          <div className="flex items-center gap-4">
            <div>
              <span>Dashboard: </span>
              <Link href="#" className="text-blue-600 hover:underline">
                {dashboardId || "None"}
              </Link>
            </div>
            <div>
              <span>Visualization: </span>
              <Link href="#" className="text-blue-600 hover:underline">
                {visualizationId || "None"}
              </Link>
            </div>
          </div>
          <span className="text-gray-400">Click to expand visualization editor</span>
        </div>
      )}
    </div>
  );
}
