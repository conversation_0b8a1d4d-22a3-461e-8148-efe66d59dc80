import { TabSectionHeading } from "@/components/custom/TabSectionHeading";
import { dashboards } from "@/app/data/hardcodeddata/dashboardStructures";
import { LayoutDashboard, ExternalLink, User } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface AllDashboardsTabProps {
  onDashboardSelect: (dashboardId: string) => void;
}

export function AllDashboardsTab({ onDashboardSelect }: AllDashboardsTabProps) {   
    // Convert dashboards object to array for easier mapping
    const dashboardList = Object.values(dashboards);
    
    // Separate system and custom dashboards
    const systemDashboards = dashboardList.filter(dashboard => dashboard.user === "System");
    const customDashboards = dashboardList.filter(dashboard => dashboard.user !== "System");
    
    // Handle selecting a dashboard
    const handleDashboardClick = (dashboardId: string) => {
        console.log("AllDashboardsTab: Dashboard clicked:", dashboardId);
        // Call the parent component's function
        onDashboardSelect(dashboardId);
    };
    
    // Dashboard Card component for reuse
    const DashboardCard = ({ dashboard }: { dashboard: typeof dashboardList[0] }) => (
        <Card 
            key={dashboard.dashboard_id} 
            className="hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => handleDashboardClick(dashboard.dashboard_id)}
        >
            <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                    <CardTitle className="text-lg text-blue-500">{dashboard.title}</CardTitle>
                    <span className="text-xs text-gray-400">ID: {dashboard.dashboard_id}</span>
                </div>
                <CardDescription>{dashboard.description}</CardDescription>
            </CardHeader>
            <CardContent>
                <div className="flex items-center justify-between w-full">
                    <div className="text-sm text-gray-500">
                        {dashboard.visualizationIds.length} visualization{dashboard.visualizationIds.length !== 1 ? 's' : ''}
                    </div>
                    <div className="text-xs text-blue-500 flex items-center gap-1">
                        <User className="h-3 w-3" />
                        {dashboard.user}
                    </div>
                </div>
            </CardContent>
        </Card>
    );
    
    return (
        <div className="space-y-6">
            <TabSectionHeading 
                icon={LayoutDashboard} 
                iconColorClass="text-blue-500" 
                showSeparator
            >
                <div className="flex justify-between items-center w-full">
                    <div>System Dashboards</div>
                </div>
            </TabSectionHeading>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {systemDashboards.map((dashboard) => (
                    <DashboardCard key={dashboard.dashboard_id} dashboard={dashboard} />
                ))}
            </div>

            <TabSectionHeading 
                icon={LayoutDashboard} 
                iconColorClass="text-blue-500" 
                showSeparator
            >
                <div className="flex justify-between items-center w-full">
                    <div>Custom Dashboards</div>
                </div>
            </TabSectionHeading>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {customDashboards.map((dashboard) => (
                    <DashboardCard key={dashboard.dashboard_id} dashboard={dashboard} />
                ))}
            </div>
        </div>
    );
}
