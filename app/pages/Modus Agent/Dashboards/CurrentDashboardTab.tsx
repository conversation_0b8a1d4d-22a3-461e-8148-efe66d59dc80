import React, { useState, useEffect } from "react";
import { Visualization } from '@/components/custom/visualization';
import { TabSectionHeading } from "@/components/custom/TabSectionHeading";
import { LayoutDashboard } from "lucide-react";
import {
  currentDashboardId,
  dashboards
} from "@/app/data/hardcodeddata/dashboardStructures";
import { Responsive, WidthProvider } from "react-grid-layout";
import "react-grid-layout/css/styles.css";
import "react-resizable/css/styles.css";
import { useArtifactStore } from "@/app/store/artifact/artifactStore";
import { useVisualizationStore } from "@/app/store/visualization/visualizationStore";
import { useLivePreviewStore } from "@/app/store/visualization/livePreviewStore";
import { VisualizationPanel } from "@/app/pages/Modus Agent/VisualizationPanel/VisualizationPanel";

// Create a responsive grid layout
const ResponsiveGridLayout = WidthProvider(Responsive);

interface CurrentDashboardTabProps {
  selectedDashboardId: string | null;
}

export function CurrentDashboardTab({ selectedDashboardId }: CurrentDashboardTabProps) {
  // State to track active visualization
  const [activeVizId, setActiveVizId] = useState<string | null>(null);

  // Define dragHandleClass for the grid layout
  const dragHandleClass = "drag-handle";

  // Define initial layouts for the grid
  const [layouts, setLayouts] = useState({});

  // Access stores for tab management and visualization updates
  const { addTab, setActiveTabId, removeTab } = useArtifactStore();
  const { visualizations: storeVisualizations, updateVisualization } = useVisualizationStore();
  const { livePreviewConfigs, setLivePreviewConfig, clearLivePreviewConfig } = useLivePreviewStore();

  // Reset active visualization and update layouts when dashboard changes
  useEffect(() => {
    setActiveVizId(null);

    // Use the selectedDashboardId if provided, otherwise fall back to currentDashboardId
    const dashboardId = selectedDashboardId || currentDashboardId;

    // Initialize layouts when dashboard changes
    if (dashboardId && dashboards[dashboardId]) {
      const dashboard = dashboards[dashboardId];
      const vizIds = dashboard.visualizationIds;

      // Create layout objects for each visualization
      const initialLayouts = {
        lg: vizIds.map((vizId, index) => ({
          i: vizId,
          x: (index % 2) * 6,  // alternate between left and right
          y: Math.floor(index / 2) * 4,  // new row every 2 items
          w: 6,  // half width
          h: 4,  // fixed height
        }))
      };

      setLayouts(initialLayouts);
    }
  }, [selectedDashboardId]);

  // Handle layout changes
  const onLayoutChange = (_currentLayout: any, allLayouts: any) => {
    setLayouts(allLayouts);
  };

  // Use selectedDashboardId if provided, otherwise fall back to currentDashboardId
  const dashboardId = selectedDashboardId || currentDashboardId;

  // Return a message if no dashboard is selected or the dashboard doesn't exist
  if (!dashboardId || !dashboards[dashboardId]) {
    return <div className="p-6 text-center text-gray-500">Please select a dashboard to view</div>;
  }

  const dashboard = dashboards[dashboardId];

  // Handle edit button click on visualization
  const handleEditVisualization = (vizId: string) => {
    console.log('Edit button clicked for visualization:', vizId);
    setActiveVizId(vizId);

    // Get the visualization data from store
    const viz = storeVisualizations[vizId];
    console.log('vizId', vizId);
    console.log('Viz: viz', viz);
    if (!viz) {
      console.error('Visualization not found:', vizId);
      return;
    }

    console.log('Found visualization:', viz);

    // Create a tab for editing without API calls
    const tabId = `viz-edit-${vizId}`;
    console.log('Creating edit tab with ID:', tabId);

    // Add the tab for visualization editing
    addTab({
      id: tabId,
      title: `Edit: ${viz.title}`,
      renderArtifact: () => {
        console.log('Rendering VisualizationPanel for tab:', tabId);
        return (
          <VisualizationPanel
            visualization={viz}
            dashboardId={dashboardId}
            visualizationId={vizId}
            onSave={(updatedViz) => {
              // Update the visualization in the store
              updateVisualization(vizId, updatedViz);
              console.log('Visualization updated successfully:', updatedViz);

              // Close the edit tab
              removeTab(tabId);

              // Show success message or notification here if needed
            }}
            onCancel={() => {
              // Close the edit tab
              removeTab(tabId);
            }}
          />
        );
      }
    });

    console.log('Tab added, now activating tab:', tabId);
    // Activate the tab - the addTab function should have already set isCollapsed: false
    // But we'll call setActiveTabId to ensure it's active and expanded
    setActiveTabId(tabId);

    // Add a small delay to ensure the tab is properly activated and visible
    setTimeout(() => {
      console.log('Checking if tab is visible and panel is expanded');
      // Force focus on the artifact panel if needed
      const artifactPanel = document.querySelector('[data-active-tab-id]');
      if (artifactPanel) {
        console.log('Artifact panel found, scrolling into view');
        artifactPanel.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
      } else {
        console.warn('Artifact panel not found in DOM');
      }
    }, 100);

    console.log('Tab activated and panel should be expanded');
  };

  return (
    <div className="space-y-6">
      <TabSectionHeading icon={LayoutDashboard} iconColorClass="text-blue-500" showSeparator>
        <div className="flex justify-between items-center w-full">
          <div>{dashboard.title}</div>
        </div>
      </TabSectionHeading>

      {/* Dashboard description and ID */}
      <div className="flex justify-between items-center text-sm mb-4">
        <p className="text-gray-500">{dashboard.description} (ID: {dashboard.dashboard_id})</p>
        <div className="flex items-center gap-4">
          <button
            onClick={() => handleEditVisualization('viz-001')}
            className="px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600"
          >
            Test Edit viz-001
          </button>
          <p className="text-sm text-blue-500 italic">Drag to reposition & resize visualizations</p>
        </div>
      </div>

      {/* Revised style that avoids double border */}
      <style jsx global>{`
        .visualization-drag-area {
          padding: 0 !important;
          background: white;
          border-radius: 0.5rem;
          overflow: hidden;
        }
        .visualization-drag-area.selected > div {
          box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
        }
        .selected .visualization-card {
          border: 2px solid #3b82f6 !important;
        }
        .custom-drag-handle {
          cursor: move;
          padding: 5px;
          position: absolute;
          top: 5px;
          right: 5px;
          z-index: 100;
          opacity: 0.5;
          background-color: rgba(255, 255, 255, 0.8);
          border-radius: 4px;
        }
        .custom-drag-handle:hover {
          opacity: 1;
        }
      `}</style>

      <ResponsiveGridLayout
        className="layout"
        layouts={layouts}
        breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
        cols={{ lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 }}
        rowHeight={100}
        onLayoutChange={onLayoutChange}
        draggableHandle={`.${dragHandleClass}`}
        isDraggable={true}
        isResizable={true}
      >
        {dashboard.visualizationIds.map((vizId) => {
          const viz = storeVisualizations[vizId];
          if (!viz) return null;

          // Check if there's a live preview config for this visualization
          const livePreviewConfig = livePreviewConfigs[vizId];

          return (
            <div
              key={viz.visualization_id}
              className={`visualization-drag-area ${activeVizId === viz.visualization_id ? 'selected' : ''}`}
              onClick={() => setActiveVizId(viz.visualization_id)}
            >
              {/* Drag handle positioned on top of the visualization */}
              <div className={`${dragHandleClass} custom-drag-handle`}>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M8 5H6V7H8V5Z" fill="currentColor" />
                  <path d="M18 5H16V7H18V5Z" fill="currentColor" />
                  <path d="M8 11H6V13H8V11Z" fill="currentColor" />
                  <path d="M18 11H16V13H18V11Z" fill="currentColor" />
                  <path d="M8 17H6V19H8V17Z" fill="currentColor" />
                  <path d="M18 17H16V19H18V17Z" fill="currentColor" />
                </svg>
              </div>

              <Visualization
                type={viz.type}
                title={viz.title}
                description={viz.description}
                data={viz.data}
                xAxisKey={viz.xAxisKey}
                yAxisKeys={viz.yAxisKeys}
                className={`visualization-card ${viz.className}`}
                visualizationId={viz.visualization_id}
                dashboardId={viz.dashboard_id}
                isActive={false} // Let the container handle the active styling
                onEdit={() => handleEditVisualization(viz.visualization_id)}
                isLivePreview={!!livePreviewConfig}
                livePreviewConfig={livePreviewConfig}
                stacking={viz.stacking}
                onUpdate={(updatedProps) => {
                  // Update the visualization in the store with the new props
                  // Map the VisualizationProps to Visualization interface
                  const mappedUpdates: any = {};
                  if (updatedProps.type) mappedUpdates.type = updatedProps.type;
                  if (updatedProps.data) mappedUpdates.data = updatedProps.data;
                  if (updatedProps.title) mappedUpdates.title = updatedProps.title;
                  if (updatedProps.description) mappedUpdates.description = updatedProps.description;
                  if (updatedProps.xAxisKey) mappedUpdates.xAxisKey = updatedProps.xAxisKey;
                  if (updatedProps.yAxisKeys) {
                    // Ensure yAxisKeys have the required type property
                    mappedUpdates.yAxisKeys = updatedProps.yAxisKeys.map(key => ({
                      ...key,
                      type: key.type || 'bar' // Default to 'bar' if type is undefined
                    }));
                  }
                  if (updatedProps.leftYAxisLabel) mappedUpdates.leftYAxisLabel = updatedProps.leftYAxisLabel;
                  if (updatedProps.rightYAxisLabel) mappedUpdates.rightYAxisLabel = updatedProps.rightYAxisLabel;
                  if (updatedProps.stacking) mappedUpdates.stacking = updatedProps.stacking;

                  updateVisualization(viz.visualization_id, mappedUpdates);
                }}
              />
            </div>
          );
        })}
      </ResponsiveGridLayout>
    </div>
  );
}
