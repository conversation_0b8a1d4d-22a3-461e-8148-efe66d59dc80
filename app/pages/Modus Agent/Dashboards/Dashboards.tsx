import { Workspace } from '@/app/layout/Workspace/Workspace';
import { FC, useState, useEffect } from 'react';
import { AllDashboardsTab } from './AllDashboardsTab';
import { CurrentDashboardTab } from './CurrentDashboardTab';
import { dashboards, currentDashboardId } from '@/app/data/hardcodeddata/dashboardStructures';
import { useWorkspaceStore } from '@/app/store/workspace/workspaceStore';

const Dashboards: FC = () => { 
  // Add state to track the selected dashboard
  const [selectedDashboardId, setSelectedDashboardId] = useState<string | null>(currentDashboardId);
  // Add state to track the active tab
  const [activeTabId, setActiveTabId] = useState<string>("all-dashboards");
  
  // Get the workspace store functions
  const setStoreActiveTab = useWorkspaceStore(state => state.setActiveTab);
  const setActiveNavigation = useWorkspaceStore(state => state.setActiveNavigation);
  const refreshActiveTab = useWorkspaceStore(state => state.refreshActiveTab);
  
  // Set initial active tab and navigation
  useEffect(() => {
    // Set active navigation in the workspace store
    setActiveNavigation({
      group: "Modus Agent",
      item: "Dashboards"
    });
    
    // Set initial active tab in the store
    setStoreActiveTab(activeTabId, activeTabId === "all-dashboards" ? "All Dashboards" : "Current Dashboard");
  }, []);
  
  // Handle dashboard selection
  const handleDashboardSelect = (dashboardId: string) => {
    console.log("Dashboard selected:", dashboardId);
    
    // Set the selected dashboard ID
    setSelectedDashboardId(dashboardId);
    
    // Set the active tab to current-dashboard
    setActiveTabId("current-dashboard");
    
    // Refresh to ensure the content updates
    refreshActiveTab();
  };

  // Update the workspace store when activeTabId changes
  useEffect(() => {
    if (activeTabId === "current-dashboard") {
      setStoreActiveTab("current-dashboard", "Current Dashboard");
    } else {
      setStoreActiveTab("all-dashboards", "All Dashboards");
    }
  }, [activeTabId, setStoreActiveTab]);

  const tabs = [
    {
      id: "all-dashboards",
      label: "All Dashboards",
      content: <AllDashboardsTab onDashboardSelect={handleDashboardSelect} />
    },
    {
      id: "current-dashboard",
      label: "Current Dashboard",
      content: <CurrentDashboardTab selectedDashboardId={selectedDashboardId} />
    },
  ];
  
  return (
    <Workspace 
      key={`workspace-${selectedDashboardId}`} 
      tabs={tabs} 
      initialActiveTabId={activeTabId}
    />
  );
}

export default Dashboards;