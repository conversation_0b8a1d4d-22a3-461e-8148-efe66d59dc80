import * as React from "react"
import { useArtifactStore } from "@/app/store/artifact/artifactStore"
import { useChatStore } from "@/app/store/chat/chatStore"
import { chatService } from "@/app/services/chat"
import { InvestigationGPTChat } from "@/app/layout/InvestigationGPT/InvestigationGPTChat"
import { ChatHistoryArtifact } from "@/app/components/artifacts/ChatHistoryArtifact"
import { formatDistanceToNow } from 'date-fns'
import { BubbleTag } from "@/components/custom/BubbleTag"
import { Skeleton } from "@/components/ui/skeleton"

// ChatItem component for individual chat entries
function ChatItem({ id, hasVisualization, title, createdAt, onClick }: {
  id: string
  hasVisualization: boolean
  title: string
  createdAt: string
  onClick: () => void
}) {
  // Format date to show exact datetime
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString)
      if (isNaN(date.getTime())) {
        return '-'
      }
      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch (error) {
      return '-'
    }
  }

  return (
    <div 
      onClick={onClick}
      className="p-4 border-b hover:bg-gray-50 cursor-pointer"
    >
      <div className="flex justify-between items-start mb-2">
        <div>
          <h3 className="font-medium">{title || 'Unnamed Chat'}</h3>
        </div>
        <span className="text-sm text-gray-400">
          {formatDate(createdAt)}
        </span>
      </div>
      <div className="flex items-center justify-between">
        <p className="text-sm text-gray-500">Chat {id}</p>
        {hasVisualization && (
          <BubbleTag
            text="Visualization"
            color="yellow"
          />
        )}
      </div>
    </div>
  )
}

export function ChatHistoryTab() {
  const { addTab, setActiveTabId, setCollapsed } = useArtifactStore()
  const { allChatIds, fetchAllChatIds } = useChatStore()
  const [isLoading, setIsLoading] = React.useState(true)
  const [error, setError] = React.useState<string | null>(null)

  // Fetch all chat IDs
  React.useEffect(() => {
    const fetchChats = async () => {
      setIsLoading(true)
      setError(null)
      try {
        await fetchAllChatIds()
      } catch (err) {
        setError('Failed to load chat history. Please try again later.')
        console.error('Error fetching chat history:', err)
      } finally {
        setIsLoading(false)
      }
    }
    
    fetchChats()
  }, [fetchAllChatIds])

  // Sort chat IDs by creation date
  const sortedChatIds = React.useMemo(() => {
    return [...(allChatIds || [])].sort((a, b) => {
      const dateA = a.created_at ? new Date(a.created_at).getTime() : 0
      const dateB = b.created_at ? new Date(b.created_at).getTime() : 0
      return dateB - dateA // Sort in descending order (newest first)
    })
  }, [allChatIds])

  // Format chat title - handle null titles gracefully
  const formatChatTitle = React.useCallback((chatInfo: typeof allChatIds[0]) => {
    if (chatInfo.chat_title) return chatInfo.chat_title
    
    // If title is null, use a fragment of the chat ID
    return `Chat ${chatInfo.chat_id.slice(0, 8)}...`
  }, [])

  const handleChatClick = React.useCallback((chatId: string, chatTitle: string) => {
    // Use the chat_title from API response or fallback to a default
    const title = chatTitle || `Chat ${chatId.slice(0, 8)}`
    
    // Create unique tab ID with prefix to avoid confusing the tab system
    const tabId = `chat-artifact-${chatId}`
    
    addTab({
      id: tabId, // Use prefixed ID for the tab
      title: `${title}`,
      renderArtifact: () => (
        <ChatHistoryArtifact chatId={chatId} />
      )
    })
    setActiveTabId(tabId)
    setCollapsed(false)
  }, [addTab, setActiveTabId, setCollapsed])

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b bg-white">
        <h2 className="text-xl font-semibold">Chat History</h2>
        <p className="text-sm text-gray-500">
          Total Chats: {allChatIds?.length || 0}
        </p>
      </div>

      <div className="flex-1 overflow-auto">
        {isLoading ? (
          // Loading state
          Array(5).fill(0).map((_, index) => (
            <div key={index} className="p-4 border-b">
              <div className="flex justify-between mb-2">
                <Skeleton className="h-5 w-1/3" />
                <Skeleton className="h-4 w-1/4" />
              </div>
              <Skeleton className="h-4 w-1/5 mt-2" />
            </div>
          ))
        ) : error ? (
          // Error state
          <div className="p-6 text-center">
            <p className="text-red-500">{error}</p>
            <button 
              onClick={() => fetchAllChatIds()}
              className="mt-4 px-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-600"
            >
              Retry
            </button>
          </div>
        ) : allChatIds.length === 0 ? (
          // Empty state
          <div className="p-6 text-center text-gray-500">
            <p>No chat history found</p>
          </div>
        ) : (
          // Chat list
          sortedChatIds.map((chatInfo) => (
            <ChatItem
              key={chatInfo.chat_id}
              id={chatInfo.chat_id}
              hasVisualization={chatInfo.has_visualization}
              title={formatChatTitle(chatInfo)}
              createdAt={chatInfo.created_at}
              onClick={() => handleChatClick(chatInfo.chat_id, formatChatTitle(chatInfo))}
            />
          ))
        )}
      </div>
    </div>
  )
}