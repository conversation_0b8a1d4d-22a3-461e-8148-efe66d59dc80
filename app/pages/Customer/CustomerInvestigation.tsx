import { Workspace } from '@/app/layout/Workspace/Workspace'
import { CustomerOverviewTab } from './CustomerOverviewTab';
import { CustomerRedFlagsTab } from './CustomerRedFlagsTab';
import { CustomerLinkagesTab } from './CustomerLinkagesTab';
import { CustomerDigitalFootprintTab } from './CustomerDigitalFootprintTab';

export default function CustomerInvestigation() {
  const tabs = [
    {
      id: 'overview',
      label: 'Overview',
      content: <CustomerOverviewTab />
    },
    {
      id: 'flags',
      label: 'Red Flags',
      content: <CustomerRedFlagsTab />
    },
    {
      id: 'linkages',
      label: 'Linkages',
      content: <CustomerLinkagesTab />
    },
    {
      id: 'digital-footprint',
      label: 'Digital Footprint',
      content: <CustomerDigitalFootprintTab />
    }
  ];

  return <Workspace tabs={tabs} />;
} 