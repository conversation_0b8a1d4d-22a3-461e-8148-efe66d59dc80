import { RedFlag } from '@/app/store/merchant/InvestigationRedFlagsStore';

// Sample customer data
export const customerData = {
  overview: {
    name: '<PERSON><PERSON>',
    customerId: 'CUST123456',
    email: 'r<PERSON><PERSON>.<EMAIL>',
    phone: '+91 **********',
    address: '123 Gagan Street, Jaipur, Rajasthan',
    kycStatus: 'Verified',
    accountCreated: '2023-01-15',
    lastActive: '2024-03-20',
    riskScore: 75,
    riskLevel: 'Medium',
    totalTransactions: 156,
    totalSpent: 250000,
    avgTransactionValue: 1602.56,
    preferredPaymentMethods: ['UPI', 'Credit Card', 'Net Banking'],
    transactionLocations: ['Mumbai', 'Delhi', 'Bangalore'],
    documents: [
      {
        id: 'DOC001',
        name: 'Aadhaar Card',
        type: 'KYC',
        category: 'Identity Proof',
        uploadDate: '2023-01-12',
        status: 'Verified',
        verificationDate: '2023-01-14',
        fileType: 'PDF',
        fileSize: '1.2 MB'
      },
      {
        id: 'DOC002',
        name: 'PA<PERSON> <PERSON>',
        type: 'KYC',
        category: 'Identity Proof',
        uploadDate: '2023-01-12',
        status: 'Verified',
        verificationDate: '2023-01-14',
        fileType: 'JPEG',
        fileSize: '0.8 MB'
      },
      {
        id: 'DOC003',
        name: 'Utility Bill',
        type: 'KYC',
        category: 'Address Proof',
        uploadDate: '2023-01-12',
        status: 'Verified',
        verificationDate: '2023-01-15',
        fileType: 'PDF',
        fileSize: '2.1 MB'
      },
      {
        id: 'DOC004',
        name: 'Income Certificate',
        type: 'Financial',
        category: 'Income Proof',
        uploadDate: '2023-01-20',
        status: 'Verified',
        verificationDate: '2023-01-22',
        fileType: 'PDF',
        fileSize: '1.5 MB'
      },
      {
        id: 'DOC005',
        name: 'Bank Statement',
        type: 'Financial',
        category: 'Financial History',
        uploadDate: '2023-02-05',
        status: 'Verified',
        verificationDate: '2023-02-07',
        fileType: 'PDF',
        fileSize: '3.2 MB'
      }
    ],
    creditInsights: {
      activeAccounts: 5,
      loanTypes: 'BNPL (Buy Now Pay Later)',
      loanTicketSize: '₹6,700',
      totalCreditExposure: '₹33,500',
      paymentHistory: 'No Defaults',
      creditAge: '9 months',
      creditScore: 648,
      recentEnquiries: 8,
      linkedDevices: 3,
      creditEnquiries: [
        {
          enquiryDate: '2025-04-28',
          institution: 'LazyPay',
          productType: 'BNPL',
          enquiryPurpose: 'New Purchase Financing',
          amount: 6500,
          outcome: 'Approved'
        },
        {
          enquiryDate: '2025-04-15',
          institution: 'ZestMoney',
          productType: 'BNPL',
          enquiryPurpose: 'Grocery/Essentials Loan',
          amount: 7200,
          outcome: 'Approved'
        },
        {
          enquiryDate: '2025-03-29',
          institution: 'KreditBee',
          productType: 'Personal Loan',
          enquiryPurpose: 'Mobile Recharge Expense',
          amount: 5000,
          outcome: 'Declined'
        },
        {
          enquiryDate: '2025-03-12',
          institution: 'Amazon Pay Later',
          productType: 'BNPL',
          enquiryPurpose: 'Electronics Purchase',
          amount: 9500,
          outcome: 'Approved'
        },
        {
          enquiryDate: '2025-02-28',
          institution: 'Paytm Postpaid',
          productType: 'BNPL',
          enquiryPurpose: 'Bill Payment',
          amount: 5800,
          outcome: 'Approved'
        },
        {
          enquiryDate: '2025-02-10',
          institution: 'Kissht',
          productType: 'BNPL',
          enquiryPurpose: 'Festival Shopping',
          amount: 10000,
          outcome: 'Declined'
        }
      ],
      tradelines: [
        {
          lender: 'ZestMoney',
          productType: 'BNPL',
          loanStart: '2025-01-20',
          status: 'Active',
          sanctionedAmount: 6000,
          currentBalance: 800,
          dpd: 0
        },
        {
          lender: 'LazyPay',
          productType: 'BNPL',
          loanStart: '2024-12-15',
          status: 'Closed',
          sanctionedAmount: 5500,
          currentBalance: 0,
          dpd: 0
        },
        {
          lender: 'KreditBee',
          productType: 'BNPL',
          loanStart: '2024-11-10',
          status: 'Active',
          sanctionedAmount: 8000,
          currentBalance: 1100,
          dpd: 0
        },
        {
          lender: 'Amazon Pay',
          productType: 'BNPL',
          loanStart: '2024-08-05',
          status: 'Written Off',
          sanctionedAmount: 7200,
          currentBalance: 7200,
          dpd: 180
        },
        {
          lender: 'MoneyTap',
          productType: 'Personal',
          loanStart: '2023-07-12',
          status: 'Active',
          sanctionedAmount: 12000,
          currentBalance: 4500,
          dpd: 30
        },
        {
          lender: 'Paytm Postpaid',
          productType: 'BNPL',
          loanStart: '2023-11-22',
          status: 'Closed',
          sanctionedAmount: 5000,
          currentBalance: 0,
          dpd: 0
        }
      ]
    },
    applicationDetails: {
      date: '2023-01-10',
      kycStatus: 'Completed',
      onboardingRisk: 'Medium'
    },
    customerDetails: {
      occupation: 'Farmer',
      annualIncome: '₹1L',
      age: 56
    },
    accountDetails: {
      type: 'Savings Account',
      status: 'Active',
      accountSince: '6 months'
    },
    deviceInfo: {
      primaryDevice: 'iPhone 13',
      browser: 'Chrome',
      os: 'iOS 15.4',
      lastLoginIP: '*************'
    },
    transactionSummary: {
      totalAmount: 700000, // ₹7L
      numTransactions: 40,
      avgAmount: 17000, // ₹17k
      uniqueBeneficiaries: 12,
      cashWithdrawal: 400000, // ₹4L
      topCustomerCity: 'Rajasthan',
      lastTransactions: [
        {
          time: '21:05',
          date: '3rd Nov',
          type: 'Withdrawal',
          location: 'Mumbai',
          amount: 40900, // ₹40,900
          channel: 'ATM'
        },
        {
          time: '20:59',
          date: '3rd Nov',
          type: 'Withdrawal',
          location: 'Mumbai',
          amount: 39800, // ₹39,800
          channel: 'ATM'
        },
        {
          time: '14:23',
          date: '2nd Nov',
          type: 'Transfer',
          location: 'Delhi',
          amount: 25000, // ₹25,000
          channel: 'UPI'
        },
        {
          time: '10:45',
          date: '1st Nov',
          type: 'Payment',
          location: 'Bangalore',
          amount: 12500, // ₹12,500
          channel: 'Net Banking'
        },
        {
          time: '18:30',
          date: '31st Oct',
          type: 'Deposit',
          location: 'Rajasthan',
          amount: 50000, // ₹50,000
          channel: 'Bank Transfer'
        }
      ]
    }
  },
  linkages: {
    devices: [
      {
        id: 'DEV001',
        type: 'iPhone 13',
        firstSeen: '2023-01-15',
        lastSeen: '2024-03-20',
        ipAddresses: ['*************', '*************'],
        sharedWith: ['CUST789012', 'CUST345678']
      },
      {
        id: 'DEV002',
        type: 'Windows PC',
        firstSeen: '2023-03-20',
        lastSeen: '2024-03-15',
        ipAddresses: ['*************'],
        sharedWith: ['CUST789012']
      }
    ],
    emails: [
      {
        email: '<EMAIL>',
        verified: true,
        firstUsed: '2023-01-15',
        lastUsed: '2024-03-20',
        linkedAccounts: ['CUST789012']
      }
    ],
    phones: [
      {
        number: '+91 **********',
        verified: true,
        firstUsed: '2023-01-15',
        lastUsed: '2024-03-20',
        linkedAccounts: ['CUST345678']
      }
    ],
    addresses: [
      {
        address: '123 Main Street, Mumbai, Maharashtra',
        type: 'Residential',
        verified: true,
        firstUsed: '2023-01-15',
        lastUsed: '2024-03-20',
        sharedWith: ['CUST789012', 'CUST345678']
      }
    ]
  },
  digitalFootprint: {
    devices: {
      total: 3,
      active: 2,
      history: [
        {
          deviceId: 'DEV001',
          type: 'iPhone 13',
          browser: 'Chrome',
          os: 'iOS 15.4',
          firstSeen: '2023-01-15',
          lastSeen: '2024-03-20',
          loginCount: 156,
          ipAddresses: ['*************', '*************'],
          locations: ['Mumbai', 'Delhi']
        },
        {
          deviceId: 'DEV002',
          type: 'Windows PC',
          browser: 'Firefox',
          os: 'Windows 11',
          firstSeen: '2023-03-20',
          lastSeen: '2024-03-15',
          loginCount: 89,
          ipAddresses: ['*************'],
          locations: ['Mumbai']
        }
      ]
    },
    locations: {
      frequent: ['Mumbai', 'Delhi', 'Bangalore'],
      unusual: ['Chennai'],
      history: [
        {
          city: 'Mumbai',
          firstSeen: '2023-01-15',
          lastSeen: '2024-03-20',
          visitCount: 120,
          devices: ['DEV001', 'DEV002']
        },
        {
          city: 'Delhi',
          firstSeen: '2023-06-15',
          lastSeen: '2024-03-18',
          visitCount: 25,
          devices: ['DEV001']
        }
      ]
    },
    behavior: {
      loginTimes: {
        morning: 45,
        afternoon: 65,
        evening: 35,
        night: 11
      },
      transactionPatterns: {
        weekday: 85,
        weekend: 71,
        average_value: 1602.56
      },
      riskIndicators: {
        vpnUsage: false,
        multipleDevices: true,
        unusualLoginTimes: false,
        rapidLocationChanges: true
      }
    }
  },
  investigations: {
    active: [
      {
        investigation_id: "INV001",
        case_number: "CS1001",
        title: "Unusual Transaction Pattern",
        status: "Open",
        priority: "High",
        assignee_Name: "Rahul Sharma",
        created_at: "2024-06-01T10:30:00Z",
        description: "Multiple high-value transactions in quick succession"
      },
      {
        investigation_id: "INV002",
        case_number: "CS1002",
        title: "Suspected Money Mule",
        status: "In Progress",
        priority: "High", 
        assignee_Name: "Priya Mehta",
        created_at: "2024-05-25T14:45:00Z",
        description: "Account showing patterns consistent with money mule activity"
      }
    ],
    past: [
      {
        investigation_id: "INV003",
        case_number: "CS1003",
        title: "KYC Verification Issue",
        status: "Closed",
        priority: "Medium",
        assignee_Name: "Vikram Singh",
        created_at: "2024-02-10T09:15:00Z",
        description: "Address verification discrepancy resolved after customer provided updated documentation"
      },
      {
        investigation_id: "INV004", 
        case_number: "CS1004",
        title: "Multiple Failed Login Attempts",
        status: "Closed",
        priority: "Low",
        assignee_Name: "Anjali Patel",
        created_at: "2024-01-05T16:20:00Z",
        description: "Confirmed with customer that attempts were legitimate"
      },
      {
        investigation_id: "INV005",
        case_number: "CS1005", 
        title: "Suspicious Transfer Activity",
        status: "Closed",
        priority: "High",
        assignee_Name: "Rohit Kumar",
        created_at: "2023-11-20T11:10:00Z",
        description: "Investigated and confirmed legitimate business transactions"
      }
    ]
  }
};
