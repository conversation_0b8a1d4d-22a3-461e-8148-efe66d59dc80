import { FC } from 'react';
import { Smartphone, MapPin, Activity, AlertTriangle } from 'lucide-react';
import { CustomCard } from '@/components/custom/CustomCard';
import { customerData } from './CustomerSampleData';
import { format } from 'date-fns';
import { BubbleTag } from '@/components/custom/BubbleTag';
import SectionHeaderWithRedFlags from '@/components/custom/SectionHeaderWithRedFlags';

const DeviceSection: FC = () => {
  const { devices } = customerData.digitalFootprint;

  return (
    <section className="space-y-2">
      <SectionHeaderWithRedFlags 
        redFlags={[]}
        title={`Devices (${devices.total} total, ${devices.active} active)`}
        icon={Smartphone}
        iconColorClass="text-blue-600"
      />
      <CustomCard>
        <div className="divide-y">
          {devices.history.map(device => (
            <div key={device.deviceId} className="p-4 space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <h3 className="font-medium">{device.type}</h3>
                  <span className="text-sm text-gray-500">ID: {device.deviceId}</span>
                </div>
                <BubbleTag
                  text={`${device.loginCount} logins`}
                  color="blue"
                />
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">Browser</p>
                  <p>{device.browser}</p>
                </div>
                <div>
                  <p className="text-gray-500">Operating System</p>
                  <p>{device.os}</p>
                </div>
                <div>
                  <p className="text-gray-500">First Seen</p>
                  <p>{format(new Date(device.firstSeen), 'MMM d, yyyy')}</p>
                </div>
                <div>
                  <p className="text-gray-500">Last Seen</p>
                  <p>{format(new Date(device.lastSeen), 'MMM d, yyyy')}</p>
                </div>
              </div>
              <div className="text-sm">
                <p className="text-gray-500">IP Addresses</p>
                <div className="flex flex-wrap gap-2 mt-1">
                  {device.ipAddresses.map(ip => (
                    <span key={ip} className="px-2 py-1 bg-gray-100 rounded text-xs">
                      {ip}
                    </span>
                  ))}
                </div>
              </div>
              <div className="text-sm">
                <p className="text-gray-500">Locations</p>
                <div className="flex flex-wrap gap-2 mt-1">
                  {device.locations.map(location => (
                    <span key={location} className="px-2 py-1 bg-gray-100 rounded text-xs">
                      {location}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CustomCard>
    </section>
  );
};

const LocationSection: FC = () => {
  const { locations } = customerData.digitalFootprint;

  return (
    <section className="space-y-2">
      <SectionHeaderWithRedFlags 
        redFlags={[]}
        title="Locations"
        icon={MapPin}
        iconColorClass="text-blue-600"
      />
      <CustomCard>
        <div className="p-4 space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium mb-2">Frequent Locations</h4>
              <div className="flex flex-wrap gap-2">
                {locations.frequent.map(location => (
                  <BubbleTag
                    key={location}
                    text={location}
                    color="blue"
                  />
                ))}
              </div>
            </div>
            {locations.unusual.length > 0 && (
              <div>
                <h4 className="text-sm font-medium mb-2">Unusual Locations</h4>
                <div className="flex flex-wrap gap-2">
                  {locations.unusual.map(location => (
                    <BubbleTag
                      key={location}
                      text={location}
                      color="orange"
                      icon={<AlertTriangle className="h-3 w-3" />}
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
          <div className="mt-4">
            <h4 className="text-sm font-medium mb-2">Location History</h4>
            <div className="divide-y">
              {locations.history.map(location => (
                <div key={location.city} className="py-3 first:pt-0 last:pb-0">
                  <div className="flex items-center justify-between">
                    <h5 className="font-medium">{location.city}</h5>
                    <span className="text-sm text-gray-500">{location.visitCount} visits</span>
                  </div>
                  <div className="mt-2 grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500">First Seen</p>
                      <p>{format(new Date(location.firstSeen), 'MMM d, yyyy')}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Last Seen</p>
                      <p>{format(new Date(location.lastSeen), 'MMM d, yyyy')}</p>
                    </div>
                  </div>
                  <div className="mt-2">
                    <p className="text-sm text-gray-500">Devices Used</p>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {location.devices.map(deviceId => (
                        <span key={deviceId} className="px-2 py-1 bg-gray-100 rounded text-xs">
                          {deviceId}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CustomCard>
    </section>
  );
};

const BehaviorSection: FC = () => {
  const { behavior } = customerData.digitalFootprint;

  return (
    <section className="space-y-2">
      <SectionHeaderWithRedFlags 
        redFlags={[]}
        title="Behavioral Patterns"
        icon={Activity}
        iconColorClass="text-blue-600"
      />
      <CustomCard>
        <div className="p-4 space-y-6">
          {/* Login Times */}
          <div>
            <h4 className="text-sm font-medium mb-3">Login Activity Distribution</h4>
            <div className="grid grid-cols-4 gap-4">
              {Object.entries(behavior.loginTimes).map(([time, count]) => (
                <div key={time} className="text-center">
                  <div className="text-2xl font-semibold">{count}</div>
                  <div className="text-sm text-gray-500 capitalize">{time}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Transaction Patterns */}
          <div>
            <h4 className="text-sm font-medium mb-3">Transaction Patterns</h4>
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-semibold">{behavior.transactionPatterns.weekday}</div>
                <div className="text-sm text-gray-500">Weekday Transactions</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-semibold">{behavior.transactionPatterns.weekend}</div>
                <div className="text-sm text-gray-500">Weekend Transactions</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-semibold">₹{behavior.transactionPatterns.average_value.toFixed(2)}</div>
                <div className="text-sm text-gray-500">Average Value</div>
              </div>
            </div>
          </div>

          {/* Risk Indicators */}
          <div>
            <h4 className="text-sm font-medium mb-3">Risk Indicators</h4>
            <div className="grid grid-cols-2 gap-4">
              {Object.entries(behavior.riskIndicators).map(([indicator, value]) => (
                <div key={indicator} className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${value ? 'bg-red-500' : 'bg-green-500'}`} />
                  <span className="text-sm capitalize">{indicator.replace(/_/g, ' ')}</span>
                  <span className={`text-sm ${value ? 'text-red-500' : 'text-green-500'}`}>
                    {value ? 'Detected' : 'Not Detected'}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CustomCard>
    </section>
  );
};

export const CustomerDigitalFootprintTab: FC = () => {
  return (
    <div className="space-y-6">
      <DeviceSection />
      <LocationSection />
      <BehaviorSection />
    </div>
  );
};
