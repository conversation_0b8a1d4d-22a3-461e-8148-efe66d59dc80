import { FC, useState, useEffect, useMemo } from 'react';
import { AlertTriangle } from 'lucide-react';
import RedFlagsRender, { FlagSeverityOption, RedFlag } from '@/components/custom/RedFlagsRender';
import { useCustomerIdStore } from '@/app/store/customer/customerIdStore';
import { useArtifactStore } from '@/app/store/artifact/artifactStore';
import { customerService } from '@/app/services/customerServices';

// Define severity filter options
const severityOptions: FlagSeverityOption[] = [
  { value: 'critical', label: 'Critical', icon: AlertTriangle },
  { value: 'high', label: 'High', icon: AlertTriangle },
  { value: 'medium', label: 'Medium', icon: AlertTriangle },
  { value: 'low', label: 'Low', icon: AlertTriangle }
];

// Dynamic category mapping
const getCategoryInfo = (flagType: string) => {
  const category = flagType.toLowerCase();
  return {
    category,
    label: flagType.replace(/_/g, ' ').split(' ').map((word: string) => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' '),
    icon: AlertTriangle,
    color: 'blue'
  };
};

export const CustomerRedFlagsTab: FC = () => {
  const [flags, setFlags] = useState<RedFlag[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [filterSeverities, setFilterSeverities] = useState<string[]>([]);
  // For type safety, use the store directly
  const customerIdStore = useCustomerIdStore();
  // Access the customer ID from the store
  const selectedCustomerId = customerIdStore.customerIdList.length > 0 
    ? customerIdStore.customerIdList[0].value 
    : null;
  const artifactStore = useArtifactStore();

  useEffect(() => {
    // Fetch customer list if not already loaded
    if (customerIdStore.customerIdList.length === 0 && !customerIdStore.loading) {
      customerIdStore.fetchCustomerIdList();
    }
  }, [customerIdStore]);

  useEffect(() => {
    if (selectedCustomerId) {
      fetchRedFlags();
    } else {
      // Clear flags if no customer ID is selected
      setFlags([]);
    }
  }, [selectedCustomerId]);

  const fetchRedFlags = async () => {
    if (!selectedCustomerId) return;
    
    setIsLoading(true);
    try {
      const data = await customerService.getCustomerRedFlags(selectedCustomerId);
      if (data && Array.isArray(data)) {
        setFlags(data);
      } else {
        console.error('Unexpected API response format');
        setFlags([]);
      }
    } catch (error) {
      console.error('Error fetching red flags:', error);
      setFlags([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    await fetchRedFlags();
  };

  // Get unique categories for dropdown
  const categoryOptions = useMemo(() => {
    const categories = flags.map(flag => flag.flag_type || '');
    const uniqueCategories = Array.from(new Set(categories));
    return uniqueCategories.map(category => ({
      value: category.toLowerCase(),
      label: category.replace(/_/g, ' ').split(' ').map((word: string) => 
        word.charAt(0).toUpperCase() + word.slice(1)
      ).join(' '),
      icon: AlertTriangle
    }));
  }, [flags]);

  return (
    <div className="h-full">
      <RedFlagsRender
        flags={flags}
        severityOptions={severityOptions}
        categoryOptions={categoryOptions}
        onRefresh={handleRefresh}
        isLoading={isLoading}
        getCategoryInfo={getCategoryInfo}
        externalFilterSeverities={filterSeverities}
        setExternalFilterSeverities={setFilterSeverities}
        allowViewModeToggle={false}
        useArtifactTab={true}
        artifactStore={artifactStore}
      />
    </div>
  );
};
