import { FC, useEffect, useState } from 'react';
import { CustomCard } from '@/components/custom/CustomCard';
import { User, CreditCard, AlertTriangle, MapPin, Copy, FileText, Bell, Activity } from 'lucide-react';
import { useActiveContext } from '@/app/layout/ActiveContext/useActiveContext';
import { customerData } from './CustomerSampleData';
import { format } from 'date-fns';
import { BubbleTag } from '@/components/custom/BubbleTag';
import SectionHeaderWithRedFlags from '@/components/custom/SectionHeaderWithRedFlags';
import { RiskAssessmentParagraph } from '@/components/custom/RiskAssessmentParagraph';
import { useCustomerIdStore } from '@/app/store/customer/customerIdStore';
import { KeyMetrics } from '@/components/custom/KeyMetrics';
import { CustomTableView } from '@/components/custom/CustomTableView';
import { InvestigationsSection } from '@/app/pages/Merchant/MerchantInvestigation/Overview/components';
import { formatTimestamp } from '@/utils/timeFormat';
import { ReportableSection } from '@/app/pages/ReportGeneration/utils/ReportSectionHelpers';

// Import SimpleMetric type from KeyMetrics component
type SimpleMetric = {
  label: string;
  value: string | number;
  icon: string;
};

const mapRiskLevel = (riskLevel: string): 'severe' | 'high' | 'medium' | 'low' => {
  const level = riskLevel.toLowerCase();
  if (level.includes('severe')) return 'severe';
  if (level.includes('high')) return 'high';
  if (level.includes('medium')) return 'medium';
  return 'low';
};

// Create a CustomerHeader component similar to MerchantHeader
interface CustomerHeaderProps {
  customerName: string;
  customerId: string;
}

const CustomerHeader: FC<CustomerHeaderProps> = ({ customerName, customerId }) => {
  const copyCustomerId = () => {
    navigator.clipboard.writeText(customerId);
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h2 className="text-xl font-semibold text-blue-600">{customerName}</h2>
          <div className="flex items-center gap-1 text-sm text-gray-500">
            <span>[{customerId}]</span>
            <button 
              onClick={copyCustomerId}
              className="text-blue-500 hover:text-blue-700"
            >
              <Copy className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export const CustomerOverviewTab: FC = () => {
  const { activeContexts, handleSelect } = useActiveContext();
  const { customerIdList, fetchCustomerIdList } = useCustomerIdStore();
  const { overview, investigations } = customerData;
  const [customerName, setCustomerName] = useState<string>('');
  const [isMetricsExpanded, setIsMetricsExpanded] = useState<boolean>(false);
  const [isTableExpanded, setIsTableExpanded] = useState<boolean>(false);
  const [isCreditMetricsExpanded, setIsCreditMetricsExpanded] = useState<boolean>(false);
  const [isCreditEnquiriesExpanded, setIsCreditEnquiriesExpanded] = useState<boolean>(false);
  const [isTradelinesExpanded, setIsTradelinesExpanded] = useState<boolean>(false);
  const [isDocumentsExpanded, setIsDocumentsExpanded] = useState<boolean>(false);

  useEffect(() => {
    fetchCustomerIdList();
  }, [fetchCustomerIdList]);

  useEffect(() => {
    if (activeContexts.customer && customerIdList.length > 0) {
      const customerInfo = customerIdList.find(
        customer => customer.value === activeContexts.customer
      );
      setCustomerName(customerInfo ? customerInfo.label : activeContexts.customer);
    }
  }, [activeContexts.customer, customerIdList]);

  // Check if a customer is selected in the active context
  if (!activeContexts.customer) {
    // No customer selected, return blank page
    return null;
  }

  const getRiskLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'high':
        return 'red';
      case 'medium':
        return 'orange';
      case 'low':
        return 'green';
      default:
        return 'blue';
    }
  };

  // Create hardcoded metrics for Transaction Summary
  const transactionMetrics: SimpleMetric[] = [
    {
      label: "Amount of Txn",
      value: `₹${(overview.transactionSummary?.totalAmount || 0).toLocaleString()}`,
      icon: "CreditCard"
    },
    {
      label: "Num of Txn",
      value: overview.transactionSummary?.numTransactions || overview.totalTransactions,
      icon: "ListIcon"
    },
    {
      label: "Avg Txn Amount",
      value: `₹${(overview.transactionSummary?.avgAmount || overview.avgTransactionValue).toLocaleString()}`,
      icon: "TrendingUp"
    },
    {
      label: "Unique Beneficiaries",
      value: overview.transactionSummary?.uniqueBeneficiaries || 0,
      icon: "Users"
    },
    {
      label: "Cash Withdrawal",
      value: `₹${(overview.transactionSummary?.cashWithdrawal || 0).toLocaleString()}`,
      icon: "Banknote"
    },
    {
      label: "Top Customer City",
      value: overview.transactionSummary?.topCustomerCity || '-',
      icon: "MapPin"
    }
  ];

  // Map and filter investigations
  const investigationItems = investigations ? [...investigations.active, ...investigations.past].map((investigation) => ({
    id: investigation.investigation_id,
    title: investigation.title,
    content: (
      <ReportableSection 
        type="single-investigation" 
        data={{ investigation, customerId: activeContexts.customer }}
      >
        <div className="grid grid-cols-[auto_1fr_auto] gap-4 p-3 cursor-pointer" onClick={() => {
          handleSelect('case', investigation.case_number, investigation.investigation_id);
        }}>
          {/* Column 1: Icon */}
          <div className="flex items-center">
            <AlertTriangle className={`h-4 w-4 ${
              investigation.priority === 'High' ? 'text-red-500' : 'text-yellow-500'
            }`} />
          </div>
          {/* Column 2: Case Info */}
          <div className="space-y-1 min-w-0">
            <span className="text-sm font-medium block truncate">
              Case #{investigation.case_number} - {investigation.title}
            </span>
            <p className="text-sm text-gray-600">{investigation.description}</p>
          </div>
          {/* Column 3: Status & Investigator */}
          <div className="flex flex-col items-end justify-between">
            <div className="flex items-center gap-2">
              <BubbleTag  
                text={investigation.status}
                color="blue"
              />
              <BubbleTag
                text={`${investigation.priority} Priority`}
                color={investigation.priority === 'High' ? 'red' : 'yellow'}
              />
              <span className="text-xs text-gray-500 ml-2">
                {formatTimestamp(investigation.created_at, 'relative')}      
              </span>
            </div>
            <span className="text-xs text-gray-500 mt-2">
              Investigator: {investigation.assignee_Name}
            </span>
          </div>
        </div>
      </ReportableSection>
    ),
    metadata: {
      caseNumber: investigation.case_number,
      description: investigation.description,
      title: investigation.title,
      status: investigation.status,
      priority: investigation.priority,
      assignee: investigation.assignee_Name,
      lastUpdated: investigation.created_at,
      channel: 'Direct',
      type: 'Compliance',
      id: investigation.investigation_id
    }
  })) : [];

  const activeInvestigations = investigationItems.filter(
    item => (item.metadata as any).status.toLowerCase() === 'open' || (item.metadata as any).status.toLowerCase() === 'in progress'
  );

  const pastInvestigations = investigationItems.filter(
    item => (item.metadata as any).status.toLowerCase() === 'closed'
  );

  return (
    <div className="space-y-4">
      <CustomerHeader 
        customerName={customerName} 
        customerId={activeContexts.customer}
      />
      
      <SectionHeaderWithRedFlags 
        redFlags={[]}
        title="Customer Overview"
        icon={User}
        iconColorClass="text-blue-600"
      />

      {/* Basic Information */}
      <div className="space-y-6">
        <div className="grid grid-cols-2 gap-4 mt-2">
          {/* Contact Information */}
          <div className="bg-white rounded-md border border-gray-100 p-4 shadow-sm">
            <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
              <User className="h-4 w-4 text-blue-600 mr-2" />
              Contact Information
            </h3>
            <div className="space-y-2.5">
              <div className="flex items-baseline">
                <p className="text-xs text-gray-500 w-24">Email</p>
                <p className="text-sm font-medium text-gray-800">{overview.email}</p>
              </div>
              <div className="flex items-baseline">
                <p className="text-xs text-gray-500 w-24">Phone</p>
                <p className="text-sm font-medium text-gray-800">{overview.phone}</p>
              </div>
              <div className="flex items-baseline">
                <p className="text-xs text-gray-500 w-24">Address</p>
                <p className="text-sm font-medium text-gray-800">{overview.address}</p>
              </div>
            </div>
          </div>

          {/* Application Details */}
          <div className="bg-white rounded-md border border-gray-100 p-4 shadow-sm">
            <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
              <CreditCard className="h-4 w-4 text-blue-600 mr-2" />
              Application Details
            </h3>
            <div className="space-y-2.5">
              <div className="flex items-baseline">
                <p className="text-xs text-gray-500 w-24">Date</p>
                <p className="text-sm font-medium text-gray-800">{format(new Date(overview.applicationDetails?.date), 'MMM d, yyyy')}</p>
              </div>
              <div className="flex items-baseline">
                <p className="text-xs text-gray-500 w-24">KYC Status</p>
                <div className="flex items-center">
                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                    {overview.applicationDetails?.kycStatus}
                  </span>
                </div>
              </div>
              <div className="flex items-baseline">
                <p className="text-xs text-gray-500 w-24">Onboarding Risk</p>
                <div className="flex items-center">
                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800">
                    {overview.applicationDetails?.onboardingRisk}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Customer Details */}
          <div className="bg-white rounded-md border border-gray-100 p-4 shadow-sm">
            <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
              <User className="h-4 w-4 text-blue-600 mr-2" />
              Customer Details
            </h3>
            <div className="space-y-2.5">
              <div className="flex items-baseline">
                <p className="text-xs text-gray-500 w-24">Occupation</p>
                <p className="text-sm font-medium text-gray-800">{overview.customerDetails?.occupation}</p>
              </div>
              <div className="flex items-baseline">
                <p className="text-xs text-gray-500 w-24">Annual Income</p>
                <p className="text-sm font-medium text-gray-800">{overview.customerDetails?.annualIncome}</p>
              </div>
              <div className="flex items-baseline">
                <p className="text-xs text-gray-500 w-24">Age</p>
                <p className="text-sm font-medium text-gray-800">{overview.customerDetails?.age}</p>
              </div>
            </div>
          </div>

          {/* Account Details */}
          <div className="bg-white rounded-md border border-gray-100 p-4 shadow-sm">
            <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
              <CreditCard className="h-4 w-4 text-blue-600 mr-2" />
              Account Details
            </h3>
            <div className="space-y-2.5">
              <div className="flex items-baseline">
                <p className="text-xs text-gray-500 w-24">Type</p>
                <p className="text-sm font-medium text-gray-800">{overview.accountDetails?.type}</p>
              </div>
              <div className="flex items-baseline">
                <p className="text-xs text-gray-500 w-24">Status</p>
                <div className="flex items-center">
                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                    {overview.accountDetails?.status}
                  </span>
                </div>
              </div>
              <div className="flex items-baseline">
                <p className="text-xs text-gray-500 w-24">Account Since</p>
                <p className="text-sm font-medium text-gray-800">{overview.accountDetails?.accountSince}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <RiskAssessmentParagraph
        title="Customer Risk Summary"
        justification="Savings account opened in October 2024. Mr. Rajesh is based in a village in Rajasthan; declared a yearly income of ₹1L. Multiple red-flags noted; transaction pattern is analogous to mule accounts. Account likely part of a mule community."
        riskLevel={mapRiskLevel(overview.riskLevel)}
      />

      {/* Transaction Summary using KeyMetrics */}
      <section>
        <SectionHeaderWithRedFlags 
          redFlags={[]}
          title="Transaction Summary"
          icon={CreditCard}
          iconColorClass="text-blue-600"
        />
        
        <div className="pt-4">
          <KeyMetrics
            hardcodedMetrics={transactionMetrics}
            isMetricsExpanded={isMetricsExpanded}
            setIsMetricsExpanded={setIsMetricsExpanded}
            title=""
            showHeader={false}
          />
        </div>
        
        {overview.transactionSummary?.lastTransactions && overview.transactionSummary.lastTransactions.length > 0 && (
          <CustomTableView
            className=""
            title="Latest Transactions"
            columns={[
              { key: 'timeDate', header: 'Time' },
              { key: 'type', header: 'Type' },
              { key: 'location', header: 'Location' },
              { key: 'amount', header: 'Amount' },
              { key: 'channel', header: 'Channel' }
            ]}
            data={overview.transactionSummary.lastTransactions.map(transaction => ({
              timeDate: `${transaction.time}, ${transaction.date}`,
              type: transaction.type,
              location: transaction.location,
              amount: `₹${transaction.amount.toLocaleString()}`,
              channel: transaction.channel
            }))}
            initialRowLimit={3}
            isExpanded={isTableExpanded}
            setIsExpanded={setIsTableExpanded}
          />
        )}
      </section>

      {/* Credit Insights Section */}
      <section>
        <SectionHeaderWithRedFlags 
          redFlags={[]}
          title="Credit Insights"
          icon={CreditCard}
          iconColorClass="text-blue-600"
        />
        
        <div className="pt-4">
          <KeyMetrics
            hardcodedMetrics={[
              {
                label: "Active Accounts",
                value: overview.creditInsights?.activeAccounts || 0,
                icon: "CreditCard"
              },
              {
                label: "Loan Types",
                value: overview.creditInsights?.loanTypes || "-",
                icon: "Wallet"
              },
              {
                label: "Loan Ticket Size",
                value: overview.creditInsights?.loanTicketSize || "₹0",
                icon: "TrendingUp"
              },
              {
                label: "Credit Exposure",
                value: overview.creditInsights?.totalCreditExposure || "₹0",
                icon: "DollarSign"
              },
              {
                label: "Payment History",
                value: overview.creditInsights?.paymentHistory || "-",
                icon: "Calendar"
              },
              {
                label: "Credit Age",
                value: overview.creditInsights?.creditAge || "-",
                icon: "Clock"
              },
              {
                label: "Credit Score",
                value: overview.creditInsights?.creditScore || 0,
                icon: "BarChart"
              },
              {
                label: "Recent Enquiries",
                value: overview.creditInsights?.recentEnquiries || 0,
                icon: "Search"
              },
              {
                label: "Linked Devices/IPs",
                value: overview.creditInsights?.linkedDevices || 0,
                icon: "Smartphone"
              }
            ]}
            isMetricsExpanded={isCreditMetricsExpanded}
            setIsMetricsExpanded={setIsCreditMetricsExpanded}
            title="Credit Insights Metrics"
            showHeader={false}
          />
        </div>
        
        {/* Credit Enquiries Table */}
        <div className="mt-4">
          {overview.creditInsights?.creditEnquiries && overview.creditInsights.creditEnquiries.length > 0 && (
            <CustomTableView
              className=""
              title="Credit Enquiries (Last 3 Months)"
              columns={[
                { key: 'enquiryDate', header: 'Enquiry Date' },
                { key: 'institution', header: 'Institution' },
                { key: 'productType', header: 'Product Type' },
                { key: 'enquiryPurpose', header: 'Enquiry Purpose' },
                { key: 'amount', header: 'Amount (INR)' },
                { key: 'outcome', header: 'Outcome' }
              ]}
              data={overview.creditInsights.creditEnquiries.map(enquiry => ({
                enquiryDate: enquiry.enquiryDate,
                institution: enquiry.institution,
                productType: enquiry.productType,
                enquiryPurpose: enquiry.enquiryPurpose,
                amount: enquiry.amount.toLocaleString(),
                outcome: enquiry.outcome
              }))}
              initialRowLimit={4}
              isExpanded={isCreditEnquiriesExpanded}
              setIsExpanded={setIsCreditEnquiriesExpanded}
            />
          )}
        </div>
        
        {/* Tradelines Table */}
        <div className="mt-6">
          {overview.creditInsights?.tradelines && overview.creditInsights.tradelines.length > 0 && (
            <CustomTableView
              className=""
              title="Tradelines (Active + Closed)"
              columns={[
                { key: 'lender', header: 'Lender' },
                { key: 'productType', header: 'Product Type' },
                { key: 'loanStart', header: 'Loan Start' },
                { key: 'status', header: 'Status' },
                { key: 'sanctionedAmount', header: 'Sanctioned Amt (INR)' },
                { key: 'currentBalance', header: 'Current Balance' },
                { key: 'dpd', header: 'DPD (Days Past Due)' }
              ]}
              data={overview.creditInsights.tradelines.map(tradeline => ({
                lender: tradeline.lender,
                productType: tradeline.productType,
                loanStart: tradeline.loanStart,
                status: tradeline.status,
                sanctionedAmount: tradeline.sanctionedAmount.toLocaleString(),
                currentBalance: tradeline.currentBalance.toLocaleString(),
                dpd: tradeline.dpd
              }))}
              initialRowLimit={4}
              isExpanded={isTradelinesExpanded}
              setIsExpanded={setIsTradelinesExpanded}
            />
          )}
        </div>
      </section>

      {/* Uploaded Documents Section */}
      <section>
        <SectionHeaderWithRedFlags 
          redFlags={[]}
          title="Uploaded Documents"
          icon={FileText}
          iconColorClass="text-blue-600"
        />
        
        <div className="mt-4">
          {overview.documents && overview.documents.length > 0 && (
            <CustomTableView
              className=""
              title="Customer Documents"
              columns={[
                { key: 'name', header: 'Document Name' },
                { key: 'category', header: 'Category' },
                { key: 'type', header: 'Type' },
                { key: 'uploadDate', header: 'Upload Date' },
                { key: 'status', header: 'Status' },
                { key: 'fileType', header: 'File Type' },
                { key: 'fileSize', header: 'File Size' }
              ]}
              data={overview.documents.map(doc => ({
                name: doc.name,
                category: doc.category,
                type: doc.type,
                uploadDate: doc.uploadDate,
                status: doc.status,
                fileType: doc.fileType,
                fileSize: doc.fileSize
              }))}
              initialRowLimit={3}
              isExpanded={isDocumentsExpanded}
              setIsExpanded={setIsDocumentsExpanded}
            />
          )}
        </div>
      </section>

      {/* Investigations Sections */}
      <section className="space-y-3">
        <InvestigationsSection 
          investigations={activeInvestigations}
          title="Active Investigations"
          icon={Bell}
          iconColorClass="text-orange-500"
        />

        <InvestigationsSection 
          investigations={pastInvestigations}
          title="Past Investigations"
          icon={FileText}
          iconColorClass="text-teal-600"
        />
      </section>
    </div>
  );
};
