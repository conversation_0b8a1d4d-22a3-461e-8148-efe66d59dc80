import { FC } from 'react';
import { Smartphone, Mail, Phone, MapPin, Calendar, Link } from 'lucide-react';
import { CustomCard } from '@/components/custom/CustomCard';
import { customerData } from './CustomerSampleData';
import { format } from 'date-fns';
import SectionHeaderWithRedFlags from '@/components/custom/SectionHeaderWithRedFlags';

interface LinkageItemProps {
  title: string;
  subtitle: string;
  metadata: {
    firstSeen?: string;
    lastSeen?: string;
    verified?: boolean;
    linkedAccounts?: string[];
    sharedWith?: string[];
    [key: string]: any;
  };
  icon: React.ReactNode;
}

const LinkageItem: FC<LinkageItemProps> = ({ title, subtitle, metadata, icon }) => {
  return (
    <div className="p-4 border-b last:border-b-0">
      <div className="flex items-start gap-4">
        <div className="p-2 bg-blue-50 rounded-lg">
          {icon}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <h3 className="font-medium text-sm">{title}</h3>
            {metadata.verified && (
              <span className="text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded">
                Verified
              </span>
            )}
          </div>
          <p className="text-sm text-gray-600 mt-1">{subtitle}</p>
          <div className="mt-2 flex flex-wrap gap-2">
            {metadata.firstSeen && (
              <div className="flex items-center gap-1 text-xs text-gray-500">
                <Calendar className="h-3 w-3" />
                <span>First seen: {format(new Date(metadata.firstSeen), 'MMM d, yyyy')}</span>
              </div>
            )}
            {metadata.lastSeen && (
              <div className="flex items-center gap-1 text-xs text-gray-500">
                <Calendar className="h-3 w-3" />
                <span>Last seen: {format(new Date(metadata.lastSeen), 'MMM d, yyyy')}</span>
              </div>
            )}
          </div>
          {((metadata.linkedAccounts && metadata.linkedAccounts.length > 0) || (metadata.sharedWith && metadata.sharedWith.length > 0)) && (
            <div className="mt-2">
              <div className="flex items-center gap-1 text-xs text-blue-600">
                <Link className="h-3 w-3" />
                {(() => {
                  const totalLinks = (metadata.linkedAccounts?.length || 0) + (metadata.sharedWith?.length || 0);
                  return (
                    <span>
                      Linked with {totalLinks} other {totalLinks === 1 ? 'customer' : 'customers'}
                    </span>
                  );
                })()}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export const CustomerLinkagesTab: FC = () => {
  const { linkages } = customerData;

  return (
    <div className="space-y-6">
      {/* Devices Section */}
      <section>
        <SectionHeaderWithRedFlags 
          redFlags={[]}
          title="Devices"
          icon={Smartphone}
          iconColorClass="text-blue-600"
        />
        <CustomCard>
          {linkages.devices.map(device => (
            <LinkageItem
              key={device.id}
              title={device.type}
              subtitle={`ID: ${device.id} • ${device.ipAddresses.length} IP addresses`}
              metadata={{
                firstSeen: device.firstSeen,
                lastSeen: device.lastSeen,
                sharedWith: device.sharedWith
              }}
              icon={<Smartphone className="h-4 w-4 text-blue-600" />}
            />
          ))}
        </CustomCard>
      </section>

      {/* Emails Section */}
      <section>
        <SectionHeaderWithRedFlags 
          redFlags={[]}
          title="Email Addresses"
          icon={Mail}
          iconColorClass="text-blue-600"
        />
        <CustomCard>
          {linkages.emails.map(email => (
            <LinkageItem
              key={email.email}
              title={email.email}
              subtitle="Email Address"
              metadata={{
                verified: email.verified,
                firstUsed: email.firstUsed,
                lastUsed: email.lastUsed,
                linkedAccounts: email.linkedAccounts
              }}
              icon={<Mail className="h-4 w-4 text-blue-600" />}
            />
          ))}
        </CustomCard>
      </section>

      {/* Phone Numbers Section */}
      <section>
        <SectionHeaderWithRedFlags 
          redFlags={[]}
          title="Phone Numbers"
          icon={Phone}
          iconColorClass="text-blue-600"
        />
        <CustomCard>
          {linkages.phones.map(phone => (
            <LinkageItem
              key={phone.number}
              title={phone.number}
              subtitle="Phone Number"
              metadata={{
                verified: phone.verified,
                firstUsed: phone.firstUsed,
                lastUsed: phone.lastUsed,
                linkedAccounts: phone.linkedAccounts
              }}
              icon={<Phone className="h-4 w-4 text-blue-600" />}
            />
          ))}
        </CustomCard>
      </section>

      {/* Addresses Section */}
      <section>
        <SectionHeaderWithRedFlags 
          redFlags={[]}
          title="Addresses"
          icon={MapPin}
          iconColorClass="text-blue-600"
        />
        <CustomCard>
          {linkages.addresses.map(address => (
            <LinkageItem
              key={address.address}
              title={address.address}
              subtitle={address.type}
              metadata={{
                verified: address.verified,
                firstUsed: address.firstUsed,
                lastUsed: address.lastUsed,
                sharedWith: address.sharedWith
              }}
              icon={<MapPin className="h-4 w-4 text-blue-600" />}
            />
          ))}
        </CustomCard>
      </section>
    </div>
  );
};
