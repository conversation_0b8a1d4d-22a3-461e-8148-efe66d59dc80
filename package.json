{"name": "merchant-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@codemirror/lang-sql": "^6.8.0", "@headlessui/react": "^2.2.0", "@hookform/resolvers": "^3.9.1", "@monaco-editor/react": "^4.6.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.4", "@react-pdf/renderer": "^4.1.6", "@types/papaparse": "^5.3.15", "@types/react-window": "^1.8.8", "@uiw/codemirror-theme-vscode": "^4.23.12", "@uiw/react-codemirror": "^4.23.12", "axios": "^1.7.9", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "framer-motion": "^11.15.0", "googleapis": "^144.0.0", "groq-sdk": "^0.9.0", "html2canvas": "^1.4.1", "imapflow": "^1.0.171", "jsonwebtoken": "^9.0.2", "jspdf": "^2.5.2", "lucide-react": "^0.462.0", "mailparser": "^3.7.2", "next": "15.0.3", "next-auth": "^4.24.11", "nodemailer": "^6.9.16", "papaparse": "^5.4.1", "react": "^18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-grid-layout": "^1.5.1", "react-hook-form": "^7.54.2", "react-markdown": "^9.0.3", "react-syntax-highlighter": "^15.6.1", "react-window": "^1.8.11", "recharts": "^2.14.1", "remark-gfm": "^4.0.0", "sql-formatter": "^15.6.2", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.0", "vis-data": "^7.1.9", "vis-network": "^9.1.9", "zod": "^3.24.1", "zustand": "^5.0.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-grid-layout": "^1.3.5", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "eslint": "^8", "eslint-config-next": "15.0.3", "eslint-config-prettier": "^10.1.2", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}